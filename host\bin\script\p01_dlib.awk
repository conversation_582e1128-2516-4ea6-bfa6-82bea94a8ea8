BEGIN	{
	Count = 0
	Flag = 0
	ModuleName = ""
	FS="[[:space:]+]"
	print "#include <sysClb.h>\n\n"
	print ".text\n\n"
}

/###dlib###/{
	ModuleName = $2
	printf "LLDR_CALLLIB_MODDEF(%s,%c%s%c)\n\n", ModuleName, 34, <PERSON>mg<PERSON>ame, 34
}

{
	if( Flag == 1 )
	{
		i = 1
		while( i <= NF )
		{
			if( $i != "" )
			{
				printf "LLDR_CALLLIB_FUNDEF(%s,%s,%d)\n", ModuleName, $i, Count 
				Count++
				break
			}
			i++
		}
	}
}

/EXPORTS/	{ Flag = 1 }

END	{
	print "\n.end\n"
}


