{"schema": "xilinx.com:schema:json_instance:1.0", "ip_inst": {"xci_name": "bd_2aab_m17e_0", "cell_name": "m17_exit", "component_reference": "xilinx.com:ip:sc_exit:1.0", "ip_revision": "16", "gen_directory": ".", "parameters": {"component_parameters": {"READ_WRITE_MODE": [{"value": "READ_WRITE", "value_src": "user", "resolve_type": "user", "usage": "all"}], "ENABLE_PIPELINING": [{"value": "0", "value_src": "user", "resolve_type": "user", "format": "bitString", "usage": "all"}], "IS_CASCADED": [{"value": "0", "value_src": "user", "resolve_type": "user", "format": "long", "usage": "all"}], "SSC_ROUTE_WIDTH": [{"value": "3", "value_src": "user", "resolve_type": "user", "format": "long", "usage": "all"}], "MEP_IDENTIFIER_WIDTH": [{"value": "1", "value_src": "user", "resolve_type": "user", "format": "long", "usage": "all"}], "RDATA_WIDTH": [{"value": "32", "value_src": "user", "resolve_type": "user", "format": "long", "usage": "all"}], "WDATA_WIDTH": [{"value": "32", "value_src": "user", "resolve_type": "user", "format": "long", "usage": "all"}], "M_RUSER_BITS_PER_BYTE": [{"value": "0", "resolve_type": "user", "format": "long", "enabled": false, "usage": "all"}], "M_WUSER_BITS_PER_BYTE": [{"value": "0", "resolve_type": "user", "format": "long", "enabled": false, "usage": "all"}], "MAX_RUSER_BITS_PER_BYTE": [{"value": "0", "resolve_type": "user", "format": "long", "usage": "all"}], "MAX_WUSER_BITS_PER_BYTE": [{"value": "0", "resolve_type": "user", "format": "long", "usage": "all"}], "M_ARUSER_WIDTH": [{"value": "0", "resolve_type": "user", "format": "long", "enabled": false, "usage": "all"}], "M_AWUSER_WIDTH": [{"value": "0", "resolve_type": "user", "format": "long", "enabled": false, "usage": "all"}], "M_RUSER_WIDTH": [{"value": "0", "resolve_type": "user", "format": "long", "enabled": false, "usage": "all"}], "M_WUSER_WIDTH": [{"value": "0", "resolve_type": "user", "format": "long", "enabled": false, "usage": "all"}], "M_BUSER_WIDTH": [{"value": "0", "resolve_type": "user", "format": "long", "usage": "all"}], "S_ID_WIDTH": [{"value": "3", "value_src": "user", "resolve_type": "user", "format": "long", "usage": "all"}], "M_ID_WIDTH": [{"value": "0", "resolve_type": "user", "format": "long", "enabled": false, "usage": "all"}], "NUM_WRITE_THREADS": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "NUM_READ_THREADS": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "M_MAX_BURST_LENGTH": [{"value": "0", "value_src": "user", "resolve_type": "user", "format": "long", "usage": "all"}], "ADDR_WIDTH": [{"value": "5", "value_src": "user", "resolve_type": "user", "format": "long", "usage": "all"}], "M_PROTOCOL": [{"value": "AXI4LITE", "value_src": "user", "resolve_type": "user", "usage": "all"}], "HAS_LOCK": [{"value": "0", "resolve_type": "user", "format": "long", "enabled": false, "usage": "all"}], "HAS_BURST": [{"value": "0", "value_src": "user", "resolve_type": "user", "format": "long", "usage": "all"}], "HAS_ACLKEN": [{"value": "0", "resolve_type": "user", "format": "long", "usage": "all"}], "NUM_MSC": [{"value": "20", "value_src": "user", "resolve_type": "user", "format": "long", "usage": "all"}], "READ_ACCEPTANCE": [{"value": "32", "resolve_type": "user", "format": "long", "usage": "all"}], "WRITE_ACCEPTANCE": [{"value": "32", "resolve_type": "user", "format": "long", "usage": "all"}], "NUM_WRITE_OUTSTANDING": [{"value": "8", "value_src": "user", "resolve_type": "user", "format": "long", "usage": "all"}], "NUM_READ_OUTSTANDING": [{"value": "8", "value_src": "user", "resolve_type": "user", "format": "long", "usage": "all"}], "SINGLE_ISSUING": [{"value": "1", "value_src": "user", "resolve_type": "user", "format": "long", "usage": "all"}], "SSC000_ROUTE": [{"value": "0b101", "value_src": "user", "resolve_type": "user", "format": "bitString", "usage": "all"}], "SSC001_ROUTE": [{"value": "0b111", "value_src": "user", "resolve_type": "user", "format": "bitString", "usage": "all"}], "SSC002_ROUTE": [{"value": "001", "resolve_type": "user", "format": "bitString", "usage": "all"}], "SSC003_ROUTE": [{"value": "001", "resolve_type": "user", "format": "bitString", "usage": "all"}], "SSC004_ROUTE": [{"value": "001", "resolve_type": "user", "format": "bitString", "usage": "all"}], "SSC005_ROUTE": [{"value": "001", "resolve_type": "user", "format": "bitString", "usage": "all"}], "SSC006_ROUTE": [{"value": "001", "resolve_type": "user", "format": "bitString", "usage": "all"}], "SSC007_ROUTE": [{"value": "001", "resolve_type": "user", "format": "bitString", "usage": "all"}], "SSC008_ROUTE": [{"value": "001", "resolve_type": "user", "format": "bitString", "usage": "all"}], "SSC009_ROUTE": [{"value": "001", "resolve_type": "user", "format": "bitString", "usage": "all"}], "SSC010_ROUTE": [{"value": "001", "resolve_type": "user", "format": "bitString", "usage": "all"}], "SSC011_ROUTE": [{"value": "001", "resolve_type": "user", "format": "bitString", "usage": "all"}], "SSC012_ROUTE": [{"value": "001", "resolve_type": "user", "format": "bitString", "usage": "all"}], "SSC013_ROUTE": [{"value": "001", "resolve_type": "user", "format": "bitString", "usage": "all"}], "SSC014_ROUTE": [{"value": "001", "resolve_type": "user", "format": "bitString", "usage": "all"}], "SSC015_ROUTE": [{"value": "001", "resolve_type": "user", "format": "bitString", "usage": "all"}], "SSC016_ROUTE": [{"value": "001", "resolve_type": "user", "format": "bitString", "usage": "all"}], "SSC017_ROUTE": [{"value": "001", "resolve_type": "user", "format": "bitString", "usage": "all"}], "SSC018_ROUTE": [{"value": "001", "resolve_type": "user", "format": "bitString", "usage": "all"}], "SSC019_ROUTE": [{"value": "001", "resolve_type": "user", "format": "bitString", "usage": "all"}], "SSC020_ROUTE": [{"value": "001", "resolve_type": "user", "format": "bitString", "usage": "all"}], "SSC021_ROUTE": [{"value": "001", "resolve_type": "user", "format": "bitString", "usage": "all"}], "SSC022_ROUTE": [{"value": "001", "resolve_type": "user", "format": "bitString", "usage": "all"}], "SSC023_ROUTE": [{"value": "001", "resolve_type": "user", "format": "bitString", "usage": "all"}], "SSC024_ROUTE": [{"value": "001", "resolve_type": "user", "format": "bitString", "usage": "all"}], "SSC025_ROUTE": [{"value": "001", "resolve_type": "user", "format": "bitString", "usage": "all"}], "SSC026_ROUTE": [{"value": "001", "resolve_type": "user", "format": "bitString", "usage": "all"}], "SSC027_ROUTE": [{"value": "001", "resolve_type": "user", "format": "bitString", "usage": "all"}], "SSC028_ROUTE": [{"value": "001", "resolve_type": "user", "format": "bitString", "usage": "all"}], "SSC029_ROUTE": [{"value": "001", "resolve_type": "user", "format": "bitString", "usage": "all"}], "SSC030_ROUTE": [{"value": "001", "resolve_type": "user", "format": "bitString", "usage": "all"}], "SSC031_ROUTE": [{"value": "001", "resolve_type": "user", "format": "bitString", "usage": "all"}], "SSC032_ROUTE": [{"value": "001", "resolve_type": "user", "format": "bitString", "usage": "all"}], "SSC033_ROUTE": [{"value": "001", "resolve_type": "user", "format": "bitString", "usage": "all"}], "SSC034_ROUTE": [{"value": "001", "resolve_type": "user", "format": "bitString", "usage": "all"}], "SSC035_ROUTE": [{"value": "001", "resolve_type": "user", "format": "bitString", "usage": "all"}], "SSC036_ROUTE": [{"value": "001", "resolve_type": "user", "format": "bitString", "usage": "all"}], "SSC037_ROUTE": [{"value": "001", "resolve_type": "user", "format": "bitString", "usage": "all"}], "SSC038_ROUTE": [{"value": "001", "resolve_type": "user", "format": "bitString", "usage": "all"}], "SSC039_ROUTE": [{"value": "001", "resolve_type": "user", "format": "bitString", "usage": "all"}], "SSC040_ROUTE": [{"value": "001", "resolve_type": "user", "format": "bitString", "usage": "all"}], "SSC041_ROUTE": [{"value": "001", "resolve_type": "user", "format": "bitString", "usage": "all"}], "SSC042_ROUTE": [{"value": "001", "resolve_type": "user", "format": "bitString", "usage": "all"}], "SSC043_ROUTE": [{"value": "001", "resolve_type": "user", "format": "bitString", "usage": "all"}], "SSC044_ROUTE": [{"value": "001", "resolve_type": "user", "format": "bitString", "usage": "all"}], "SSC045_ROUTE": [{"value": "001", "resolve_type": "user", "format": "bitString", "usage": "all"}], "SSC046_ROUTE": [{"value": "001", "resolve_type": "user", "format": "bitString", "usage": "all"}], "SSC047_ROUTE": [{"value": "001", "resolve_type": "user", "format": "bitString", "usage": "all"}], "SSC048_ROUTE": [{"value": "001", "resolve_type": "user", "format": "bitString", "usage": "all"}], "SSC049_ROUTE": [{"value": "001", "resolve_type": "user", "format": "bitString", "usage": "all"}], "SSC050_ROUTE": [{"value": "001", "resolve_type": "user", "format": "bitString", "usage": "all"}], "SSC051_ROUTE": [{"value": "001", "resolve_type": "user", "format": "bitString", "usage": "all"}], "SSC052_ROUTE": [{"value": "001", "resolve_type": "user", "format": "bitString", "usage": "all"}], "SSC053_ROUTE": [{"value": "001", "resolve_type": "user", "format": "bitString", "usage": "all"}], "SSC054_ROUTE": [{"value": "001", "resolve_type": "user", "format": "bitString", "usage": "all"}], "SSC055_ROUTE": [{"value": "001", "resolve_type": "user", "format": "bitString", "usage": "all"}], "SSC056_ROUTE": [{"value": "001", "resolve_type": "user", "format": "bitString", "usage": "all"}], "SSC057_ROUTE": [{"value": "001", "resolve_type": "user", "format": "bitString", "usage": "all"}], "SSC058_ROUTE": [{"value": "001", "resolve_type": "user", "format": "bitString", "usage": "all"}], "SSC059_ROUTE": [{"value": "001", "resolve_type": "user", "format": "bitString", "usage": "all"}], "SSC060_ROUTE": [{"value": "001", "resolve_type": "user", "format": "bitString", "usage": "all"}], "SSC061_ROUTE": [{"value": "001", "resolve_type": "user", "format": "bitString", "usage": "all"}], "SSC062_ROUTE": [{"value": "001", "resolve_type": "user", "format": "bitString", "usage": "all"}], "SSC063_ROUTE": [{"value": "001", "resolve_type": "user", "format": "bitString", "usage": "all"}], "SSC064_ROUTE": [{"value": "001", "resolve_type": "user", "format": "bitString", "usage": "all"}], "SSC065_ROUTE": [{"value": "001", "resolve_type": "user", "format": "bitString", "usage": "all"}], "SSC066_ROUTE": [{"value": "001", "resolve_type": "user", "format": "bitString", "usage": "all"}], "SSC067_ROUTE": [{"value": "001", "resolve_type": "user", "format": "bitString", "usage": "all"}], "SSC068_ROUTE": [{"value": "001", "resolve_type": "user", "format": "bitString", "usage": "all"}], "SSC069_ROUTE": [{"value": "001", "resolve_type": "user", "format": "bitString", "usage": "all"}], "SSC070_ROUTE": [{"value": "001", "resolve_type": "user", "format": "bitString", "usage": "all"}], "SSC071_ROUTE": [{"value": "001", "resolve_type": "user", "format": "bitString", "usage": "all"}], "SSC072_ROUTE": [{"value": "001", "resolve_type": "user", "format": "bitString", "usage": "all"}], "SSC073_ROUTE": [{"value": "001", "resolve_type": "user", "format": "bitString", "usage": "all"}], "SSC074_ROUTE": [{"value": "001", "resolve_type": "user", "format": "bitString", "usage": "all"}], "SSC075_ROUTE": [{"value": "001", "resolve_type": "user", "format": "bitString", "usage": "all"}], "SSC076_ROUTE": [{"value": "001", "resolve_type": "user", "format": "bitString", "usage": "all"}], "SSC077_ROUTE": [{"value": "001", "resolve_type": "user", "format": "bitString", "usage": "all"}], "SSC078_ROUTE": [{"value": "001", "resolve_type": "user", "format": "bitString", "usage": "all"}], "SSC079_ROUTE": [{"value": "001", "resolve_type": "user", "format": "bitString", "usage": "all"}], "SSC080_ROUTE": [{"value": "001", "resolve_type": "user", "format": "bitString", "usage": "all"}], "SSC081_ROUTE": [{"value": "001", "resolve_type": "user", "format": "bitString", "usage": "all"}], "SSC082_ROUTE": [{"value": "001", "resolve_type": "user", "format": "bitString", "usage": "all"}], "SSC083_ROUTE": [{"value": "001", "resolve_type": "user", "format": "bitString", "usage": "all"}], "SSC084_ROUTE": [{"value": "001", "resolve_type": "user", "format": "bitString", "usage": "all"}], "SSC085_ROUTE": [{"value": "001", "resolve_type": "user", "format": "bitString", "usage": "all"}], "SSC086_ROUTE": [{"value": "001", "resolve_type": "user", "format": "bitString", "usage": "all"}], "SSC087_ROUTE": [{"value": "001", "resolve_type": "user", "format": "bitString", "usage": "all"}], "SSC088_ROUTE": [{"value": "001", "resolve_type": "user", "format": "bitString", "usage": "all"}], "SSC089_ROUTE": [{"value": "001", "resolve_type": "user", "format": "bitString", "usage": "all"}], "SSC090_ROUTE": [{"value": "001", "resolve_type": "user", "format": "bitString", "usage": "all"}], "SSC091_ROUTE": [{"value": "001", "resolve_type": "user", "format": "bitString", "usage": "all"}], "SSC092_ROUTE": [{"value": "001", "resolve_type": "user", "format": "bitString", "usage": "all"}], "SSC093_ROUTE": [{"value": "001", "resolve_type": "user", "format": "bitString", "usage": "all"}], "SSC094_ROUTE": [{"value": "001", "resolve_type": "user", "format": "bitString", "usage": "all"}], "SSC095_ROUTE": [{"value": "001", "resolve_type": "user", "format": "bitString", "usage": "all"}], "SSC096_ROUTE": [{"value": "001", "resolve_type": "user", "format": "bitString", "usage": "all"}], "SSC097_ROUTE": [{"value": "001", "resolve_type": "user", "format": "bitString", "usage": "all"}], "SSC098_ROUTE": [{"value": "001", "resolve_type": "user", "format": "bitString", "usage": "all"}], "SSC099_ROUTE": [{"value": "001", "resolve_type": "user", "format": "bitString", "usage": "all"}], "SSC100_ROUTE": [{"value": "001", "resolve_type": "user", "format": "bitString", "usage": "all"}], "SSC101_ROUTE": [{"value": "001", "resolve_type": "user", "format": "bitString", "usage": "all"}], "SSC102_ROUTE": [{"value": "001", "resolve_type": "user", "format": "bitString", "usage": "all"}], "SSC103_ROUTE": [{"value": "001", "resolve_type": "user", "format": "bitString", "usage": "all"}], "SSC104_ROUTE": [{"value": "001", "resolve_type": "user", "format": "bitString", "usage": "all"}], "SSC105_ROUTE": [{"value": "001", "resolve_type": "user", "format": "bitString", "usage": "all"}], "SSC106_ROUTE": [{"value": "001", "resolve_type": "user", "format": "bitString", "usage": "all"}], "SSC107_ROUTE": [{"value": "001", "resolve_type": "user", "format": "bitString", "usage": "all"}], "SSC108_ROUTE": [{"value": "001", "resolve_type": "user", "format": "bitString", "usage": "all"}], "SSC109_ROUTE": [{"value": "001", "resolve_type": "user", "format": "bitString", "usage": "all"}], "SSC110_ROUTE": [{"value": "001", "resolve_type": "user", "format": "bitString", "usage": "all"}], "SSC111_ROUTE": [{"value": "001", "resolve_type": "user", "format": "bitString", "usage": "all"}], "SSC112_ROUTE": [{"value": "001", "resolve_type": "user", "format": "bitString", "usage": "all"}], "SSC113_ROUTE": [{"value": "001", "resolve_type": "user", "format": "bitString", "usage": "all"}], "SSC114_ROUTE": [{"value": "001", "resolve_type": "user", "format": "bitString", "usage": "all"}], "SSC115_ROUTE": [{"value": "001", "resolve_type": "user", "format": "bitString", "usage": "all"}], "SSC116_ROUTE": [{"value": "001", "resolve_type": "user", "format": "bitString", "usage": "all"}], "SSC117_ROUTE": [{"value": "001", "resolve_type": "user", "format": "bitString", "usage": "all"}], "SSC118_ROUTE": [{"value": "001", "resolve_type": "user", "format": "bitString", "usage": "all"}], "SSC119_ROUTE": [{"value": "001", "resolve_type": "user", "format": "bitString", "usage": "all"}], "SSC120_ROUTE": [{"value": "001", "resolve_type": "user", "format": "bitString", "usage": "all"}], "SSC121_ROUTE": [{"value": "001", "resolve_type": "user", "format": "bitString", "usage": "all"}], "SSC122_ROUTE": [{"value": "001", "resolve_type": "user", "format": "bitString", "usage": "all"}], "SSC123_ROUTE": [{"value": "001", "resolve_type": "user", "format": "bitString", "usage": "all"}], "SSC124_ROUTE": [{"value": "001", "resolve_type": "user", "format": "bitString", "usage": "all"}], "SSC125_ROUTE": [{"value": "001", "resolve_type": "user", "format": "bitString", "usage": "all"}], "SSC126_ROUTE": [{"value": "001", "resolve_type": "user", "format": "bitString", "usage": "all"}], "SSC127_ROUTE": [{"value": "001", "resolve_type": "user", "format": "bitString", "usage": "all"}], "SSC128_ROUTE": [{"value": "001", "resolve_type": "user", "format": "bitString", "usage": "all"}], "SSC129_ROUTE": [{"value": "001", "resolve_type": "user", "format": "bitString", "usage": "all"}], "SSC130_ROUTE": [{"value": "001", "resolve_type": "user", "format": "bitString", "usage": "all"}], "SSC131_ROUTE": [{"value": "001", "resolve_type": "user", "format": "bitString", "usage": "all"}], "SSC132_ROUTE": [{"value": "001", "resolve_type": "user", "format": "bitString", "usage": "all"}], "SSC133_ROUTE": [{"value": "001", "resolve_type": "user", "format": "bitString", "usage": "all"}], "SSC134_ROUTE": [{"value": "001", "resolve_type": "user", "format": "bitString", "usage": "all"}], "SSC135_ROUTE": [{"value": "001", "resolve_type": "user", "format": "bitString", "usage": "all"}], "SSC136_ROUTE": [{"value": "001", "resolve_type": "user", "format": "bitString", "usage": "all"}], "SSC137_ROUTE": [{"value": "001", "resolve_type": "user", "format": "bitString", "usage": "all"}], "SSC138_ROUTE": [{"value": "001", "resolve_type": "user", "format": "bitString", "usage": "all"}], "SSC139_ROUTE": [{"value": "001", "resolve_type": "user", "format": "bitString", "usage": "all"}], "SSC140_ROUTE": [{"value": "001", "resolve_type": "user", "format": "bitString", "usage": "all"}], "SSC141_ROUTE": [{"value": "001", "resolve_type": "user", "format": "bitString", "usage": "all"}], "SSC142_ROUTE": [{"value": "001", "resolve_type": "user", "format": "bitString", "usage": "all"}], "SSC143_ROUTE": [{"value": "001", "resolve_type": "user", "format": "bitString", "usage": "all"}], "SSC144_ROUTE": [{"value": "001", "resolve_type": "user", "format": "bitString", "usage": "all"}], "SSC145_ROUTE": [{"value": "001", "resolve_type": "user", "format": "bitString", "usage": "all"}], "SSC146_ROUTE": [{"value": "001", "resolve_type": "user", "format": "bitString", "usage": "all"}], "SSC147_ROUTE": [{"value": "001", "resolve_type": "user", "format": "bitString", "usage": "all"}], "SSC148_ROUTE": [{"value": "001", "resolve_type": "user", "format": "bitString", "usage": "all"}], "SSC149_ROUTE": [{"value": "001", "resolve_type": "user", "format": "bitString", "usage": "all"}], "SSC150_ROUTE": [{"value": "001", "resolve_type": "user", "format": "bitString", "usage": "all"}], "SSC151_ROUTE": [{"value": "001", "resolve_type": "user", "format": "bitString", "usage": "all"}], "SSC152_ROUTE": [{"value": "001", "resolve_type": "user", "format": "bitString", "usage": "all"}], "SSC153_ROUTE": [{"value": "001", "resolve_type": "user", "format": "bitString", "usage": "all"}], "SSC154_ROUTE": [{"value": "001", "resolve_type": "user", "format": "bitString", "usage": "all"}], "SSC155_ROUTE": [{"value": "001", "resolve_type": "user", "format": "bitString", "usage": "all"}], "SSC156_ROUTE": [{"value": "001", "resolve_type": "user", "format": "bitString", "usage": "all"}], "SSC157_ROUTE": [{"value": "001", "resolve_type": "user", "format": "bitString", "usage": "all"}], "SSC158_ROUTE": [{"value": "001", "resolve_type": "user", "format": "bitString", "usage": "all"}], "SSC159_ROUTE": [{"value": "001", "resolve_type": "user", "format": "bitString", "usage": "all"}], "SSC160_ROUTE": [{"value": "001", "resolve_type": "user", "format": "bitString", "usage": "all"}], "SSC161_ROUTE": [{"value": "001", "resolve_type": "user", "format": "bitString", "usage": "all"}], "SSC162_ROUTE": [{"value": "001", "resolve_type": "user", "format": "bitString", "usage": "all"}], "SSC163_ROUTE": [{"value": "001", "resolve_type": "user", "format": "bitString", "usage": "all"}], "SSC164_ROUTE": [{"value": "001", "resolve_type": "user", "format": "bitString", "usage": "all"}], "SSC165_ROUTE": [{"value": "001", "resolve_type": "user", "format": "bitString", "usage": "all"}], "SSC166_ROUTE": [{"value": "001", "resolve_type": "user", "format": "bitString", "usage": "all"}], "SSC167_ROUTE": [{"value": "001", "resolve_type": "user", "format": "bitString", "usage": "all"}], "SSC168_ROUTE": [{"value": "001", "resolve_type": "user", "format": "bitString", "usage": "all"}], "SSC169_ROUTE": [{"value": "001", "resolve_type": "user", "format": "bitString", "usage": "all"}], "SSC170_ROUTE": [{"value": "001", "resolve_type": "user", "format": "bitString", "usage": "all"}], "SSC171_ROUTE": [{"value": "001", "resolve_type": "user", "format": "bitString", "usage": "all"}], "SSC172_ROUTE": [{"value": "001", "resolve_type": "user", "format": "bitString", "usage": "all"}], "SSC173_ROUTE": [{"value": "001", "resolve_type": "user", "format": "bitString", "usage": "all"}], "SSC174_ROUTE": [{"value": "001", "resolve_type": "user", "format": "bitString", "usage": "all"}], "SSC175_ROUTE": [{"value": "001", "resolve_type": "user", "format": "bitString", "usage": "all"}], "SSC176_ROUTE": [{"value": "001", "resolve_type": "user", "format": "bitString", "usage": "all"}], "SSC177_ROUTE": [{"value": "001", "resolve_type": "user", "format": "bitString", "usage": "all"}], "SSC178_ROUTE": [{"value": "001", "resolve_type": "user", "format": "bitString", "usage": "all"}], "SSC179_ROUTE": [{"value": "001", "resolve_type": "user", "format": "bitString", "usage": "all"}], "SSC180_ROUTE": [{"value": "001", "resolve_type": "user", "format": "bitString", "usage": "all"}], "SSC181_ROUTE": [{"value": "001", "resolve_type": "user", "format": "bitString", "usage": "all"}], "SSC182_ROUTE": [{"value": "001", "resolve_type": "user", "format": "bitString", "usage": "all"}], "SSC183_ROUTE": [{"value": "001", "resolve_type": "user", "format": "bitString", "usage": "all"}], "SSC184_ROUTE": [{"value": "001", "resolve_type": "user", "format": "bitString", "usage": "all"}], "SSC185_ROUTE": [{"value": "001", "resolve_type": "user", "format": "bitString", "usage": "all"}], "SSC186_ROUTE": [{"value": "001", "resolve_type": "user", "format": "bitString", "usage": "all"}], "SSC187_ROUTE": [{"value": "001", "resolve_type": "user", "format": "bitString", "usage": "all"}], "SSC188_ROUTE": [{"value": "001", "resolve_type": "user", "format": "bitString", "usage": "all"}], "SSC189_ROUTE": [{"value": "001", "resolve_type": "user", "format": "bitString", "usage": "all"}], "SSC190_ROUTE": [{"value": "001", "resolve_type": "user", "format": "bitString", "usage": "all"}], "SSC191_ROUTE": [{"value": "001", "resolve_type": "user", "format": "bitString", "usage": "all"}], "SSC192_ROUTE": [{"value": "001", "resolve_type": "user", "format": "bitString", "usage": "all"}], "SSC193_ROUTE": [{"value": "001", "resolve_type": "user", "format": "bitString", "usage": "all"}], "SSC194_ROUTE": [{"value": "001", "resolve_type": "user", "format": "bitString", "usage": "all"}], "SSC195_ROUTE": [{"value": "001", "resolve_type": "user", "format": "bitString", "usage": "all"}], "SSC196_ROUTE": [{"value": "001", "resolve_type": "user", "format": "bitString", "usage": "all"}], "SSC197_ROUTE": [{"value": "001", "resolve_type": "user", "format": "bitString", "usage": "all"}], "SSC198_ROUTE": [{"value": "001", "resolve_type": "user", "format": "bitString", "usage": "all"}], "SSC199_ROUTE": [{"value": "001", "resolve_type": "user", "format": "bitString", "usage": "all"}], "SSC200_ROUTE": [{"value": "001", "resolve_type": "user", "format": "bitString", "usage": "all"}], "SSC201_ROUTE": [{"value": "001", "resolve_type": "user", "format": "bitString", "usage": "all"}], "SSC202_ROUTE": [{"value": "001", "resolve_type": "user", "format": "bitString", "usage": "all"}], "SSC203_ROUTE": [{"value": "001", "resolve_type": "user", "format": "bitString", "usage": "all"}], "SSC204_ROUTE": [{"value": "001", "resolve_type": "user", "format": "bitString", "usage": "all"}], "SSC205_ROUTE": [{"value": "001", "resolve_type": "user", "format": "bitString", "usage": "all"}], "SSC206_ROUTE": [{"value": "001", "resolve_type": "user", "format": "bitString", "usage": "all"}], "SSC207_ROUTE": [{"value": "001", "resolve_type": "user", "format": "bitString", "usage": "all"}], "SSC208_ROUTE": [{"value": "001", "resolve_type": "user", "format": "bitString", "usage": "all"}], "SSC209_ROUTE": [{"value": "001", "resolve_type": "user", "format": "bitString", "usage": "all"}], "SSC210_ROUTE": [{"value": "001", "resolve_type": "user", "format": "bitString", "usage": "all"}], "SSC211_ROUTE": [{"value": "001", "resolve_type": "user", "format": "bitString", "usage": "all"}], "SSC212_ROUTE": [{"value": "001", "resolve_type": "user", "format": "bitString", "usage": "all"}], "SSC213_ROUTE": [{"value": "001", "resolve_type": "user", "format": "bitString", "usage": "all"}], "SSC214_ROUTE": [{"value": "001", "resolve_type": "user", "format": "bitString", "usage": "all"}], "SSC215_ROUTE": [{"value": "001", "resolve_type": "user", "format": "bitString", "usage": "all"}], "SSC216_ROUTE": [{"value": "001", "resolve_type": "user", "format": "bitString", "usage": "all"}], "SSC217_ROUTE": [{"value": "001", "resolve_type": "user", "format": "bitString", "usage": "all"}], "SSC218_ROUTE": [{"value": "001", "resolve_type": "user", "format": "bitString", "usage": "all"}], "SSC219_ROUTE": [{"value": "001", "resolve_type": "user", "format": "bitString", "usage": "all"}], "SSC220_ROUTE": [{"value": "001", "resolve_type": "user", "format": "bitString", "usage": "all"}], "SSC221_ROUTE": [{"value": "001", "resolve_type": "user", "format": "bitString", "usage": "all"}], "SSC222_ROUTE": [{"value": "001", "resolve_type": "user", "format": "bitString", "usage": "all"}], "SSC223_ROUTE": [{"value": "001", "resolve_type": "user", "format": "bitString", "usage": "all"}], "SSC224_ROUTE": [{"value": "001", "resolve_type": "user", "format": "bitString", "usage": "all"}], "SSC225_ROUTE": [{"value": "001", "resolve_type": "user", "format": "bitString", "usage": "all"}], "SSC226_ROUTE": [{"value": "001", "resolve_type": "user", "format": "bitString", "usage": "all"}], "SSC227_ROUTE": [{"value": "001", "resolve_type": "user", "format": "bitString", "usage": "all"}], "SSC228_ROUTE": [{"value": "001", "resolve_type": "user", "format": "bitString", "usage": "all"}], "SSC229_ROUTE": [{"value": "001", "resolve_type": "user", "format": "bitString", "usage": "all"}], "SSC230_ROUTE": [{"value": "001", "resolve_type": "user", "format": "bitString", "usage": "all"}], "SSC231_ROUTE": [{"value": "001", "resolve_type": "user", "format": "bitString", "usage": "all"}], "SSC232_ROUTE": [{"value": "001", "resolve_type": "user", "format": "bitString", "usage": "all"}], "SSC233_ROUTE": [{"value": "001", "resolve_type": "user", "format": "bitString", "usage": "all"}], "SSC234_ROUTE": [{"value": "001", "resolve_type": "user", "format": "bitString", "usage": "all"}], "SSC235_ROUTE": [{"value": "001", "resolve_type": "user", "format": "bitString", "usage": "all"}], "SSC236_ROUTE": [{"value": "001", "resolve_type": "user", "format": "bitString", "usage": "all"}], "SSC237_ROUTE": [{"value": "001", "resolve_type": "user", "format": "bitString", "usage": "all"}], "SSC238_ROUTE": [{"value": "001", "resolve_type": "user", "format": "bitString", "usage": "all"}], "SSC239_ROUTE": [{"value": "001", "resolve_type": "user", "format": "bitString", "usage": "all"}], "SSC240_ROUTE": [{"value": "001", "resolve_type": "user", "format": "bitString", "usage": "all"}], "SSC241_ROUTE": [{"value": "001", "resolve_type": "user", "format": "bitString", "usage": "all"}], "SSC242_ROUTE": [{"value": "001", "resolve_type": "user", "format": "bitString", "usage": "all"}], "SSC243_ROUTE": [{"value": "001", "resolve_type": "user", "format": "bitString", "usage": "all"}], "SSC244_ROUTE": [{"value": "001", "resolve_type": "user", "format": "bitString", "usage": "all"}], "SSC245_ROUTE": [{"value": "001", "resolve_type": "user", "format": "bitString", "usage": "all"}], "SSC246_ROUTE": [{"value": "001", "resolve_type": "user", "format": "bitString", "usage": "all"}], "SSC247_ROUTE": [{"value": "001", "resolve_type": "user", "format": "bitString", "usage": "all"}], "SSC248_ROUTE": [{"value": "001", "resolve_type": "user", "format": "bitString", "usage": "all"}], "SSC249_ROUTE": [{"value": "001", "resolve_type": "user", "format": "bitString", "usage": "all"}], "SSC250_ROUTE": [{"value": "001", "resolve_type": "user", "format": "bitString", "usage": "all"}], "SSC251_ROUTE": [{"value": "001", "resolve_type": "user", "format": "bitString", "usage": "all"}], "SSC252_ROUTE": [{"value": "001", "resolve_type": "user", "format": "bitString", "usage": "all"}], "SSC253_ROUTE": [{"value": "001", "resolve_type": "user", "format": "bitString", "usage": "all"}], "SSC254_ROUTE": [{"value": "001", "resolve_type": "user", "format": "bitString", "usage": "all"}], "SSC255_ROUTE": [{"value": "001", "resolve_type": "user", "format": "bitString", "usage": "all"}], "Component_Name": [{"value": "bd_2aab_m17e_0", "resolve_type": "user", "usage": "all"}]}, "model_parameters": {"C_FAMILY": [{"value": "zynq", "resolve_type": "generated", "usage": "all"}], "C_ENABLE_PIPELINING": [{"value": "0", "resolve_type": "generated", "format": "bitString", "usage": "all"}], "C_IS_CASCADED": [{"value": "0", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_SSC_ROUTE_WIDTH": [{"value": "3", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_MEP_IDENTIFIER_WIDTH": [{"value": "1", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_SSC_ROUTE_ARRAY": [{"value": "0b111101", "resolve_type": "generated", "format": "bitString", "usage": "all"}], "C_RDATA_WIDTH": [{"value": "32", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_WDATA_WIDTH": [{"value": "32", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_M_RUSER_BITS_PER_BYTE": [{"value": "0", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_M_WUSER_BITS_PER_BYTE": [{"value": "0", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_MAX_RUSER_BITS_PER_BYTE": [{"value": "0", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_MAX_WUSER_BITS_PER_BYTE": [{"value": "0", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_M_ARUSER_WIDTH": [{"value": "0", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_M_AWUSER_WIDTH": [{"value": "0", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_M_RUSER_WIDTH": [{"value": "0", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_M_WUSER_WIDTH": [{"value": "0", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_M_BUSER_WIDTH": [{"value": "0", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_S_ID_WIDTH": [{"value": "3", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_M_ID_WIDTH": [{"value": "0", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_ADDR_WIDTH": [{"value": "5", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_M_PROTOCOL": [{"value": "2", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_HAS_LOCK": [{"value": "0", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_NUM_MSC": [{"value": "20", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_SINGLE_ISSUING": [{"value": "1", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_M_LIMIT_READ_LENGTH": [{"value": "0", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_M_LIMIT_WRITE_LENGTH": [{"value": "0", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_NUM_READ_OUTSTANDING": [{"value": "8", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_NUM_WRITE_OUTSTANDING": [{"value": "8", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_READ_ACCEPTANCE": [{"value": "32", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_WRITE_ACCEPTANCE": [{"value": "32", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_NUM_WRITE_THREADS": [{"value": "1", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_NUM_READ_THREADS": [{"value": "1", "resolve_type": "generated", "format": "long", "usage": "all"}]}, "project_parameters": {"ARCHITECTURE": [{"value": "zynq"}], "BASE_BOARD_PART": [{"value": ""}], "BOARD_CONNECTIONS": [{"value": ""}], "DEVICE": [{"value": "xc7z020"}], "PACKAGE": [{"value": "clg400"}], "PREFHDL": [{"value": "VERILOG"}], "SILICON_REVISION": [{"value": ""}], "SIMULATOR_LANGUAGE": [{"value": "MIXED"}], "SPEEDGRADE": [{"value": "-1"}], "STATIC_POWER": [{"value": ""}], "TEMPERATURE_GRADE": [{"value": ""}]}, "runtime_parameters": {"IPCONTEXT": [{"value": "IP_Integrator"}], "IPREVISION": [{"value": "16"}], "MANAGED": [{"value": "TRUE"}], "OUTPUTDIR": [{"value": "."}], "SELECTEDSIMMODEL": [{"value": ""}], "SHAREDDIR": [{"value": "../../../../../ipshared"}], "SWVERSION": [{"value": "2024.2"}], "SYNTHESISFLOW": [{"value": "GLOBAL"}]}}, "boundary": {"ports": {"aclk": [{"direction": "in"}], "aresetn": [{"direction": "in", "driver_value": "0b1"}], "s_axi_awid": [{"direction": "in", "size_left": "2", "size_right": "0", "driver_value": "0x0"}], "s_axi_awaddr": [{"direction": "in", "size_left": "4", "size_right": "0", "driver_value": "0x00"}], "s_axi_awlen": [{"direction": "in", "size_left": "7", "size_right": "0", "driver_value": "0x0"}], "s_axi_awlock": [{"direction": "in", "size_left": "0", "size_right": "0", "driver_value": "0x0"}], "s_axi_awcache": [{"direction": "in", "size_left": "3", "size_right": "0", "driver_value": "0x0"}], "s_axi_awprot": [{"direction": "in", "size_left": "2", "size_right": "0", "driver_value": "0x0"}], "s_axi_awqos": [{"direction": "in", "size_left": "3", "size_right": "0", "driver_value": "0x0"}], "s_axi_awuser": [{"direction": "in", "size_left": "1023", "size_right": "0", "driver_value": "0x0"}], "s_axi_awvalid": [{"direction": "in", "driver_value": "0x0"}], "s_axi_awready": [{"direction": "out"}], "s_axi_wdata": [{"direction": "in", "size_left": "31", "size_right": "0", "driver_value": "0x00000000"}], "s_axi_wstrb": [{"direction": "in", "size_left": "3", "size_right": "0", "driver_value": "0xF"}], "s_axi_wlast": [{"direction": "in", "driver_value": "0x1"}], "s_axi_wuser": [{"direction": "in", "size_left": "1023", "size_right": "0", "driver_value": "0x0"}], "s_axi_wvalid": [{"direction": "in", "driver_value": "0x0"}], "s_axi_wready": [{"direction": "out"}], "s_axi_bid": [{"direction": "out", "size_left": "2", "size_right": "0"}], "s_axi_bresp": [{"direction": "out", "size_left": "1", "size_right": "0"}], "s_axi_buser": [{"direction": "out", "size_left": "1023", "size_right": "0"}], "s_axi_bvalid": [{"direction": "out"}], "s_axi_bready": [{"direction": "in", "driver_value": "0x0"}], "s_axi_arid": [{"direction": "in", "size_left": "2", "size_right": "0", "driver_value": "0x0"}], "s_axi_araddr": [{"direction": "in", "size_left": "4", "size_right": "0", "driver_value": "0x00"}], "s_axi_arlen": [{"direction": "in", "size_left": "7", "size_right": "0", "driver_value": "0x0"}], "s_axi_arlock": [{"direction": "in", "size_left": "0", "size_right": "0", "driver_value": "0x0"}], "s_axi_arcache": [{"direction": "in", "size_left": "3", "size_right": "0", "driver_value": "0x0"}], "s_axi_arprot": [{"direction": "in", "size_left": "2", "size_right": "0", "driver_value": "0x0"}], "s_axi_arqos": [{"direction": "in", "size_left": "3", "size_right": "0", "driver_value": "0x0"}], "s_axi_aruser": [{"direction": "in", "size_left": "1023", "size_right": "0", "driver_value": "0x0"}], "s_axi_arvalid": [{"direction": "in", "driver_value": "0x0"}], "s_axi_arready": [{"direction": "out"}], "s_axi_rid": [{"direction": "out", "size_left": "2", "size_right": "0"}], "s_axi_rdata": [{"direction": "out", "size_left": "31", "size_right": "0"}], "s_axi_rresp": [{"direction": "out", "size_left": "1", "size_right": "0"}], "s_axi_rlast": [{"direction": "out"}], "s_axi_ruser": [{"direction": "out", "size_left": "1023", "size_right": "0"}], "s_axi_rvalid": [{"direction": "out"}], "s_axi_rready": [{"direction": "in", "driver_value": "0x0"}], "m_axi_awaddr": [{"direction": "out", "size_left": "4", "size_right": "0"}], "m_axi_awprot": [{"direction": "out", "size_left": "2", "size_right": "0"}], "m_axi_awvalid": [{"direction": "out"}], "m_axi_awready": [{"direction": "in", "driver_value": "0x0"}], "m_axi_wdata": [{"direction": "out", "size_left": "31", "size_right": "0"}], "m_axi_wstrb": [{"direction": "out", "size_left": "3", "size_right": "0"}], "m_axi_wvalid": [{"direction": "out"}], "m_axi_wready": [{"direction": "in", "driver_value": "0x0"}], "m_axi_bresp": [{"direction": "in", "size_left": "1", "size_right": "0", "driver_value": "0x0"}], "m_axi_bvalid": [{"direction": "in", "driver_value": "0x0"}], "m_axi_bready": [{"direction": "out"}], "m_axi_araddr": [{"direction": "out", "size_left": "4", "size_right": "0"}], "m_axi_arprot": [{"direction": "out", "size_left": "2", "size_right": "0"}], "m_axi_arvalid": [{"direction": "out"}], "m_axi_arready": [{"direction": "in", "driver_value": "0x0"}], "m_axi_rdata": [{"direction": "in", "size_left": "31", "size_right": "0", "driver_value": "0x00000000"}], "m_axi_rresp": [{"direction": "in", "size_left": "1", "size_right": "0", "driver_value": "0x0"}], "m_axi_rvalid": [{"direction": "in", "driver_value": "0x0"}], "m_axi_rready": [{"direction": "out"}]}, "interfaces": {"S_AXI": {"vlnv": "xilinx.com:interface:aximm:1.0", "abstraction_type": "xilinx.com:interface:aximm_rtl:1.0", "mode": "slave", "parameters": {"DATA_WIDTH": [{"value": "32", "value_permission": "bd", "resolve_type": "generated", "format": "long", "is_ips_inferred": true, "is_static_object": false}], "PROTOCOL": [{"value": "AXI4", "value_permission": "bd", "resolve_type": "generated", "is_ips_inferred": true, "is_static_object": false}], "FREQ_HZ": [{"value": "50000000", "value_src": "user_prop", "value_permission": "bd", "resolve_type": "generated", "format": "long", "is_ips_inferred": true, "is_static_object": false}], "ID_WIDTH": [{"value": "3", "value_src": "propagated", "value_permission": "bd", "resolve_type": "generated", "format": "long", "is_ips_inferred": true, "is_static_object": false}], "ADDR_WIDTH": [{"value": "5", "value_src": "propagated", "value_permission": "bd", "resolve_type": "generated", "format": "long", "is_ips_inferred": true, "is_static_object": false}], "AWUSER_WIDTH": [{"value": "1024", "value_permission": "bd", "resolve_type": "generated", "format": "long", "is_ips_inferred": true, "is_static_object": false}], "ARUSER_WIDTH": [{"value": "1024", "value_permission": "bd", "resolve_type": "generated", "format": "long", "is_ips_inferred": true, "is_static_object": false}], "WUSER_WIDTH": [{"value": "1024", "value_permission": "bd", "resolve_type": "generated", "format": "long", "is_ips_inferred": true, "is_static_object": false}], "RUSER_WIDTH": [{"value": "1024", "value_permission": "bd", "resolve_type": "generated", "format": "long", "is_ips_inferred": true, "is_static_object": false}], "BUSER_WIDTH": [{"value": "1024", "value_permission": "bd", "resolve_type": "generated", "format": "long", "is_ips_inferred": true, "is_static_object": false}], "READ_WRITE_MODE": [{"value": "READ_WRITE", "value_permission": "bd", "resolve_type": "generated", "is_ips_inferred": true, "is_static_object": false}], "HAS_BURST": [{"value": "0", "value_src": "constant", "value_permission": "bd", "resolve_type": "generated", "format": "long", "is_ips_inferred": true, "is_static_object": false}], "HAS_LOCK": [{"value": "1", "value_permission": "bd", "resolve_type": "generated", "format": "long", "is_ips_inferred": true, "is_static_object": false}], "HAS_PROT": [{"value": "1", "value_permission": "bd", "resolve_type": "generated", "format": "long", "is_ips_inferred": true, "is_static_object": false}], "HAS_CACHE": [{"value": "1", "value_permission": "bd", "resolve_type": "generated", "format": "long", "is_ips_inferred": true, "is_static_object": false}], "HAS_QOS": [{"value": "1", "value_permission": "bd", "resolve_type": "generated", "format": "long", "is_ips_inferred": true, "is_static_object": false}], "HAS_REGION": [{"value": "0", "value_src": "constant", "value_permission": "bd", "resolve_type": "generated", "format": "long", "is_ips_inferred": true, "is_static_object": false}], "HAS_WSTRB": [{"value": "1", "value_permission": "bd", "resolve_type": "generated", "format": "long", "is_ips_inferred": true, "is_static_object": false}], "HAS_BRESP": [{"value": "1", "value_permission": "bd", "resolve_type": "generated", "format": "long", "is_ips_inferred": true, "is_static_object": false}], "HAS_RRESP": [{"value": "1", "value_permission": "bd", "resolve_type": "generated", "format": "long", "is_ips_inferred": true, "is_static_object": false}], "SUPPORTS_NARROW_BURST": [{"value": "0", "value_permission": "bd", "resolve_type": "generated", "format": "long", "is_ips_inferred": true, "is_static_object": false}], "NUM_READ_OUTSTANDING": [{"value": "2", "value_permission": "bd", "resolve_type": "generated", "format": "long", "is_ips_inferred": true, "is_static_object": false}], "NUM_WRITE_OUTSTANDING": [{"value": "2", "value_permission": "bd", "resolve_type": "generated", "format": "long", "is_ips_inferred": true, "is_static_object": false}], "MAX_BURST_LENGTH": [{"value": "256", "value_permission": "bd", "resolve_type": "generated", "format": "long", "is_ips_inferred": true, "is_static_object": false}], "PHASE": [{"value": "0.0", "value_permission": "bd", "resolve_type": "generated", "format": "float", "is_ips_inferred": true, "is_static_object": false}], "CLK_DOMAIN": [{"value": "ZynqDesign_processing_system7_0_0_FCLK_CLK0", "value_src": "default_prop", "value_permission": "bd", "resolve_type": "generated", "is_ips_inferred": true, "is_static_object": false}], "NUM_READ_THREADS": [{"value": "1", "value_permission": "bd", "resolve_type": "generated", "format": "long", "is_ips_inferred": true, "is_static_object": false}], "NUM_WRITE_THREADS": [{"value": "1", "value_permission": "bd", "resolve_type": "generated", "format": "long", "is_ips_inferred": true, "is_static_object": false}], "RUSER_BITS_PER_BYTE": [{"value": "0", "value_permission": "bd", "resolve_type": "generated", "format": "long", "is_ips_inferred": true, "is_static_object": false}], "WUSER_BITS_PER_BYTE": [{"value": "0", "value_permission": "bd", "resolve_type": "generated", "format": "long", "is_ips_inferred": true, "is_static_object": false}], "INSERT_VIP": [{"value": "0", "resolve_type": "user", "format": "long", "usage": "simulation.rtl", "is_ips_inferred": true, "is_static_object": false}]}, "port_maps": {"AWID": [{"physical_name": "s_axi_awid"}], "AWADDR": [{"physical_name": "s_axi_awaddr"}], "AWLEN": [{"physical_name": "s_axi_awlen"}], "AWLOCK": [{"physical_name": "s_axi_awlock"}], "AWCACHE": [{"physical_name": "s_axi_awcache"}], "AWPROT": [{"physical_name": "s_axi_awprot"}], "AWQOS": [{"physical_name": "s_axi_awqos"}], "AWUSER": [{"physical_name": "s_axi_awuser"}], "AWVALID": [{"physical_name": "s_axi_awvalid"}], "AWREADY": [{"physical_name": "s_axi_awready"}], "WDATA": [{"physical_name": "s_axi_wdata"}], "WSTRB": [{"physical_name": "s_axi_wstrb"}], "WLAST": [{"physical_name": "s_axi_wlast"}], "WUSER": [{"physical_name": "s_axi_wuser"}], "WVALID": [{"physical_name": "s_axi_wvalid"}], "WREADY": [{"physical_name": "s_axi_wready"}], "BID": [{"physical_name": "s_axi_bid"}], "BRESP": [{"physical_name": "s_axi_bresp"}], "BUSER": [{"physical_name": "s_axi_buser"}], "BVALID": [{"physical_name": "s_axi_bvalid"}], "BREADY": [{"physical_name": "s_axi_bready"}], "ARID": [{"physical_name": "s_axi_arid"}], "ARADDR": [{"physical_name": "s_axi_araddr"}], "ARLEN": [{"physical_name": "s_axi_arlen"}], "ARLOCK": [{"physical_name": "s_axi_arlock"}], "ARCACHE": [{"physical_name": "s_axi_arcache"}], "ARPROT": [{"physical_name": "s_axi_arprot"}], "ARQOS": [{"physical_name": "s_axi_arqos"}], "ARUSER": [{"physical_name": "s_axi_aruser"}], "ARVALID": [{"physical_name": "s_axi_arvalid"}], "ARREADY": [{"physical_name": "s_axi_arready"}], "RID": [{"physical_name": "s_axi_rid"}], "RDATA": [{"physical_name": "s_axi_rdata"}], "RRESP": [{"physical_name": "s_axi_rresp"}], "RLAST": [{"physical_name": "s_axi_rlast"}], "RUSER": [{"physical_name": "s_axi_ruser"}], "RVALID": [{"physical_name": "s_axi_rvalid"}], "RREADY": [{"physical_name": "s_axi_rready"}]}}, "M_AXI": {"vlnv": "xilinx.com:interface:aximm:1.0", "abstraction_type": "xilinx.com:interface:aximm_rtl:1.0", "mode": "master", "parameters": {"DATA_WIDTH": [{"value": "32", "value_permission": "bd", "resolve_type": "generated", "format": "long", "is_ips_inferred": true, "is_static_object": false}], "PROTOCOL": [{"value": "AXI4LITE", "value_src": "propagated", "value_permission": "bd", "resolve_type": "generated", "is_ips_inferred": true, "is_static_object": false}], "FREQ_HZ": [{"value": "50000000", "value_src": "user_prop", "value_permission": "bd", "resolve_type": "generated", "format": "long", "is_ips_inferred": true, "is_static_object": false}], "ID_WIDTH": [{"value": "0", "value_permission": "bd", "resolve_type": "generated", "format": "long", "is_ips_inferred": true, "is_static_object": false}], "ADDR_WIDTH": [{"value": "5", "value_src": "propagated", "value_permission": "bd", "resolve_type": "generated", "format": "long", "is_ips_inferred": true, "is_static_object": false}], "AWUSER_WIDTH": [{"value": "0", "value_permission": "bd", "resolve_type": "generated", "format": "long", "is_ips_inferred": true, "is_static_object": false}], "ARUSER_WIDTH": [{"value": "0", "value_permission": "bd", "resolve_type": "generated", "format": "long", "is_ips_inferred": true, "is_static_object": false}], "WUSER_WIDTH": [{"value": "0", "value_permission": "bd", "resolve_type": "generated", "format": "long", "is_ips_inferred": true, "is_static_object": false}], "RUSER_WIDTH": [{"value": "0", "value_permission": "bd", "resolve_type": "generated", "format": "long", "is_ips_inferred": true, "is_static_object": false}], "BUSER_WIDTH": [{"value": "0", "value_permission": "bd", "resolve_type": "generated", "format": "long", "is_ips_inferred": true, "is_static_object": false}], "READ_WRITE_MODE": [{"value": "READ_WRITE", "value_permission": "bd", "resolve_type": "generated", "is_ips_inferred": true, "is_static_object": false}], "HAS_BURST": [{"value": "0", "value_src": "propagated", "value_permission": "bd", "resolve_type": "generated", "format": "long", "is_ips_inferred": true, "is_static_object": false}], "HAS_LOCK": [{"value": "0", "value_src": "propagated", "value_permission": "bd", "resolve_type": "generated", "format": "long", "is_ips_inferred": true, "is_static_object": false}], "HAS_PROT": [{"value": "1", "value_src": "default_prop", "value_permission": "bd", "resolve_type": "generated", "format": "long", "is_ips_inferred": true, "is_static_object": false}], "HAS_CACHE": [{"value": "0", "value_src": "propagated", "value_permission": "bd", "resolve_type": "generated", "format": "long", "is_ips_inferred": true, "is_static_object": false}], "HAS_QOS": [{"value": "0", "value_src": "propagated", "value_permission": "bd", "resolve_type": "generated", "format": "long", "is_ips_inferred": true, "is_static_object": false}], "HAS_REGION": [{"value": "0", "value_src": "constant", "value_permission": "bd", "resolve_type": "generated", "format": "long", "is_ips_inferred": true, "is_static_object": false}], "HAS_WSTRB": [{"value": "1", "value_permission": "bd", "resolve_type": "generated", "format": "long", "is_ips_inferred": true, "is_static_object": false}], "HAS_BRESP": [{"value": "1", "value_permission": "bd", "resolve_type": "generated", "format": "long", "is_ips_inferred": true, "is_static_object": false}], "HAS_RRESP": [{"value": "1", "value_permission": "bd", "resolve_type": "generated", "format": "long", "is_ips_inferred": true, "is_static_object": false}], "SUPPORTS_NARROW_BURST": [{"value": "0", "value_permission": "bd", "resolve_type": "generated", "format": "long", "is_ips_inferred": true, "is_static_object": false}], "NUM_READ_OUTSTANDING": [{"value": "8", "value_src": "user", "value_permission": "bd", "resolve_type": "generated", "format": "long", "is_ips_inferred": true, "is_static_object": false}], "NUM_WRITE_OUTSTANDING": [{"value": "8", "value_src": "user", "value_permission": "bd", "resolve_type": "generated", "format": "long", "is_ips_inferred": true, "is_static_object": false}], "MAX_BURST_LENGTH": [{"value": "1", "value_permission": "bd", "resolve_type": "generated", "format": "long", "is_ips_inferred": true, "is_static_object": false}], "PHASE": [{"value": "0.0", "value_permission": "bd", "resolve_type": "generated", "format": "float", "is_ips_inferred": true, "is_static_object": false}], "CLK_DOMAIN": [{"value": "ZynqDesign_processing_system7_0_0_FCLK_CLK0", "value_src": "default_prop", "value_permission": "bd", "resolve_type": "generated", "is_ips_inferred": true, "is_static_object": false}], "NUM_READ_THREADS": [{"value": "1", "value_src": "user", "value_permission": "bd", "resolve_type": "generated", "format": "long", "is_ips_inferred": true, "is_static_object": false}], "NUM_WRITE_THREADS": [{"value": "1", "value_src": "user", "value_permission": "bd", "resolve_type": "generated", "format": "long", "is_ips_inferred": true, "is_static_object": false}], "RUSER_BITS_PER_BYTE": [{"value": "0", "value_src": "user", "value_permission": "bd", "resolve_type": "generated", "format": "long", "is_ips_inferred": true, "is_static_object": false}], "WUSER_BITS_PER_BYTE": [{"value": "0", "value_src": "user", "value_permission": "bd", "resolve_type": "generated", "format": "long", "is_ips_inferred": true, "is_static_object": false}], "INSERT_VIP": [{"value": "0", "resolve_type": "user", "format": "long", "usage": "simulation.rtl", "is_ips_inferred": true, "is_static_object": false}]}, "port_maps": {"AWADDR": [{"physical_name": "m_axi_awaddr"}], "AWPROT": [{"physical_name": "m_axi_awprot"}], "AWVALID": [{"physical_name": "m_axi_awvalid"}], "AWREADY": [{"physical_name": "m_axi_awready"}], "WDATA": [{"physical_name": "m_axi_wdata"}], "WSTRB": [{"physical_name": "m_axi_wstrb"}], "WVALID": [{"physical_name": "m_axi_wvalid"}], "WREADY": [{"physical_name": "m_axi_wready"}], "BRESP": [{"physical_name": "m_axi_bresp"}], "BVALID": [{"physical_name": "m_axi_bvalid"}], "BREADY": [{"physical_name": "m_axi_bready"}], "ARADDR": [{"physical_name": "m_axi_araddr"}], "ARPROT": [{"physical_name": "m_axi_arprot"}], "ARVALID": [{"physical_name": "m_axi_arvalid"}], "ARREADY": [{"physical_name": "m_axi_arready"}], "RDATA": [{"physical_name": "m_axi_rdata"}], "RRESP": [{"physical_name": "m_axi_rresp"}], "RVALID": [{"physical_name": "m_axi_rvalid"}], "RREADY": [{"physical_name": "m_axi_rready"}]}}, "CLK": {"vlnv": "xilinx.com:signal:clock:1.0", "abstraction_type": "xilinx.com:signal:clock_rtl:1.0", "mode": "slave", "parameters": {"ASSOCIATED_BUSIF": [{"value": "S_AXI:M_AXI", "value_src": "constant", "value_permission": "bd", "usage": "all"}], "ASSOCIATED_RESET": [{"value": "aresetn", "value_src": "constant", "value_permission": "bd", "usage": "all"}], "FREQ_HZ": [{"value": "50000000", "value_src": "user_prop", "value_permission": "bd", "resolve_type": "user", "format": "long", "usage": "all"}], "FREQ_TOLERANCE_HZ": [{"value": "0", "value_permission": "bd", "resolve_type": "generated", "format": "long", "is_ips_inferred": true, "is_static_object": false}], "PHASE": [{"value": "0.0", "value_permission": "bd", "resolve_type": "generated", "format": "float", "is_ips_inferred": true, "is_static_object": false}], "CLK_DOMAIN": [{"value": "ZynqDesign_processing_system7_0_0_FCLK_CLK0", "value_src": "default_prop", "value_permission": "bd", "resolve_type": "generated", "is_ips_inferred": true, "is_static_object": false}], "ASSOCIATED_PORT": [{"value": "", "value_permission": "bd", "resolve_type": "generated", "is_ips_inferred": true, "is_static_object": false}], "INSERT_VIP": [{"value": "0", "resolve_type": "user", "format": "long", "usage": "simulation.rtl", "is_ips_inferred": true, "is_static_object": false}]}, "port_maps": {"CLK": [{"physical_name": "aclk"}]}}, "RST": {"vlnv": "xilinx.com:signal:reset:1.0", "abstraction_type": "xilinx.com:signal:reset_rtl:1.0", "mode": "slave", "parameters": {"POLARITY": [{"value": "ACTIVE_LOW", "value_src": "constant_prop", "value_permission": "bd", "resolve_type": "generated", "is_ips_inferred": true, "is_static_object": false}], "INSERT_VIP": [{"value": "0", "resolve_type": "user", "format": "long", "usage": "simulation.rtl", "is_ips_inferred": true, "is_static_object": false}], "TYPE": [{"value": "INTERCONNECT", "resolve_type": "generated", "is_ips_inferred": true, "is_static_object": false}]}, "port_maps": {"RST": [{"physical_name": "aresetn"}]}}}}}}