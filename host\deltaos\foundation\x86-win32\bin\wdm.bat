@echo off
:: ################################################################
:: # Purpose:  Wrap wdm.sh for windows                            #
:: # OS:       Windows                                            #
:: ################################################################
:: # use sh.exe and launch the more powerful shell script         #
SETLOCAL
SET NAME=%~n0
SET DIR=%~dp0
SET UNIXDIR=%DIR:\=/%
IF "%WIND_HOST_TYPE%" == "" SET WIND_HOST_TYPE=x86-win32
IF "%WIND_TOOLS%" == "" SET WIND_TOOLS=%DIR%\..\..
:: # Set the path to ensure the shell script finds all tools      #
set PATH=%WIND_TOOLS%\..\utilities-1.0\x86-win32\bin;%PATH%
:: # Finally, call the appropriate shell script                   #
call sh.exe %UNIXDIR%/%NAME%.sh %*
