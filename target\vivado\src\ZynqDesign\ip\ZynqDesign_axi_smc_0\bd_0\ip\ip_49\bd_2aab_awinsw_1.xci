{"schema": "xilinx.com:schema:json_instance:1.0", "ip_inst": {"xci_name": "bd_2aab_awinsw_1", "cell_name": "aw_la_out_swbd_0", "component_reference": "xilinx.com:ip:sc_switchboard:1.0", "ip_revision": "8", "gen_directory": ".", "parameters": {"component_parameters": {"S_PIPELINES": [{"value": "0", "resolve_type": "user", "format": "long", "usage": "all"}], "M_PIPELINES": [{"value": "0", "resolve_type": "user", "format": "long", "usage": "all"}], "S_LATENCY": [{"value": "0", "resolve_type": "user", "format": "long", "usage": "all"}], "NUM_SI": [{"value": "1", "value_src": "user", "resolve_type": "user", "format": "long", "usage": "all"}], "NUM_MI": [{"value": "3", "value_src": "user", "resolve_type": "user", "format": "long", "usage": "all"}], "TESTING_MODE": [{"value": "0", "resolve_type": "user", "format": "long", "usage": "all"}], "PAYLD_WIDTH": [{"value": "155", "value_src": "user", "resolve_type": "user", "format": "long", "usage": "all"}], "M00_S00_CONNECTIVITY": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "M00_S01_CONNECTIVITY": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "M00_S02_CONNECTIVITY": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "M00_S03_CONNECTIVITY": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "M00_S04_CONNECTIVITY": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "M00_S05_CONNECTIVITY": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "M00_S06_CONNECTIVITY": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "M00_S07_CONNECTIVITY": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "M00_S08_CONNECTIVITY": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "M00_S09_CONNECTIVITY": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "M00_S10_CONNECTIVITY": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "M00_S11_CONNECTIVITY": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "M00_S12_CONNECTIVITY": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "M00_S13_CONNECTIVITY": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "M00_S14_CONNECTIVITY": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "M00_S15_CONNECTIVITY": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "M01_S00_CONNECTIVITY": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "M01_S01_CONNECTIVITY": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "M01_S02_CONNECTIVITY": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "M01_S03_CONNECTIVITY": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "M01_S04_CONNECTIVITY": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "M01_S05_CONNECTIVITY": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "M01_S06_CONNECTIVITY": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "M01_S07_CONNECTIVITY": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "M01_S08_CONNECTIVITY": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "M01_S09_CONNECTIVITY": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "M01_S10_CONNECTIVITY": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "M01_S11_CONNECTIVITY": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "M01_S12_CONNECTIVITY": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "M01_S13_CONNECTIVITY": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "M01_S14_CONNECTIVITY": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "M01_S15_CONNECTIVITY": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "M02_S00_CONNECTIVITY": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "M02_S01_CONNECTIVITY": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "M02_S02_CONNECTIVITY": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "M02_S03_CONNECTIVITY": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "M02_S04_CONNECTIVITY": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "M02_S05_CONNECTIVITY": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "M02_S06_CONNECTIVITY": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "M02_S07_CONNECTIVITY": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "M02_S08_CONNECTIVITY": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "M02_S09_CONNECTIVITY": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "M02_S10_CONNECTIVITY": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "M02_S11_CONNECTIVITY": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "M02_S12_CONNECTIVITY": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "M02_S13_CONNECTIVITY": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "M02_S14_CONNECTIVITY": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "M02_S15_CONNECTIVITY": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "M03_S00_CONNECTIVITY": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "M03_S01_CONNECTIVITY": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "M03_S02_CONNECTIVITY": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "M03_S03_CONNECTIVITY": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "M03_S04_CONNECTIVITY": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "M03_S05_CONNECTIVITY": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "M03_S06_CONNECTIVITY": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "M03_S07_CONNECTIVITY": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "M03_S08_CONNECTIVITY": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "M03_S09_CONNECTIVITY": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "M03_S10_CONNECTIVITY": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "M03_S11_CONNECTIVITY": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "M03_S12_CONNECTIVITY": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "M03_S13_CONNECTIVITY": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "M03_S14_CONNECTIVITY": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "M03_S15_CONNECTIVITY": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "M04_S00_CONNECTIVITY": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "M04_S01_CONNECTIVITY": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "M04_S02_CONNECTIVITY": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "M04_S03_CONNECTIVITY": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "M04_S04_CONNECTIVITY": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "M04_S05_CONNECTIVITY": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "M04_S06_CONNECTIVITY": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "M04_S07_CONNECTIVITY": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "M04_S08_CONNECTIVITY": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "M04_S09_CONNECTIVITY": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "M04_S10_CONNECTIVITY": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "M04_S11_CONNECTIVITY": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "M04_S12_CONNECTIVITY": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "M04_S13_CONNECTIVITY": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "M04_S14_CONNECTIVITY": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "M04_S15_CONNECTIVITY": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "M05_S00_CONNECTIVITY": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "M05_S01_CONNECTIVITY": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "M05_S02_CONNECTIVITY": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "M05_S03_CONNECTIVITY": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "M05_S04_CONNECTIVITY": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "M05_S05_CONNECTIVITY": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "M05_S06_CONNECTIVITY": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "M05_S07_CONNECTIVITY": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "M05_S08_CONNECTIVITY": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "M05_S09_CONNECTIVITY": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "M05_S10_CONNECTIVITY": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "M05_S11_CONNECTIVITY": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "M05_S12_CONNECTIVITY": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "M05_S13_CONNECTIVITY": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "M05_S14_CONNECTIVITY": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "M05_S15_CONNECTIVITY": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "M06_S00_CONNECTIVITY": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "M06_S01_CONNECTIVITY": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "M06_S02_CONNECTIVITY": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "M06_S03_CONNECTIVITY": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "M06_S04_CONNECTIVITY": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "M06_S05_CONNECTIVITY": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "M06_S06_CONNECTIVITY": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "M06_S07_CONNECTIVITY": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "M06_S08_CONNECTIVITY": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "M06_S09_CONNECTIVITY": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "M06_S10_CONNECTIVITY": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "M06_S11_CONNECTIVITY": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "M06_S12_CONNECTIVITY": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "M06_S13_CONNECTIVITY": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "M06_S14_CONNECTIVITY": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "M06_S15_CONNECTIVITY": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "M07_S00_CONNECTIVITY": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "M07_S01_CONNECTIVITY": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "M07_S02_CONNECTIVITY": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "M07_S03_CONNECTIVITY": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "M07_S04_CONNECTIVITY": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "M07_S05_CONNECTIVITY": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "M07_S06_CONNECTIVITY": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "M07_S07_CONNECTIVITY": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "M07_S08_CONNECTIVITY": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "M07_S09_CONNECTIVITY": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "M07_S10_CONNECTIVITY": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "M07_S11_CONNECTIVITY": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "M07_S12_CONNECTIVITY": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "M07_S13_CONNECTIVITY": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "M07_S14_CONNECTIVITY": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "M07_S15_CONNECTIVITY": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "M08_S00_CONNECTIVITY": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "M08_S01_CONNECTIVITY": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "M08_S02_CONNECTIVITY": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "M08_S03_CONNECTIVITY": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "M08_S04_CONNECTIVITY": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "M08_S05_CONNECTIVITY": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "M08_S06_CONNECTIVITY": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "M08_S07_CONNECTIVITY": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "M08_S08_CONNECTIVITY": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "M08_S09_CONNECTIVITY": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "M08_S10_CONNECTIVITY": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "M08_S11_CONNECTIVITY": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "M08_S12_CONNECTIVITY": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "M08_S13_CONNECTIVITY": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "M08_S14_CONNECTIVITY": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "M08_S15_CONNECTIVITY": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "M09_S00_CONNECTIVITY": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "M09_S01_CONNECTIVITY": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "M09_S02_CONNECTIVITY": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "M09_S03_CONNECTIVITY": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "M09_S04_CONNECTIVITY": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "M09_S05_CONNECTIVITY": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "M09_S06_CONNECTIVITY": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "M09_S07_CONNECTIVITY": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "M09_S08_CONNECTIVITY": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "M09_S09_CONNECTIVITY": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "M09_S10_CONNECTIVITY": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "M09_S11_CONNECTIVITY": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "M09_S12_CONNECTIVITY": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "M09_S13_CONNECTIVITY": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "M09_S14_CONNECTIVITY": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "M09_S15_CONNECTIVITY": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "M10_S00_CONNECTIVITY": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "M10_S01_CONNECTIVITY": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "M10_S02_CONNECTIVITY": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "M10_S03_CONNECTIVITY": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "M10_S04_CONNECTIVITY": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "M10_S05_CONNECTIVITY": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "M10_S06_CONNECTIVITY": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "M10_S07_CONNECTIVITY": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "M10_S08_CONNECTIVITY": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "M10_S09_CONNECTIVITY": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "M10_S10_CONNECTIVITY": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "M10_S11_CONNECTIVITY": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "M10_S12_CONNECTIVITY": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "M10_S13_CONNECTIVITY": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "M10_S14_CONNECTIVITY": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "M10_S15_CONNECTIVITY": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "M11_S00_CONNECTIVITY": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "M11_S01_CONNECTIVITY": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "M11_S02_CONNECTIVITY": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "M11_S03_CONNECTIVITY": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "M11_S04_CONNECTIVITY": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "M11_S05_CONNECTIVITY": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "M11_S06_CONNECTIVITY": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "M11_S07_CONNECTIVITY": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "M11_S08_CONNECTIVITY": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "M11_S09_CONNECTIVITY": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "M11_S10_CONNECTIVITY": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "M11_S11_CONNECTIVITY": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "M11_S12_CONNECTIVITY": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "M11_S13_CONNECTIVITY": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "M11_S14_CONNECTIVITY": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "M11_S15_CONNECTIVITY": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "M12_S00_CONNECTIVITY": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "M12_S01_CONNECTIVITY": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "M12_S02_CONNECTIVITY": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "M12_S03_CONNECTIVITY": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "M12_S04_CONNECTIVITY": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "M12_S05_CONNECTIVITY": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "M12_S06_CONNECTIVITY": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "M12_S07_CONNECTIVITY": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "M12_S08_CONNECTIVITY": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "M12_S09_CONNECTIVITY": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "M12_S10_CONNECTIVITY": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "M12_S11_CONNECTIVITY": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "M12_S12_CONNECTIVITY": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "M12_S13_CONNECTIVITY": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "M12_S14_CONNECTIVITY": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "M12_S15_CONNECTIVITY": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "M13_S00_CONNECTIVITY": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "M13_S01_CONNECTIVITY": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "M13_S02_CONNECTIVITY": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "M13_S03_CONNECTIVITY": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "M13_S04_CONNECTIVITY": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "M13_S05_CONNECTIVITY": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "M13_S06_CONNECTIVITY": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "M13_S07_CONNECTIVITY": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "M13_S08_CONNECTIVITY": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "M13_S09_CONNECTIVITY": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "M13_S10_CONNECTIVITY": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "M13_S11_CONNECTIVITY": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "M13_S12_CONNECTIVITY": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "M13_S13_CONNECTIVITY": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "M13_S14_CONNECTIVITY": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "M13_S15_CONNECTIVITY": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "M14_S00_CONNECTIVITY": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "M14_S01_CONNECTIVITY": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "M14_S02_CONNECTIVITY": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "M14_S03_CONNECTIVITY": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "M14_S04_CONNECTIVITY": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "M14_S05_CONNECTIVITY": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "M14_S06_CONNECTIVITY": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "M14_S07_CONNECTIVITY": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "M14_S08_CONNECTIVITY": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "M14_S09_CONNECTIVITY": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "M14_S10_CONNECTIVITY": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "M14_S11_CONNECTIVITY": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "M14_S12_CONNECTIVITY": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "M14_S13_CONNECTIVITY": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "M14_S14_CONNECTIVITY": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "M14_S15_CONNECTIVITY": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "M15_S00_CONNECTIVITY": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "M15_S01_CONNECTIVITY": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "M15_S02_CONNECTIVITY": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "M15_S03_CONNECTIVITY": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "M15_S04_CONNECTIVITY": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "M15_S05_CONNECTIVITY": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "M15_S06_CONNECTIVITY": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "M15_S07_CONNECTIVITY": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "M15_S08_CONNECTIVITY": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "M15_S09_CONNECTIVITY": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "M15_S10_CONNECTIVITY": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "M15_S11_CONNECTIVITY": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "M15_S12_CONNECTIVITY": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "M15_S13_CONNECTIVITY": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "M15_S14_CONNECTIVITY": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "M15_S15_CONNECTIVITY": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "Component_Name": [{"value": "bd_2aab_awinsw_1", "resolve_type": "user", "usage": "all"}]}, "model_parameters": {"C_PAYLD_WIDTH": [{"value": "155", "resolve_type": "generated", "format": "long", "usage": "all"}], "K_MAX_INFO_WIDTH": [{"value": "1", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_S_PIPELINES": [{"value": "0", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_M_PIPELINES": [{"value": "0", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_S_LATENCY": [{"value": "0", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_NUM_SI": [{"value": "1", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_NUM_MI": [{"value": "3", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_TESTING_MODE": [{"value": "0", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_CONNECTIVITY": [{"value": "0b111", "resolve_type": "generated", "format": "bitString", "enabled": false, "usage": "all"}]}, "project_parameters": {"ARCHITECTURE": [{"value": "zynq"}], "BASE_BOARD_PART": [{"value": ""}], "BOARD_CONNECTIONS": [{"value": ""}], "DEVICE": [{"value": "xc7z020"}], "PACKAGE": [{"value": "clg400"}], "PREFHDL": [{"value": "VERILOG"}], "SILICON_REVISION": [{"value": ""}], "SIMULATOR_LANGUAGE": [{"value": "MIXED"}], "SPEEDGRADE": [{"value": "-1"}], "STATIC_POWER": [{"value": ""}], "TEMPERATURE_GRADE": [{"value": ""}]}, "runtime_parameters": {"IPCONTEXT": [{"value": "IP_Integrator"}], "IPREVISION": [{"value": "8"}], "MANAGED": [{"value": "TRUE"}], "OUTPUTDIR": [{"value": "."}], "SELECTEDSIMMODEL": [{"value": ""}], "SHAREDDIR": [{"value": "../../../../../ipshared"}], "SWVERSION": [{"value": "2024.2"}], "SYNTHESISFLOW": [{"value": "GLOBAL"}]}}, "boundary": {"ports": {"aclk": [{"direction": "in"}], "aclken": [{"direction": "in", "driver_value": "1"}], "s_sc_send": [{"direction": "in", "size_left": "2", "size_right": "0"}], "s_sc_req": [{"direction": "in", "size_left": "2", "size_right": "0"}], "s_sc_info": [{"direction": "in", "size_left": "2", "size_right": "0"}], "s_sc_payld": [{"direction": "in", "size_left": "154", "size_right": "0"}], "s_sc_recv": [{"direction": "out", "size_left": "2", "size_right": "0"}], "m_sc_recv": [{"direction": "in", "size_left": "2", "size_right": "0"}], "m_sc_send": [{"direction": "out", "size_left": "2", "size_right": "0"}], "m_sc_req": [{"direction": "out", "size_left": "2", "size_right": "0"}], "m_sc_info": [{"direction": "out", "size_left": "2", "size_right": "0"}], "m_sc_payld": [{"direction": "out", "size_left": "464", "size_right": "0"}]}, "interfaces": {"aclk": {"vlnv": "xilinx.com:signal:clock:1.0", "abstraction_type": "xilinx.com:signal:clock_rtl:1.0", "mode": "slave", "parameters": {"FREQ_HZ": [{"value": "50000000", "value_src": "user_prop", "value_permission": "bd", "resolve_type": "generated", "format": "long", "is_ips_inferred": true, "is_static_object": false}], "FREQ_TOLERANCE_HZ": [{"value": "0", "value_permission": "bd", "resolve_type": "generated", "format": "long", "is_ips_inferred": true, "is_static_object": false}], "PHASE": [{"value": "0.0", "value_permission": "bd", "resolve_type": "generated", "format": "float", "is_ips_inferred": true, "is_static_object": false}], "CLK_DOMAIN": [{"value": "ZynqDesign_processing_system7_0_0_FCLK_CLK0", "value_src": "default_prop", "value_permission": "bd", "resolve_type": "generated", "is_ips_inferred": true, "is_static_object": false}], "ASSOCIATED_BUSIF": [{"value": "M00_SC:M01_SC:M02_SC:M03_SC:M04_SC:M05_SC:M06_SC:M07_SC:M08_SC:M09_SC:M10_SC:M11_SC:M12_SC:M13_SC:M14_SC:M15_SC:S00_SC:S01_SC:S02_SC:S03_SC:S04_SC:S05_SC:S06_SC:S07_SC:S08_SC:S09_SC:S10_SC:S11_SC:S12_SC:S13_SC:S14_SC:S15_SC", "value_permission": "bd", "resolve_type": "generated", "is_ips_inferred": true, "is_static_object": false}], "ASSOCIATED_PORT": [{"value": "", "value_permission": "bd", "resolve_type": "generated", "is_ips_inferred": true, "is_static_object": false}], "ASSOCIATED_RESET": [{"value": "", "value_permission": "bd", "resolve_type": "generated", "is_ips_inferred": true, "is_static_object": false}], "INSERT_VIP": [{"value": "0", "resolve_type": "user", "format": "long", "usage": "simulation.rtl", "is_ips_inferred": true, "is_static_object": false}]}, "port_maps": {"CLK": [{"physical_name": "aclk"}]}}, "aclken": {"vlnv": "xilinx.com:signal:clockenable:1.0", "abstraction_type": "xilinx.com:signal:clockenable_rtl:1.0", "mode": "slave", "parameters": {"POLARITY": [{"value": "ACTIVE_LOW", "value_permission": "bd", "resolve_type": "generated", "is_ips_inferred": true, "is_static_object": false}]}, "port_maps": {"CE": [{"physical_name": "a<PERSON><PERSON><PERSON>"}]}}, "S00_SC": {"vlnv": "xilinx.com:interface:sc:1.0", "abstraction_type": "xilinx.com:interface:sc_rtl:1.0", "mode": "slave", "parameters": {"BRIDGES": [{"value": "M00_SC:M01_SC:M02_SC:M03_SC:M04_SC:M05_SC:M06_SC:M07_SC:M08_SC:M09_SC:M10_SC:M11_SC:M12_SC:M13_SC:M14_SC:M15_SC", "value_src": "constant", "value_permission": "bd", "usage": "all"}]}, "port_maps": {"SEND": [{"physical_name": "s_sc_send", "physical_left": "2", "physical_right": "0"}], "REQ": [{"physical_name": "s_sc_req", "physical_left": "2", "physical_right": "0"}], "INFO": [{"physical_name": "s_sc_info", "physical_left": "2", "physical_right": "0"}], "PAYLD": [{"physical_name": "s_sc_payld", "physical_left": "154", "physical_right": "0"}], "RECV": [{"physical_name": "s_sc_recv", "physical_left": "2", "physical_right": "0"}]}}, "M00_SC": {"vlnv": "xilinx.com:interface:sc:1.0", "abstraction_type": "xilinx.com:interface:sc_rtl:1.0", "mode": "master", "port_maps": {"RECV": [{"physical_name": "m_sc_recv", "physical_left": "0", "physical_right": "0"}], "SEND": [{"physical_name": "m_sc_send", "physical_left": "0", "physical_right": "0"}], "REQ": [{"physical_name": "m_sc_req", "physical_left": "0", "physical_right": "0"}], "INFO": [{"physical_name": "m_sc_info", "physical_left": "0", "physical_right": "0"}], "PAYLD": [{"physical_name": "m_sc_payld", "physical_left": "154", "physical_right": "0"}]}}, "M01_SC": {"vlnv": "xilinx.com:interface:sc:1.0", "abstraction_type": "xilinx.com:interface:sc_rtl:1.0", "mode": "master", "port_maps": {"RECV": [{"physical_name": "m_sc_recv", "physical_left": "1", "physical_right": "1"}], "SEND": [{"physical_name": "m_sc_send", "physical_left": "1", "physical_right": "1"}], "REQ": [{"physical_name": "m_sc_req", "physical_left": "1", "physical_right": "1"}], "INFO": [{"physical_name": "m_sc_info", "physical_left": "1", "physical_right": "1"}], "PAYLD": [{"physical_name": "m_sc_payld", "physical_left": "309", "physical_right": "155"}]}}, "M02_SC": {"vlnv": "xilinx.com:interface:sc:1.0", "abstraction_type": "xilinx.com:interface:sc_rtl:1.0", "mode": "master", "port_maps": {"RECV": [{"physical_name": "m_sc_recv", "physical_left": "2", "physical_right": "2"}], "SEND": [{"physical_name": "m_sc_send", "physical_left": "2", "physical_right": "2"}], "REQ": [{"physical_name": "m_sc_req", "physical_left": "2", "physical_right": "2"}], "INFO": [{"physical_name": "m_sc_info", "physical_left": "2", "physical_right": "2"}], "PAYLD": [{"physical_name": "m_sc_payld", "physical_left": "464", "physical_right": "310"}]}}}}}}