<?xml version="1.0" encoding="UTF-8"?>
<spirit:component xmlns:xilinx="http://www.xilinx.com" xmlns:spirit="http://www.spiritconsortium.org/XMLSchema/SPIRIT/1685-2009" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <spirit:vendor>xilinx.com</spirit:vendor>
  <spirit:library>customized_ip</spirit:library>
  <spirit:name>bd_2aab_bni_3</spirit:name>
  <spirit:version>1.0</spirit:version>
  <spirit:busInterfaces>
    <spirit:busInterface>
      <spirit:name>S_SC</spirit:name>
      <spirit:displayName>S_SC</spirit:displayName>
      <spirit:busType spirit:vendor="xilinx.com" spirit:library="interface" spirit:name="sc" spirit:version="1.0"/>
      <spirit:abstractionType spirit:vendor="xilinx.com" spirit:library="interface" spirit:name="sc_rtl" spirit:version="1.0"/>
      <spirit:slave/>
      <spirit:portMaps>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>INFO</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>s_sc_info</spirit:name>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>PAYLD</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>s_sc_payld</spirit:name>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>RECV</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>s_sc_recv</spirit:name>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>REQ</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>s_sc_req</spirit:name>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>SEND</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>s_sc_send</spirit:name>
          </spirit:physicalPort>
        </spirit:portMap>
      </spirit:portMaps>
    </spirit:busInterface>
    <spirit:busInterface>
      <spirit:name>M_SC</spirit:name>
      <spirit:displayName>M_SC</spirit:displayName>
      <spirit:busType spirit:vendor="xilinx.com" spirit:library="interface" spirit:name="sc" spirit:version="1.0"/>
      <spirit:abstractionType spirit:vendor="xilinx.com" spirit:library="interface" spirit:name="sc_rtl" spirit:version="1.0"/>
      <spirit:master/>
      <spirit:portMaps>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>INFO</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>m_sc_info</spirit:name>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>PAYLD</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>m_sc_payld</spirit:name>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>RECV</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>m_sc_recv</spirit:name>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>REQ</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>m_sc_req</spirit:name>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>SEND</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>m_sc_send</spirit:name>
          </spirit:physicalPort>
        </spirit:portMap>
      </spirit:portMaps>
    </spirit:busInterface>
    <spirit:busInterface>
      <spirit:name>S_AXIS_ARB</spirit:name>
      <spirit:displayName>S_AXIS_ARB</spirit:displayName>
      <spirit:busType spirit:vendor="xilinx.com" spirit:library="interface" spirit:name="axis" spirit:version="1.0"/>
      <spirit:abstractionType spirit:vendor="xilinx.com" spirit:library="interface" spirit:name="axis_rtl" spirit:version="1.0"/>
      <spirit:slave/>
      <spirit:portMaps>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>TDATA</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>s_axis_arb_tdata</spirit:name>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>TREADY</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>s_axis_arb_tready</spirit:name>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>TVALID</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>s_axis_arb_tvalid</spirit:name>
          </spirit:physicalPort>
        </spirit:portMap>
      </spirit:portMaps>
      <spirit:parameters>
        <spirit:parameter>
          <spirit:name>TDATA_NUM_BYTES</spirit:name>
          <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="BUSIFPARAM_VALUE.S_AXIS_ARB.TDATA_NUM_BYTES">1</spirit:value>
          <spirit:vendorExtensions>
            <xilinx:parameterInfo>
              <xilinx:parameterUsage>none</xilinx:parameterUsage>
            </xilinx:parameterInfo>
          </spirit:vendorExtensions>
        </spirit:parameter>
        <spirit:parameter>
          <spirit:name>TDEST_WIDTH</spirit:name>
          <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="BUSIFPARAM_VALUE.S_AXIS_ARB.TDEST_WIDTH">0</spirit:value>
          <spirit:vendorExtensions>
            <xilinx:parameterInfo>
              <xilinx:parameterUsage>none</xilinx:parameterUsage>
            </xilinx:parameterInfo>
          </spirit:vendorExtensions>
        </spirit:parameter>
        <spirit:parameter>
          <spirit:name>TID_WIDTH</spirit:name>
          <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="BUSIFPARAM_VALUE.S_AXIS_ARB.TID_WIDTH">0</spirit:value>
          <spirit:vendorExtensions>
            <xilinx:parameterInfo>
              <xilinx:parameterUsage>none</xilinx:parameterUsage>
            </xilinx:parameterInfo>
          </spirit:vendorExtensions>
        </spirit:parameter>
        <spirit:parameter>
          <spirit:name>TUSER_WIDTH</spirit:name>
          <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="BUSIFPARAM_VALUE.S_AXIS_ARB.TUSER_WIDTH">0</spirit:value>
          <spirit:vendorExtensions>
            <xilinx:parameterInfo>
              <xilinx:parameterUsage>none</xilinx:parameterUsage>
            </xilinx:parameterInfo>
          </spirit:vendorExtensions>
        </spirit:parameter>
        <spirit:parameter>
          <spirit:name>HAS_TREADY</spirit:name>
          <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="BUSIFPARAM_VALUE.S_AXIS_ARB.HAS_TREADY">1</spirit:value>
          <spirit:vendorExtensions>
            <xilinx:parameterInfo>
              <xilinx:parameterUsage>none</xilinx:parameterUsage>
            </xilinx:parameterInfo>
          </spirit:vendorExtensions>
        </spirit:parameter>
        <spirit:parameter>
          <spirit:name>HAS_TSTRB</spirit:name>
          <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="BUSIFPARAM_VALUE.S_AXIS_ARB.HAS_TSTRB">0</spirit:value>
          <spirit:vendorExtensions>
            <xilinx:parameterInfo>
              <xilinx:parameterUsage>none</xilinx:parameterUsage>
            </xilinx:parameterInfo>
          </spirit:vendorExtensions>
        </spirit:parameter>
        <spirit:parameter>
          <spirit:name>HAS_TKEEP</spirit:name>
          <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="BUSIFPARAM_VALUE.S_AXIS_ARB.HAS_TKEEP">0</spirit:value>
          <spirit:vendorExtensions>
            <xilinx:parameterInfo>
              <xilinx:parameterUsage>none</xilinx:parameterUsage>
            </xilinx:parameterInfo>
          </spirit:vendorExtensions>
        </spirit:parameter>
        <spirit:parameter>
          <spirit:name>HAS_TLAST</spirit:name>
          <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="BUSIFPARAM_VALUE.S_AXIS_ARB.HAS_TLAST">0</spirit:value>
          <spirit:vendorExtensions>
            <xilinx:parameterInfo>
              <xilinx:parameterUsage>none</xilinx:parameterUsage>
            </xilinx:parameterInfo>
          </spirit:vendorExtensions>
        </spirit:parameter>
        <spirit:parameter>
          <spirit:name>FREQ_HZ</spirit:name>
          <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="BUSIFPARAM_VALUE.S_AXIS_ARB.FREQ_HZ">100000000</spirit:value>
          <spirit:vendorExtensions>
            <xilinx:parameterInfo>
              <xilinx:parameterUsage>none</xilinx:parameterUsage>
            </xilinx:parameterInfo>
          </spirit:vendorExtensions>
        </spirit:parameter>
        <spirit:parameter>
          <spirit:name>PHASE</spirit:name>
          <spirit:value spirit:format="float" spirit:resolve="generated" spirit:id="BUSIFPARAM_VALUE.S_AXIS_ARB.PHASE">0.0</spirit:value>
          <spirit:vendorExtensions>
            <xilinx:parameterInfo>
              <xilinx:parameterUsage>none</xilinx:parameterUsage>
            </xilinx:parameterInfo>
          </spirit:vendorExtensions>
        </spirit:parameter>
        <spirit:parameter>
          <spirit:name>CLK_DOMAIN</spirit:name>
          <spirit:value spirit:resolve="generated" spirit:id="BUSIFPARAM_VALUE.S_AXIS_ARB.CLK_DOMAIN"/>
          <spirit:vendorExtensions>
            <xilinx:parameterInfo>
              <xilinx:parameterUsage>none</xilinx:parameterUsage>
            </xilinx:parameterInfo>
          </spirit:vendorExtensions>
        </spirit:parameter>
        <spirit:parameter>
          <spirit:name>LAYERED_METADATA</spirit:name>
          <spirit:value spirit:resolve="generated" spirit:id="BUSIFPARAM_VALUE.S_AXIS_ARB.LAYERED_METADATA">undef</spirit:value>
          <spirit:vendorExtensions>
            <xilinx:parameterInfo>
              <xilinx:parameterUsage>none</xilinx:parameterUsage>
            </xilinx:parameterInfo>
          </spirit:vendorExtensions>
        </spirit:parameter>
        <spirit:parameter>
          <spirit:name>INSERT_VIP</spirit:name>
          <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="BUSIFPARAM_VALUE.S_AXIS_ARB.INSERT_VIP">0</spirit:value>
          <spirit:vendorExtensions>
            <xilinx:parameterInfo>
              <xilinx:parameterUsage>simulation.rtl</xilinx:parameterUsage>
            </xilinx:parameterInfo>
          </spirit:vendorExtensions>
        </spirit:parameter>
      </spirit:parameters>
      <spirit:vendorExtensions>
        <xilinx:busInterfaceInfo>
          <xilinx:enablement>
            <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="BUSIF_ENABLEMENT.S_AXIS_ARB" xilinx:dependency="(spirit:decode(id(&apos;PARAM_VALUE.CHANNEL&apos;)) == 1 &amp;&amp; spirit:decode(id(&apos;PARAM_VALUE.NUM_SI&apos;)) > 1 )">false</xilinx:isEnabled>
          </xilinx:enablement>
        </xilinx:busInterfaceInfo>
      </spirit:vendorExtensions>
    </spirit:busInterface>
    <spirit:busInterface>
      <spirit:name>M_AXIS_ARB</spirit:name>
      <spirit:displayName>M_AXIS_ARB</spirit:displayName>
      <spirit:busType spirit:vendor="xilinx.com" spirit:library="interface" spirit:name="axis" spirit:version="1.0"/>
      <spirit:abstractionType spirit:vendor="xilinx.com" spirit:library="interface" spirit:name="axis_rtl" spirit:version="1.0"/>
      <spirit:master/>
      <spirit:portMaps>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>TDATA</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>m_axis_arb_tdata</spirit:name>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>TREADY</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>m_axis_arb_tready</spirit:name>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>TVALID</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>m_axis_arb_tvalid</spirit:name>
          </spirit:physicalPort>
        </spirit:portMap>
      </spirit:portMaps>
      <spirit:parameters>
        <spirit:parameter>
          <spirit:name>TDATA_NUM_BYTES</spirit:name>
          <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="BUSIFPARAM_VALUE.M_AXIS_ARB.TDATA_NUM_BYTES">1</spirit:value>
          <spirit:vendorExtensions>
            <xilinx:parameterInfo>
              <xilinx:parameterUsage>none</xilinx:parameterUsage>
            </xilinx:parameterInfo>
          </spirit:vendorExtensions>
        </spirit:parameter>
        <spirit:parameter>
          <spirit:name>TDEST_WIDTH</spirit:name>
          <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="BUSIFPARAM_VALUE.M_AXIS_ARB.TDEST_WIDTH">0</spirit:value>
          <spirit:vendorExtensions>
            <xilinx:parameterInfo>
              <xilinx:parameterUsage>none</xilinx:parameterUsage>
            </xilinx:parameterInfo>
          </spirit:vendorExtensions>
        </spirit:parameter>
        <spirit:parameter>
          <spirit:name>TID_WIDTH</spirit:name>
          <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="BUSIFPARAM_VALUE.M_AXIS_ARB.TID_WIDTH">0</spirit:value>
          <spirit:vendorExtensions>
            <xilinx:parameterInfo>
              <xilinx:parameterUsage>none</xilinx:parameterUsage>
            </xilinx:parameterInfo>
          </spirit:vendorExtensions>
        </spirit:parameter>
        <spirit:parameter>
          <spirit:name>TUSER_WIDTH</spirit:name>
          <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="BUSIFPARAM_VALUE.M_AXIS_ARB.TUSER_WIDTH">0</spirit:value>
          <spirit:vendorExtensions>
            <xilinx:parameterInfo>
              <xilinx:parameterUsage>none</xilinx:parameterUsage>
            </xilinx:parameterInfo>
          </spirit:vendorExtensions>
        </spirit:parameter>
        <spirit:parameter>
          <spirit:name>HAS_TREADY</spirit:name>
          <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="BUSIFPARAM_VALUE.M_AXIS_ARB.HAS_TREADY">1</spirit:value>
          <spirit:vendorExtensions>
            <xilinx:parameterInfo>
              <xilinx:parameterUsage>none</xilinx:parameterUsage>
            </xilinx:parameterInfo>
          </spirit:vendorExtensions>
        </spirit:parameter>
        <spirit:parameter>
          <spirit:name>HAS_TSTRB</spirit:name>
          <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="BUSIFPARAM_VALUE.M_AXIS_ARB.HAS_TSTRB">0</spirit:value>
          <spirit:vendorExtensions>
            <xilinx:parameterInfo>
              <xilinx:parameterUsage>none</xilinx:parameterUsage>
            </xilinx:parameterInfo>
          </spirit:vendorExtensions>
        </spirit:parameter>
        <spirit:parameter>
          <spirit:name>HAS_TKEEP</spirit:name>
          <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="BUSIFPARAM_VALUE.M_AXIS_ARB.HAS_TKEEP">0</spirit:value>
          <spirit:vendorExtensions>
            <xilinx:parameterInfo>
              <xilinx:parameterUsage>none</xilinx:parameterUsage>
            </xilinx:parameterInfo>
          </spirit:vendorExtensions>
        </spirit:parameter>
        <spirit:parameter>
          <spirit:name>HAS_TLAST</spirit:name>
          <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="BUSIFPARAM_VALUE.M_AXIS_ARB.HAS_TLAST">0</spirit:value>
          <spirit:vendorExtensions>
            <xilinx:parameterInfo>
              <xilinx:parameterUsage>none</xilinx:parameterUsage>
            </xilinx:parameterInfo>
          </spirit:vendorExtensions>
        </spirit:parameter>
        <spirit:parameter>
          <spirit:name>FREQ_HZ</spirit:name>
          <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="BUSIFPARAM_VALUE.M_AXIS_ARB.FREQ_HZ">100000000</spirit:value>
          <spirit:vendorExtensions>
            <xilinx:parameterInfo>
              <xilinx:parameterUsage>none</xilinx:parameterUsage>
            </xilinx:parameterInfo>
          </spirit:vendorExtensions>
        </spirit:parameter>
        <spirit:parameter>
          <spirit:name>PHASE</spirit:name>
          <spirit:value spirit:format="float" spirit:resolve="generated" spirit:id="BUSIFPARAM_VALUE.M_AXIS_ARB.PHASE">0.0</spirit:value>
          <spirit:vendorExtensions>
            <xilinx:parameterInfo>
              <xilinx:parameterUsage>none</xilinx:parameterUsage>
            </xilinx:parameterInfo>
          </spirit:vendorExtensions>
        </spirit:parameter>
        <spirit:parameter>
          <spirit:name>CLK_DOMAIN</spirit:name>
          <spirit:value spirit:resolve="generated" spirit:id="BUSIFPARAM_VALUE.M_AXIS_ARB.CLK_DOMAIN"/>
          <spirit:vendorExtensions>
            <xilinx:parameterInfo>
              <xilinx:parameterUsage>none</xilinx:parameterUsage>
            </xilinx:parameterInfo>
          </spirit:vendorExtensions>
        </spirit:parameter>
        <spirit:parameter>
          <spirit:name>LAYERED_METADATA</spirit:name>
          <spirit:value spirit:resolve="generated" spirit:id="BUSIFPARAM_VALUE.M_AXIS_ARB.LAYERED_METADATA">undef</spirit:value>
          <spirit:vendorExtensions>
            <xilinx:parameterInfo>
              <xilinx:parameterUsage>none</xilinx:parameterUsage>
            </xilinx:parameterInfo>
          </spirit:vendorExtensions>
        </spirit:parameter>
        <spirit:parameter>
          <spirit:name>INSERT_VIP</spirit:name>
          <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="BUSIFPARAM_VALUE.M_AXIS_ARB.INSERT_VIP">0</spirit:value>
          <spirit:vendorExtensions>
            <xilinx:parameterInfo>
              <xilinx:parameterUsage>simulation.rtl</xilinx:parameterUsage>
            </xilinx:parameterInfo>
          </spirit:vendorExtensions>
        </spirit:parameter>
      </spirit:parameters>
      <spirit:vendorExtensions>
        <xilinx:busInterfaceInfo>
          <xilinx:enablement>
            <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="BUSIF_ENABLEMENT.M_AXIS_ARB" xilinx:dependency="(spirit:decode(id(&apos;PARAM_VALUE.CHANNEL&apos;)) == 3 &amp;&amp; spirit:decode(id(&apos;PARAM_VALUE.NUM_SI&apos;)) > 1 )">false</xilinx:isEnabled>
          </xilinx:enablement>
        </xilinx:busInterfaceInfo>
      </spirit:vendorExtensions>
    </spirit:busInterface>
    <spirit:busInterface>
      <spirit:name>aclk</spirit:name>
      <spirit:displayName>ACLK</spirit:displayName>
      <spirit:busType spirit:vendor="xilinx.com" spirit:library="signal" spirit:name="clock" spirit:version="1.0"/>
      <spirit:abstractionType spirit:vendor="xilinx.com" spirit:library="signal" spirit:name="clock_rtl" spirit:version="1.0"/>
      <spirit:slave/>
      <spirit:portMaps>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>CLK</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>s_sc_aclk</spirit:name>
          </spirit:physicalPort>
        </spirit:portMap>
      </spirit:portMaps>
      <spirit:parameters>
        <spirit:parameter>
          <spirit:name>ASSOCIATED_BUSIF</spirit:name>
          <spirit:value spirit:id="BUSIFPARAM_VALUE.ACLK.ASSOCIATED_BUSIF">S_AXIS_ARB:M_AXIS_ARB:S_SC</spirit:value>
        </spirit:parameter>
        <spirit:parameter>
          <spirit:name>ASSOCIATED_RESET</spirit:name>
          <spirit:value spirit:id="BUSIFPARAM_VALUE.ACLK.ASSOCIATED_RESET">s_sc_aresetn</spirit:value>
        </spirit:parameter>
        <spirit:parameter>
          <spirit:name>ASSOCIATED_CLKEN</spirit:name>
          <spirit:value spirit:id="BUSIFPARAM_VALUE.ACLK.ASSOCIATED_CLKEN">s_sc_aclken</spirit:value>
        </spirit:parameter>
        <spirit:parameter>
          <spirit:name>FREQ_HZ</spirit:name>
          <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="BUSIFPARAM_VALUE.ACLK.FREQ_HZ">50000000</spirit:value>
          <spirit:vendorExtensions>
            <xilinx:parameterInfo>
              <xilinx:parameterUsage>none</xilinx:parameterUsage>
            </xilinx:parameterInfo>
          </spirit:vendorExtensions>
        </spirit:parameter>
        <spirit:parameter>
          <spirit:name>FREQ_TOLERANCE_HZ</spirit:name>
          <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="BUSIFPARAM_VALUE.ACLK.FREQ_TOLERANCE_HZ">0</spirit:value>
          <spirit:vendorExtensions>
            <xilinx:parameterInfo>
              <xilinx:parameterUsage>none</xilinx:parameterUsage>
            </xilinx:parameterInfo>
          </spirit:vendorExtensions>
        </spirit:parameter>
        <spirit:parameter>
          <spirit:name>PHASE</spirit:name>
          <spirit:value spirit:format="float" spirit:resolve="generated" spirit:id="BUSIFPARAM_VALUE.ACLK.PHASE">0.0</spirit:value>
          <spirit:vendorExtensions>
            <xilinx:parameterInfo>
              <xilinx:parameterUsage>none</xilinx:parameterUsage>
            </xilinx:parameterInfo>
          </spirit:vendorExtensions>
        </spirit:parameter>
        <spirit:parameter>
          <spirit:name>CLK_DOMAIN</spirit:name>
          <spirit:value spirit:resolve="generated" spirit:id="BUSIFPARAM_VALUE.ACLK.CLK_DOMAIN">ZynqDesign_processing_system7_0_0_FCLK_CLK0</spirit:value>
          <spirit:vendorExtensions>
            <xilinx:parameterInfo>
              <xilinx:parameterUsage>none</xilinx:parameterUsage>
            </xilinx:parameterInfo>
          </spirit:vendorExtensions>
        </spirit:parameter>
        <spirit:parameter>
          <spirit:name>ASSOCIATED_PORT</spirit:name>
          <spirit:value spirit:resolve="generated" spirit:id="BUSIFPARAM_VALUE.ACLK.ASSOCIATED_PORT"/>
          <spirit:vendorExtensions>
            <xilinx:parameterInfo>
              <xilinx:parameterUsage>none</xilinx:parameterUsage>
            </xilinx:parameterInfo>
          </spirit:vendorExtensions>
        </spirit:parameter>
        <spirit:parameter>
          <spirit:name>INSERT_VIP</spirit:name>
          <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="BUSIFPARAM_VALUE.ACLK.INSERT_VIP">0</spirit:value>
          <spirit:vendorExtensions>
            <xilinx:parameterInfo>
              <xilinx:parameterUsage>simulation.rtl</xilinx:parameterUsage>
            </xilinx:parameterInfo>
          </spirit:vendorExtensions>
        </spirit:parameter>
      </spirit:parameters>
    </spirit:busInterface>
    <spirit:busInterface>
      <spirit:name>aresetn</spirit:name>
      <spirit:displayName>ARESETN</spirit:displayName>
      <spirit:busType spirit:vendor="xilinx.com" spirit:library="signal" spirit:name="reset" spirit:version="1.0"/>
      <spirit:abstractionType spirit:vendor="xilinx.com" spirit:library="signal" spirit:name="reset_rtl" spirit:version="1.0"/>
      <spirit:slave/>
      <spirit:portMaps>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>RST</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>s_sc_aresetn</spirit:name>
          </spirit:physicalPort>
        </spirit:portMap>
      </spirit:portMaps>
      <spirit:parameters>
        <spirit:parameter>
          <spirit:name>POLARITY</spirit:name>
          <spirit:value spirit:id="BUSIFPARAM_VALUE.ARESETN.POLARITY">ACTIVE_LOW</spirit:value>
        </spirit:parameter>
        <spirit:parameter>
          <spirit:name>INSERT_VIP</spirit:name>
          <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="BUSIFPARAM_VALUE.ARESETN.INSERT_VIP">0</spirit:value>
          <spirit:vendorExtensions>
            <xilinx:parameterInfo>
              <xilinx:parameterUsage>simulation.rtl</xilinx:parameterUsage>
            </xilinx:parameterInfo>
          </spirit:vendorExtensions>
        </spirit:parameter>
      </spirit:parameters>
    </spirit:busInterface>
    <spirit:busInterface>
      <spirit:name>s_sc_aclken</spirit:name>
      <spirit:displayName>CE</spirit:displayName>
      <spirit:busType spirit:vendor="xilinx.com" spirit:library="signal" spirit:name="clockenable" spirit:version="1.0"/>
      <spirit:abstractionType spirit:vendor="xilinx.com" spirit:library="signal" spirit:name="clockenable_rtl" spirit:version="1.0"/>
      <spirit:slave/>
      <spirit:portMaps>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>CE</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>s_sc_aclken</spirit:name>
          </spirit:physicalPort>
        </spirit:portMap>
      </spirit:portMaps>
      <spirit:parameters>
        <spirit:parameter>
          <spirit:name>POLARITY</spirit:name>
          <spirit:value spirit:resolve="generated" spirit:id="BUSIFPARAM_VALUE.S_SC_ACLKEN.POLARITY">ACTIVE_LOW</spirit:value>
          <spirit:vendorExtensions>
            <xilinx:parameterInfo>
              <xilinx:parameterUsage>none</xilinx:parameterUsage>
            </xilinx:parameterInfo>
          </spirit:vendorExtensions>
        </spirit:parameter>
      </spirit:parameters>
      <spirit:vendorExtensions>
        <xilinx:busInterfaceInfo>
          <xilinx:enablement>
            <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="BUSIF_ENABLEMENT.s_sc_aclken" xilinx:dependency="(spirit:decode(id(&apos;PARAM_VALUE.ACLKEN_CONVERSION&apos;)) != 0 &amp;&amp; spirit:decode(id(&apos;PARAM_VALUE.ACLKEN_CONVERSION&apos;)) != 2 )">false</xilinx:isEnabled>
          </xilinx:enablement>
        </xilinx:busInterfaceInfo>
      </spirit:vendorExtensions>
    </spirit:busInterface>
    <spirit:busInterface>
      <spirit:name>m_sc_aclk</spirit:name>
      <spirit:displayName>M_SC_ACLK</spirit:displayName>
      <spirit:busType spirit:vendor="xilinx.com" spirit:library="signal" spirit:name="clock" spirit:version="1.0"/>
      <spirit:abstractionType spirit:vendor="xilinx.com" spirit:library="signal" spirit:name="clock_rtl" spirit:version="1.0"/>
      <spirit:slave/>
      <spirit:portMaps>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>CLK</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>m_sc_aclk</spirit:name>
          </spirit:physicalPort>
        </spirit:portMap>
      </spirit:portMaps>
      <spirit:parameters>
        <spirit:parameter>
          <spirit:name>ASSOCIATED_BUSIF</spirit:name>
          <spirit:value spirit:id="BUSIFPARAM_VALUE.M_SC_ACLK.ASSOCIATED_BUSIF">M_SC</spirit:value>
        </spirit:parameter>
        <spirit:parameter>
          <spirit:name>ASSOCIATED_RESET</spirit:name>
          <spirit:value spirit:id="BUSIFPARAM_VALUE.M_SC_ACLK.ASSOCIATED_RESET">m_sc_aresetn</spirit:value>
        </spirit:parameter>
        <spirit:parameter>
          <spirit:name>ASSOCIATED_CLKEN</spirit:name>
          <spirit:value spirit:id="BUSIFPARAM_VALUE.M_SC_ACLK.ASSOCIATED_CLKEN">m_sc_aclken</spirit:value>
        </spirit:parameter>
        <spirit:parameter>
          <spirit:name>FREQ_HZ</spirit:name>
          <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="BUSIFPARAM_VALUE.M_SC_ACLK.FREQ_HZ">50000000</spirit:value>
          <spirit:vendorExtensions>
            <xilinx:parameterInfo>
              <xilinx:parameterUsage>none</xilinx:parameterUsage>
            </xilinx:parameterInfo>
          </spirit:vendorExtensions>
        </spirit:parameter>
        <spirit:parameter>
          <spirit:name>FREQ_TOLERANCE_HZ</spirit:name>
          <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="BUSIFPARAM_VALUE.M_SC_ACLK.FREQ_TOLERANCE_HZ">0</spirit:value>
          <spirit:vendorExtensions>
            <xilinx:parameterInfo>
              <xilinx:parameterUsage>none</xilinx:parameterUsage>
            </xilinx:parameterInfo>
          </spirit:vendorExtensions>
        </spirit:parameter>
        <spirit:parameter>
          <spirit:name>PHASE</spirit:name>
          <spirit:value spirit:format="float" spirit:resolve="generated" spirit:id="BUSIFPARAM_VALUE.M_SC_ACLK.PHASE">0.0</spirit:value>
          <spirit:vendorExtensions>
            <xilinx:parameterInfo>
              <xilinx:parameterUsage>none</xilinx:parameterUsage>
            </xilinx:parameterInfo>
          </spirit:vendorExtensions>
        </spirit:parameter>
        <spirit:parameter>
          <spirit:name>CLK_DOMAIN</spirit:name>
          <spirit:value spirit:resolve="generated" spirit:id="BUSIFPARAM_VALUE.M_SC_ACLK.CLK_DOMAIN">ZynqDesign_processing_system7_0_0_FCLK_CLK0</spirit:value>
          <spirit:vendorExtensions>
            <xilinx:parameterInfo>
              <xilinx:parameterUsage>none</xilinx:parameterUsage>
            </xilinx:parameterInfo>
          </spirit:vendorExtensions>
        </spirit:parameter>
        <spirit:parameter>
          <spirit:name>ASSOCIATED_PORT</spirit:name>
          <spirit:value spirit:resolve="generated" spirit:id="BUSIFPARAM_VALUE.M_SC_ACLK.ASSOCIATED_PORT"/>
          <spirit:vendorExtensions>
            <xilinx:parameterInfo>
              <xilinx:parameterUsage>none</xilinx:parameterUsage>
            </xilinx:parameterInfo>
          </spirit:vendorExtensions>
        </spirit:parameter>
        <spirit:parameter>
          <spirit:name>INSERT_VIP</spirit:name>
          <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="BUSIFPARAM_VALUE.M_SC_ACLK.INSERT_VIP">0</spirit:value>
          <spirit:vendorExtensions>
            <xilinx:parameterInfo>
              <xilinx:parameterUsage>simulation.rtl</xilinx:parameterUsage>
            </xilinx:parameterInfo>
          </spirit:vendorExtensions>
        </spirit:parameter>
      </spirit:parameters>
    </spirit:busInterface>
    <spirit:busInterface>
      <spirit:name>m_sc_aresetn</spirit:name>
      <spirit:displayName>M_SC_ARESETN</spirit:displayName>
      <spirit:busType spirit:vendor="xilinx.com" spirit:library="signal" spirit:name="reset" spirit:version="1.0"/>
      <spirit:abstractionType spirit:vendor="xilinx.com" spirit:library="signal" spirit:name="reset_rtl" spirit:version="1.0"/>
      <spirit:slave/>
      <spirit:portMaps>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>RST</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>m_sc_aresetn</spirit:name>
          </spirit:physicalPort>
        </spirit:portMap>
      </spirit:portMaps>
      <spirit:parameters>
        <spirit:parameter>
          <spirit:name>POLARITY</spirit:name>
          <spirit:value spirit:id="BUSIFPARAM_VALUE.M_SC_ARESETN.POLARITY">ACTIVE_LOW</spirit:value>
        </spirit:parameter>
        <spirit:parameter>
          <spirit:name>INSERT_VIP</spirit:name>
          <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="BUSIFPARAM_VALUE.M_SC_ARESETN.INSERT_VIP">0</spirit:value>
          <spirit:vendorExtensions>
            <xilinx:parameterInfo>
              <xilinx:parameterUsage>simulation.rtl</xilinx:parameterUsage>
            </xilinx:parameterInfo>
          </spirit:vendorExtensions>
        </spirit:parameter>
      </spirit:parameters>
    </spirit:busInterface>
    <spirit:busInterface>
      <spirit:name>m_sc_aclken</spirit:name>
      <spirit:displayName>M_SC_CE</spirit:displayName>
      <spirit:busType spirit:vendor="xilinx.com" spirit:library="signal" spirit:name="clockenable" spirit:version="1.0"/>
      <spirit:abstractionType spirit:vendor="xilinx.com" spirit:library="signal" spirit:name="clockenable_rtl" spirit:version="1.0"/>
      <spirit:slave/>
      <spirit:portMaps>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>CE</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>m_sc_aclken</spirit:name>
          </spirit:physicalPort>
        </spirit:portMap>
      </spirit:portMaps>
      <spirit:parameters>
        <spirit:parameter>
          <spirit:name>POLARITY</spirit:name>
          <spirit:value spirit:resolve="generated" spirit:id="BUSIFPARAM_VALUE.M_SC_ACLKEN.POLARITY">ACTIVE_LOW</spirit:value>
          <spirit:vendorExtensions>
            <xilinx:parameterInfo>
              <xilinx:parameterUsage>none</xilinx:parameterUsage>
            </xilinx:parameterInfo>
          </spirit:vendorExtensions>
        </spirit:parameter>
      </spirit:parameters>
      <spirit:vendorExtensions>
        <xilinx:busInterfaceInfo>
          <xilinx:enablement>
            <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="BUSIF_ENABLEMENT.m_sc_aclken" xilinx:dependency="(spirit:decode(id(&apos;PARAM_VALUE.ACLKEN_CONVERSION&apos;)) != 0 &amp;&amp; spirit:decode(id(&apos;PARAM_VALUE.ACLKEN_CONVERSION&apos;)) != 1 )">false</xilinx:isEnabled>
          </xilinx:enablement>
        </xilinx:busInterfaceInfo>
      </spirit:vendorExtensions>
    </spirit:busInterface>
  </spirit:busInterfaces>
  <spirit:model>
    <spirit:views>
      <spirit:view>
        <spirit:name>xilinx_synthesisconstraints</spirit:name>
        <spirit:displayName>Synthesis Constraints</spirit:displayName>
        <spirit:envIdentifier>:vivado.xilinx.com:synthesis.constraints</spirit:envIdentifier>
        <spirit:parameters>
          <spirit:parameter>
            <spirit:name>outputProductCRC</spirit:name>
            <spirit:value>9:06fd950c</spirit:value>
          </spirit:parameter>
        </spirit:parameters>
      </spirit:view>
      <spirit:view>
        <spirit:name>xilinx_verilogbehavioralsimulation</spirit:name>
        <spirit:displayName>Verilog Simulation</spirit:displayName>
        <spirit:envIdentifier>verilogSource:vivado.xilinx.com:simulation</spirit:envIdentifier>
        <spirit:language>verilog</spirit:language>
        <spirit:modelName>sc_node_v1_0_17_top</spirit:modelName>
        <spirit:fileSetRef>
          <spirit:localName>xilinx_verilogbehavioralsimulation_xilinx_com_ip_sc_util_1_0__ref_view_fileset</spirit:localName>
        </spirit:fileSetRef>
        <spirit:fileSetRef>
          <spirit:localName>xilinx_verilogbehavioralsimulation_view_fileset</spirit:localName>
        </spirit:fileSetRef>
        <spirit:parameters>
          <spirit:parameter>
            <spirit:name>GENtimestamp</spirit:name>
            <spirit:value>Sun Jun 01 14:00:06 UTC 2025</spirit:value>
          </spirit:parameter>
          <spirit:parameter>
            <spirit:name>outputProductCRC</spirit:name>
            <spirit:value>9:236fcbd2</spirit:value>
          </spirit:parameter>
        </spirit:parameters>
      </spirit:view>
      <spirit:view>
        <spirit:name>xilinx_verilogsimulationwrapper</spirit:name>
        <spirit:displayName>Verilog Simulation Wrapper</spirit:displayName>
        <spirit:envIdentifier>verilogSource:vivado.xilinx.com:simulation.wrapper</spirit:envIdentifier>
        <spirit:language>verilog</spirit:language>
        <spirit:modelName>bd_2aab_bni_3</spirit:modelName>
        <spirit:fileSetRef>
          <spirit:localName>xilinx_verilogsimulationwrapper_view_fileset</spirit:localName>
        </spirit:fileSetRef>
        <spirit:parameters>
          <spirit:parameter>
            <spirit:name>GENtimestamp</spirit:name>
            <spirit:value>Wed Jul 02 11:55:21 UTC 2025</spirit:value>
          </spirit:parameter>
          <spirit:parameter>
            <spirit:name>outputProductCRC</spirit:name>
            <spirit:value>9:236fcbd2</spirit:value>
          </spirit:parameter>
        </spirit:parameters>
      </spirit:view>
      <spirit:view>
        <spirit:name>xilinx_verilogsynthesis</spirit:name>
        <spirit:displayName>Verilog Synthesis</spirit:displayName>
        <spirit:envIdentifier>verilogSource:vivado.xilinx.com:synthesis</spirit:envIdentifier>
        <spirit:language>verilog</spirit:language>
        <spirit:modelName>sc_node_v1_0_17_top</spirit:modelName>
        <spirit:fileSetRef>
          <spirit:localName>xilinx_verilogsynthesis_xilinx_com_ip_sc_util_1_0__ref_view_fileset</spirit:localName>
        </spirit:fileSetRef>
        <spirit:fileSetRef>
          <spirit:localName>xilinx_verilogsynthesis_view_fileset</spirit:localName>
        </spirit:fileSetRef>
        <spirit:parameters>
          <spirit:parameter>
            <spirit:name>GENtimestamp</spirit:name>
            <spirit:value>Sun Jun 01 14:00:06 UTC 2025</spirit:value>
          </spirit:parameter>
          <spirit:parameter>
            <spirit:name>outputProductCRC</spirit:name>
            <spirit:value>9:06fd950c</spirit:value>
          </spirit:parameter>
        </spirit:parameters>
      </spirit:view>
      <spirit:view>
        <spirit:name>xilinx_verilogsynthesiswrapper</spirit:name>
        <spirit:displayName>Verilog Synthesis Wrapper</spirit:displayName>
        <spirit:envIdentifier>verilogSource:vivado.xilinx.com:synthesis.wrapper</spirit:envIdentifier>
        <spirit:language>verilog</spirit:language>
        <spirit:modelName>bd_2aab_bni_3</spirit:modelName>
        <spirit:fileSetRef>
          <spirit:localName>xilinx_verilogsynthesiswrapper_view_fileset</spirit:localName>
        </spirit:fileSetRef>
        <spirit:parameters>
          <spirit:parameter>
            <spirit:name>GENtimestamp</spirit:name>
            <spirit:value>Wed Jul 02 11:55:21 UTC 2025</spirit:value>
          </spirit:parameter>
          <spirit:parameter>
            <spirit:name>outputProductCRC</spirit:name>
            <spirit:value>9:06fd950c</spirit:value>
          </spirit:parameter>
        </spirit:parameters>
      </spirit:view>
    </spirit:views>
    <spirit:ports>
      <spirit:port>
        <spirit:name>s_sc_aclk</spirit:name>
        <spirit:wire>
          <spirit:direction>in</spirit:direction>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>wire</spirit:typeName>
              <spirit:viewNameRef>xilinx_verilogsynthesis</spirit:viewNameRef>
              <spirit:viewNameRef>xilinx_verilogbehavioralsimulation</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
        </spirit:wire>
      </spirit:port>
      <spirit:port>
        <spirit:name>s_sc_aclken</spirit:name>
        <spirit:wire>
          <spirit:direction>in</spirit:direction>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>wire</spirit:typeName>
              <spirit:viewNameRef>xilinx_verilogsynthesis</spirit:viewNameRef>
              <spirit:viewNameRef>xilinx_verilogbehavioralsimulation</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
          <spirit:driver>
            <spirit:defaultValue spirit:format="bitString" spirit:bitStringLength="1">0x1</spirit:defaultValue>
          </spirit:driver>
        </spirit:wire>
        <spirit:vendorExtensions>
          <xilinx:portInfo>
            <xilinx:enablement>
              <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="PORT_ENABLEMENT.s_sc_aclken" xilinx:dependency="(spirit:decode(id(&apos;PARAM_VALUE.ACLKEN_CONVERSION&apos;)) != 0 &amp;&amp; spirit:decode(id(&apos;PARAM_VALUE.ACLKEN_CONVERSION&apos;)) != 2 )">false</xilinx:isEnabled>
            </xilinx:enablement>
          </xilinx:portInfo>
        </spirit:vendorExtensions>
      </spirit:port>
      <spirit:port>
        <spirit:name>s_sc_aresetn</spirit:name>
        <spirit:wire>
          <spirit:direction>in</spirit:direction>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>wire</spirit:typeName>
              <spirit:viewNameRef>xilinx_verilogsynthesis</spirit:viewNameRef>
              <spirit:viewNameRef>xilinx_verilogbehavioralsimulation</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
          <spirit:driver>
            <spirit:defaultValue spirit:format="bitString" spirit:bitStringLength="1">0x1</spirit:defaultValue>
          </spirit:driver>
        </spirit:wire>
      </spirit:port>
      <spirit:port>
        <spirit:name>s_sc_req</spirit:name>
        <spirit:wire>
          <spirit:direction>in</spirit:direction>
          <spirit:vector>
            <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="(spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_SI&apos;)) - 1)">2</spirit:left>
            <spirit:right spirit:format="long">0</spirit:right>
          </spirit:vector>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>wire</spirit:typeName>
              <spirit:viewNameRef>xilinx_verilogsynthesis</spirit:viewNameRef>
              <spirit:viewNameRef>xilinx_verilogbehavioralsimulation</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
          <spirit:driver>
            <spirit:defaultValue spirit:format="bitString">0</spirit:defaultValue>
          </spirit:driver>
        </spirit:wire>
      </spirit:port>
      <spirit:port>
        <spirit:name>s_sc_info</spirit:name>
        <spirit:wire>
          <spirit:direction>in</spirit:direction>
          <spirit:vector>
            <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="((spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_SI&apos;)) * 1) - 1)">2</spirit:left>
            <spirit:right spirit:format="long">0</spirit:right>
          </spirit:vector>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>wire</spirit:typeName>
              <spirit:viewNameRef>xilinx_verilogsynthesis</spirit:viewNameRef>
              <spirit:viewNameRef>xilinx_verilogbehavioralsimulation</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
          <spirit:driver>
            <spirit:defaultValue spirit:format="bitString">0</spirit:defaultValue>
          </spirit:driver>
        </spirit:wire>
      </spirit:port>
      <spirit:port>
        <spirit:name>s_sc_send</spirit:name>
        <spirit:wire>
          <spirit:direction>in</spirit:direction>
          <spirit:vector>
            <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="(spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_SI&apos;)) - 1)">2</spirit:left>
            <spirit:right spirit:format="long">0</spirit:right>
          </spirit:vector>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>wire</spirit:typeName>
              <spirit:viewNameRef>xilinx_verilogsynthesis</spirit:viewNameRef>
              <spirit:viewNameRef>xilinx_verilogbehavioralsimulation</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
        </spirit:wire>
      </spirit:port>
      <spirit:port>
        <spirit:name>s_sc_recv</spirit:name>
        <spirit:wire>
          <spirit:direction>out</spirit:direction>
          <spirit:vector>
            <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="(spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_SI&apos;)) - 1)">2</spirit:left>
            <spirit:right spirit:format="long">0</spirit:right>
          </spirit:vector>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>logic</spirit:typeName>
              <spirit:viewNameRef>xilinx_verilogsynthesis</spirit:viewNameRef>
              <spirit:viewNameRef>xilinx_verilogbehavioralsimulation</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
        </spirit:wire>
      </spirit:port>
      <spirit:port>
        <spirit:name>s_sc_payld</spirit:name>
        <spirit:wire>
          <spirit:direction>in</spirit:direction>
          <spirit:vector>
            <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="(spirit:decode(id(&apos;MODELPARAM_VALUE.C_PAYLD_WIDTH&apos;)) - 1)">8</spirit:left>
            <spirit:right spirit:format="long">0</spirit:right>
          </spirit:vector>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>wire</spirit:typeName>
              <spirit:viewNameRef>xilinx_verilogsynthesis</spirit:viewNameRef>
              <spirit:viewNameRef>xilinx_verilogbehavioralsimulation</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
        </spirit:wire>
      </spirit:port>
      <spirit:port>
        <spirit:name>m_sc_aclk</spirit:name>
        <spirit:wire>
          <spirit:direction>in</spirit:direction>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>wire</spirit:typeName>
              <spirit:viewNameRef>xilinx_verilogsynthesis</spirit:viewNameRef>
              <spirit:viewNameRef>xilinx_verilogbehavioralsimulation</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
        </spirit:wire>
      </spirit:port>
      <spirit:port>
        <spirit:name>m_sc_aclken</spirit:name>
        <spirit:wire>
          <spirit:direction>in</spirit:direction>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>wire</spirit:typeName>
              <spirit:viewNameRef>xilinx_verilogsynthesis</spirit:viewNameRef>
              <spirit:viewNameRef>xilinx_verilogbehavioralsimulation</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
          <spirit:driver>
            <spirit:defaultValue spirit:format="bitString" spirit:bitStringLength="1">0x1</spirit:defaultValue>
          </spirit:driver>
        </spirit:wire>
        <spirit:vendorExtensions>
          <xilinx:portInfo>
            <xilinx:enablement>
              <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="PORT_ENABLEMENT.m_sc_aclken" xilinx:dependency="(spirit:decode(id(&apos;PARAM_VALUE.ACLKEN_CONVERSION&apos;)) != 0 &amp;&amp; spirit:decode(id(&apos;PARAM_VALUE.ACLKEN_CONVERSION&apos;)) != 1 )">false</xilinx:isEnabled>
            </xilinx:enablement>
          </xilinx:portInfo>
        </spirit:vendorExtensions>
      </spirit:port>
      <spirit:port>
        <spirit:name>m_sc_aresetn</spirit:name>
        <spirit:wire>
          <spirit:direction>in</spirit:direction>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>wire</spirit:typeName>
              <spirit:viewNameRef>xilinx_verilogsynthesis</spirit:viewNameRef>
              <spirit:viewNameRef>xilinx_verilogbehavioralsimulation</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
          <spirit:driver>
            <spirit:defaultValue spirit:format="bitString" spirit:bitStringLength="1">0x1</spirit:defaultValue>
          </spirit:driver>
        </spirit:wire>
      </spirit:port>
      <spirit:port>
        <spirit:name>m_sc_recv</spirit:name>
        <spirit:wire>
          <spirit:direction>in</spirit:direction>
          <spirit:vector>
            <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="(spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_MI&apos;)) - 1)">0</spirit:left>
            <spirit:right spirit:format="long">0</spirit:right>
          </spirit:vector>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>wire</spirit:typeName>
              <spirit:viewNameRef>xilinx_verilogsynthesis</spirit:viewNameRef>
              <spirit:viewNameRef>xilinx_verilogbehavioralsimulation</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
        </spirit:wire>
      </spirit:port>
      <spirit:port>
        <spirit:name>m_sc_send</spirit:name>
        <spirit:wire>
          <spirit:direction>out</spirit:direction>
          <spirit:vector>
            <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="(spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_MI&apos;)) - 1)">0</spirit:left>
            <spirit:right spirit:format="long">0</spirit:right>
          </spirit:vector>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>logic</spirit:typeName>
              <spirit:viewNameRef>xilinx_verilogsynthesis</spirit:viewNameRef>
              <spirit:viewNameRef>xilinx_verilogbehavioralsimulation</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
        </spirit:wire>
      </spirit:port>
      <spirit:port>
        <spirit:name>m_sc_req</spirit:name>
        <spirit:wire>
          <spirit:direction>out</spirit:direction>
          <spirit:vector>
            <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="(spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_MI&apos;)) - 1)">0</spirit:left>
            <spirit:right spirit:format="long">0</spirit:right>
          </spirit:vector>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>logic</spirit:typeName>
              <spirit:viewNameRef>xilinx_verilogsynthesis</spirit:viewNameRef>
              <spirit:viewNameRef>xilinx_verilogbehavioralsimulation</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
        </spirit:wire>
      </spirit:port>
      <spirit:port>
        <spirit:name>m_sc_info</spirit:name>
        <spirit:wire>
          <spirit:direction>out</spirit:direction>
          <spirit:vector>
            <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="((spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_MI&apos;)) * 1) - 1)">0</spirit:left>
            <spirit:right spirit:format="long">0</spirit:right>
          </spirit:vector>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>logic</spirit:typeName>
              <spirit:viewNameRef>xilinx_verilogsynthesis</spirit:viewNameRef>
              <spirit:viewNameRef>xilinx_verilogbehavioralsimulation</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
        </spirit:wire>
      </spirit:port>
      <spirit:port>
        <spirit:name>m_sc_payld</spirit:name>
        <spirit:wire>
          <spirit:direction>out</spirit:direction>
          <spirit:vector>
            <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="(spirit:decode(id(&apos;MODELPARAM_VALUE.C_PAYLD_WIDTH&apos;)) - 1)">8</spirit:left>
            <spirit:right spirit:format="long">0</spirit:right>
          </spirit:vector>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>logic</spirit:typeName>
              <spirit:viewNameRef>xilinx_verilogsynthesis</spirit:viewNameRef>
              <spirit:viewNameRef>xilinx_verilogbehavioralsimulation</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
        </spirit:wire>
      </spirit:port>
      <spirit:port>
        <spirit:name>m_axis_arb_tvalid</spirit:name>
        <spirit:wire>
          <spirit:direction>out</spirit:direction>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>wire</spirit:typeName>
              <spirit:viewNameRef>xilinx_verilogsynthesis</spirit:viewNameRef>
              <spirit:viewNameRef>xilinx_verilogbehavioralsimulation</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
        </spirit:wire>
        <spirit:vendorExtensions>
          <xilinx:portInfo>
            <xilinx:enablement>
              <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="PORT_ENABLEMENT.m_axis_arb_tvalid" xilinx:dependency="(spirit:decode(id(&apos;PARAM_VALUE.CHANNEL&apos;)) == 3 &amp;&amp; spirit:decode(id(&apos;PARAM_VALUE.NUM_SI&apos;)) > 1 )">false</xilinx:isEnabled>
            </xilinx:enablement>
          </xilinx:portInfo>
        </spirit:vendorExtensions>
      </spirit:port>
      <spirit:port>
        <spirit:name>m_axis_arb_tready</spirit:name>
        <spirit:wire>
          <spirit:direction>in</spirit:direction>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>wire</spirit:typeName>
              <spirit:viewNameRef>xilinx_verilogsynthesis</spirit:viewNameRef>
              <spirit:viewNameRef>xilinx_verilogbehavioralsimulation</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
          <spirit:driver>
            <spirit:defaultValue spirit:format="bitString" spirit:bitStringLength="1">0x1</spirit:defaultValue>
          </spirit:driver>
        </spirit:wire>
        <spirit:vendorExtensions>
          <xilinx:portInfo>
            <xilinx:enablement>
              <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="PORT_ENABLEMENT.m_axis_arb_tready" xilinx:dependency="(spirit:decode(id(&apos;PARAM_VALUE.CHANNEL&apos;)) == 3 &amp;&amp; spirit:decode(id(&apos;PARAM_VALUE.NUM_SI&apos;)) > 1 )">false</xilinx:isEnabled>
            </xilinx:enablement>
          </xilinx:portInfo>
        </spirit:vendorExtensions>
      </spirit:port>
      <spirit:port>
        <spirit:name>m_axis_arb_tdata</spirit:name>
        <spirit:wire>
          <spirit:direction>out</spirit:direction>
          <spirit:vector>
            <spirit:left spirit:format="long">15</spirit:left>
            <spirit:right spirit:format="long">0</spirit:right>
          </spirit:vector>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>wire</spirit:typeName>
              <spirit:viewNameRef>xilinx_verilogsynthesis</spirit:viewNameRef>
              <spirit:viewNameRef>xilinx_verilogbehavioralsimulation</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
        </spirit:wire>
        <spirit:vendorExtensions>
          <xilinx:portInfo>
            <xilinx:enablement>
              <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="PORT_ENABLEMENT.m_axis_arb_tdata" xilinx:dependency="(spirit:decode(id(&apos;PARAM_VALUE.CHANNEL&apos;)) == 3 &amp;&amp; spirit:decode(id(&apos;PARAM_VALUE.NUM_SI&apos;)) > 1 )">false</xilinx:isEnabled>
            </xilinx:enablement>
          </xilinx:portInfo>
        </spirit:vendorExtensions>
      </spirit:port>
      <spirit:port>
        <spirit:name>s_axis_arb_tvalid</spirit:name>
        <spirit:wire>
          <spirit:direction>in</spirit:direction>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>wire</spirit:typeName>
              <spirit:viewNameRef>xilinx_verilogsynthesis</spirit:viewNameRef>
              <spirit:viewNameRef>xilinx_verilogbehavioralsimulation</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
          <spirit:driver>
            <spirit:defaultValue spirit:format="bitString" spirit:bitStringLength="1">0x0</spirit:defaultValue>
          </spirit:driver>
        </spirit:wire>
        <spirit:vendorExtensions>
          <xilinx:portInfo>
            <xilinx:enablement>
              <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="PORT_ENABLEMENT.s_axis_arb_tvalid" xilinx:dependency="(spirit:decode(id(&apos;PARAM_VALUE.CHANNEL&apos;)) == 1 &amp;&amp; spirit:decode(id(&apos;PARAM_VALUE.NUM_SI&apos;)) > 1 )">false</xilinx:isEnabled>
            </xilinx:enablement>
          </xilinx:portInfo>
        </spirit:vendorExtensions>
      </spirit:port>
      <spirit:port>
        <spirit:name>s_axis_arb_tready</spirit:name>
        <spirit:wire>
          <spirit:direction>out</spirit:direction>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>wire</spirit:typeName>
              <spirit:viewNameRef>xilinx_verilogsynthesis</spirit:viewNameRef>
              <spirit:viewNameRef>xilinx_verilogbehavioralsimulation</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
        </spirit:wire>
        <spirit:vendorExtensions>
          <xilinx:portInfo>
            <xilinx:enablement>
              <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="PORT_ENABLEMENT.s_axis_arb_tready" xilinx:dependency="(spirit:decode(id(&apos;PARAM_VALUE.CHANNEL&apos;)) == 1 &amp;&amp; spirit:decode(id(&apos;PARAM_VALUE.NUM_SI&apos;)) > 1 )">false</xilinx:isEnabled>
            </xilinx:enablement>
          </xilinx:portInfo>
        </spirit:vendorExtensions>
      </spirit:port>
      <spirit:port>
        <spirit:name>s_axis_arb_tdata</spirit:name>
        <spirit:wire>
          <spirit:direction>in</spirit:direction>
          <spirit:vector>
            <spirit:left spirit:format="long">15</spirit:left>
            <spirit:right spirit:format="long">0</spirit:right>
          </spirit:vector>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>wire</spirit:typeName>
              <spirit:viewNameRef>xilinx_verilogsynthesis</spirit:viewNameRef>
              <spirit:viewNameRef>xilinx_verilogbehavioralsimulation</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
          <spirit:driver>
            <spirit:defaultValue spirit:format="long" spirit:bitStringLength="16">0</spirit:defaultValue>
          </spirit:driver>
        </spirit:wire>
        <spirit:vendorExtensions>
          <xilinx:portInfo>
            <xilinx:enablement>
              <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="PORT_ENABLEMENT.s_axis_arb_tdata" xilinx:dependency="(spirit:decode(id(&apos;PARAM_VALUE.CHANNEL&apos;)) == 1 &amp;&amp; spirit:decode(id(&apos;PARAM_VALUE.NUM_SI&apos;)) > 1 )">false</xilinx:isEnabled>
            </xilinx:enablement>
          </xilinx:portInfo>
        </spirit:vendorExtensions>
      </spirit:port>
    </spirit:ports>
    <spirit:modelParameters>
      <spirit:modelParameter xsi:type="spirit:nameValueTypeType" spirit:dataType="string">
        <spirit:name>C_FAMILY</spirit:name>
        <spirit:value spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_FAMILY">zynq</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="long">
        <spirit:name>C_FIFO_IP</spirit:name>
        <spirit:displayName>C_FIFO_IP</spirit:displayName>
        <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_FIFO_IP" spirit:choiceRef="choice_list_11ba48e0">0</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="integer">
        <spirit:name>C_DISABLE_IP</spirit:name>
        <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_DISABLE_IP" spirit:minimum="0" spirit:maximum="1" spirit:rangeType="long">0</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="long">
        <spirit:name>C_FIFO_SIZE</spirit:name>
        <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_FIFO_SIZE" spirit:minimum="0" spirit:maximum="12" spirit:rangeType="long">0</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="long">
        <spirit:name>C_FIFO_TYPE</spirit:name>
        <spirit:displayName>C_FIFO_TYPE</spirit:displayName>
        <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_FIFO_TYPE" spirit:minimum="0" spirit:maximum="4" spirit:rangeType="long">0</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="integer">
        <spirit:name>C_FIFO_OUTPUT_REG</spirit:name>
        <spirit:displayName>FIFO Register</spirit:displayName>
        <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_FIFO_OUTPUT_REG" spirit:minimum="0" spirit:maximum="1" spirit:rangeType="long">1</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="bitstring">
        <spirit:name>C_ENABLE_PIPELINING</spirit:name>
        <spirit:displayName>C_ENABLE_PIPELINING</spirit:displayName>
        <spirit:value spirit:format="bitString" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_ENABLE_PIPELINING" spirit:bitStringLength="8">0x01</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="long">
        <spirit:name>C_SYNCHRONIZATION_STAGES</spirit:name>
        <spirit:displayName>C_SYNCHRONIZATION_STAGES</spirit:displayName>
        <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_SYNCHRONIZATION_STAGES" spirit:minimum="2" spirit:maximum="8" spirit:rangeType="long">3</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="long">
        <spirit:name>C_NUM_SI</spirit:name>
        <spirit:displayName>C_NUM_SI</spirit:displayName>
        <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_NUM_SI" spirit:minimum="1" spirit:maximum="16" spirit:rangeType="long">3</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="long">
        <spirit:name>C_NUM_MI</spirit:name>
        <spirit:displayName>C_NUM_MI</spirit:displayName>
        <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_NUM_MI" spirit:minimum="1" spirit:maximum="16" spirit:rangeType="long">1</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="long">
        <spirit:name>C_CHANNEL</spirit:name>
        <spirit:displayName>C_CHANNEL</spirit:displayName>
        <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_CHANNEL" spirit:minimum="0" spirit:maximum="4" spirit:rangeType="long">4</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="long">
        <spirit:name>C_PAYLD_WIDTH</spirit:name>
        <spirit:displayName>C_PAYLD_WIDTH</spirit:displayName>
        <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_PAYLD_WIDTH" spirit:minimum="1" spirit:maximum="1854" spirit:rangeType="long">9</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="bitstring">
        <spirit:name>C_S_NUM_BYTES_ARRAY</spirit:name>
        <spirit:displayName>C_S_NUM_BYTES_ARRAY</spirit:displayName>
        <spirit:value spirit:format="bitString" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_S_NUM_BYTES_ARRAY" spirit:bitStringLength="96">0x000000040000000400000004</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="bitstring">
        <spirit:name>C_M_NUM_BYTES_ARRAY</spirit:name>
        <spirit:displayName>C_M_NUM_BYTES_ARRAY</spirit:displayName>
        <spirit:value spirit:format="bitString" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_M_NUM_BYTES_ARRAY" spirit:bitStringLength="32">0x00000004</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="bitstring">
        <spirit:name>C_PRIORITY_ARB_ARRAY</spirit:name>
        <spirit:displayName>C_PRIORITY_ARB</spirit:displayName>
        <spirit:value spirit:format="bitString" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_PRIORITY_ARB_ARRAY" spirit:bitStringLength="3">0b000</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="long">
        <spirit:name>C_USER_BITS_PER_BYTE</spirit:name>
        <spirit:displayName>C_USER_BITS_PER_BYTE</spirit:displayName>
        <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_USER_BITS_PER_BYTE" spirit:minimum="0" spirit:maximum="4" spirit:rangeType="long">0</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="long">
        <spirit:name>C_ARBITER_MODE</spirit:name>
        <spirit:displayName>C_ARBITER_MODE</spirit:displayName>
        <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_ARBITER_MODE" spirit:minimum="0" spirit:maximum="1" spirit:rangeType="long">1</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="long">
        <spirit:name>C_SC_ROUTE_WIDTH</spirit:name>
        <spirit:displayName>C_SC_ROUTE_WIDTH</spirit:displayName>
        <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_SC_ROUTE_WIDTH" spirit:minimum="1" spirit:maximum="64" spirit:rangeType="long">3</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="long">
        <spirit:name>C_ID_WIDTH</spirit:name>
        <spirit:displayName>C_ID_WIDTH</spirit:displayName>
        <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_ID_WIDTH" spirit:minimum="1" spirit:maximum="32" spirit:rangeType="long">3</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="long">
        <spirit:name>C_ADDR_WIDTH</spirit:name>
        <spirit:displayName>C_ADDR_WIDTH</spirit:displayName>
        <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_ADDR_WIDTH" spirit:minimum="1" spirit:maximum="64" spirit:rangeType="long">32</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="long">
        <spirit:name>C_USER_WIDTH</spirit:name>
        <spirit:displayName>C_USER_WIDTH</spirit:displayName>
        <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_USER_WIDTH" spirit:minimum="0" spirit:maximum="512" spirit:rangeType="long">0</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="long">
        <spirit:name>C_MAX_PAYLD_BYTES</spirit:name>
        <spirit:displayName>C_MAX_PAYLD_BYTES</spirit:displayName>
        <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_MAX_PAYLD_BYTES" spirit:minimum="4" spirit:maximum="128" spirit:rangeType="long">4</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="long">
        <spirit:name>C_S_PIPELINE</spirit:name>
        <spirit:displayName>C_S_PIPELINE</spirit:displayName>
        <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_S_PIPELINE" spirit:minimum="0" spirit:maximum="8" spirit:rangeType="long">0</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="long">
        <spirit:name>C_M_PIPELINE</spirit:name>
        <spirit:displayName>C_M_PIPELINE</spirit:displayName>
        <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_M_PIPELINE" spirit:minimum="0" spirit:maximum="8" spirit:rangeType="long">0</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="long">
        <spirit:name>C_M_SEND_PIPELINE</spirit:name>
        <spirit:displayName>C_M_SEND_PIPELINE</spirit:displayName>
        <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_M_SEND_PIPELINE" spirit:minimum="0" spirit:maximum="1" spirit:rangeType="long">0</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="long">
        <spirit:name>C_S_LATENCY</spirit:name>
        <spirit:displayName>C_S_LATENCY</spirit:displayName>
        <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_S_LATENCY" spirit:minimum="0" spirit:maximum="15" spirit:rangeType="long">0</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="long">
        <spirit:name>C_NUM_OUTSTANDING</spirit:name>
        <spirit:displayName>C_NUM_OUTSTANDING</spirit:displayName>
        <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_NUM_OUTSTANDING" spirit:minimum="0" spirit:maximum="256" spirit:rangeType="long">8</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="long">
        <spirit:name>C_ACLK_RELATIONSHIP</spirit:name>
        <spirit:displayName>C_ACLK_RELATIONSHIP</spirit:displayName>
        <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_ACLK_RELATIONSHIP" spirit:minimum="-16" spirit:maximum="16" spirit:rangeType="long">1</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="long">
        <spirit:name>C_ACLKEN_CONVERSION</spirit:name>
        <spirit:displayName>C_ACLKEN_CONVERSION</spirit:displayName>
        <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_ACLKEN_CONVERSION" spirit:minimum="0" spirit:maximum="3" spirit:rangeType="long">0</spirit:value>
      </spirit:modelParameter>
    </spirit:modelParameters>
  </spirit:model>
  <spirit:choices>
    <spirit:choice>
      <spirit:name>choice_list_11ba48e0</spirit:name>
      <spirit:enumeration>0</spirit:enumeration>
      <spirit:enumeration>2</spirit:enumeration>
    </spirit:choice>
    <spirit:choice>
      <spirit:name>choice_pairs_91ef8cba</spirit:name>
      <spirit:enumeration spirit:text="R">0</spirit:enumeration>
      <spirit:enumeration spirit:text="W">1</spirit:enumeration>
      <spirit:enumeration spirit:text="AR">2</spirit:enumeration>
      <spirit:enumeration spirit:text="AW">3</spirit:enumeration>
      <spirit:enumeration spirit:text="B">4</spirit:enumeration>
    </spirit:choice>
  </spirit:choices>
  <spirit:fileSets>
    <spirit:fileSet>
      <spirit:name>xilinx_verilogbehavioralsimulation_xilinx_com_ip_sc_util_1_0__ref_view_fileset</spirit:name>
      <spirit:file>
        <spirit:name>../../../../../ipshared/f0b6/hdl/verilog/sc_util_v1_0_4_constants.vh</spirit:name>
        <spirit:fileType>verilogSource</spirit:fileType>
        <spirit:userFileType>USED_IN_ipstatic</spirit:userFileType>
        <spirit:isIncludeFile>true</spirit:isIncludeFile>
        <spirit:logicalName>smartconnect_v1_0</spirit:logicalName>
      </spirit:file>
      <spirit:file>
        <spirit:name>../../../../../ipshared/f0b6/hdl/verilog/sc_util_v1_0_4_constants_noc.vh</spirit:name>
        <spirit:fileType>verilogSource</spirit:fileType>
        <spirit:userFileType>USED_IN_ipstatic</spirit:userFileType>
        <spirit:isIncludeFile>true</spirit:isIncludeFile>
        <spirit:logicalName>smartconnect_v1_0</spirit:logicalName>
      </spirit:file>
      <spirit:file>
        <spirit:name>../../../../../ipshared/f0b6/hdl/verilog/sc_util_v1_0_4_structs.svh</spirit:name>
        <spirit:fileType>systemVerilogSource</spirit:fileType>
        <spirit:userFileType>USED_IN_ipstatic</spirit:userFileType>
        <spirit:isIncludeFile>true</spirit:isIncludeFile>
        <spirit:logicalName>smartconnect_v1_0</spirit:logicalName>
      </spirit:file>
      <spirit:file>
        <spirit:name>../../../../../ipshared/f0b6/hdl/sc_util_v1_0_vl_rfs.sv</spirit:name>
        <spirit:fileType>systemVerilogSource</spirit:fileType>
        <spirit:userFileType>USED_IN_ipstatic</spirit:userFileType>
        <spirit:logicalName>smartconnect_v1_0</spirit:logicalName>
      </spirit:file>
      <spirit:vendorExtensions>
        <xilinx:subCoreRef>
          <xilinx:componentRef xilinx:vendor="xilinx.com" xilinx:library="ip" xilinx:name="sc_util" xilinx:version="1.0" xilinx:isGenerated="true" xilinx:checksum="4a2c0200">
            <xilinx:mode xilinx:name="copy_mode"/>
          </xilinx:componentRef>
        </xilinx:subCoreRef>
      </spirit:vendorExtensions>
    </spirit:fileSet>
    <spirit:fileSet>
      <spirit:name>xilinx_verilogbehavioralsimulation_view_fileset</spirit:name>
      <spirit:file>
        <spirit:name>../../../../../ipshared/0127/hdl/verilog/sc_node_v1_0_17_t_reqsend.svh</spirit:name>
        <spirit:fileType>systemVerilogSource</spirit:fileType>
        <spirit:userFileType>USED_IN_ipstatic</spirit:userFileType>
        <spirit:isIncludeFile>true</spirit:isIncludeFile>
        <spirit:logicalName>smartconnect_v1_0</spirit:logicalName>
      </spirit:file>
      <spirit:file>
        <spirit:name>../../../../../ipshared/0127/hdl/sc_node_v1_0_vl_rfs.sv</spirit:name>
        <spirit:fileType>systemVerilogSource</spirit:fileType>
        <spirit:userFileType>USED_IN_ipstatic</spirit:userFileType>
        <spirit:logicalName>smartconnect_v1_0</spirit:logicalName>
      </spirit:file>
    </spirit:fileSet>
    <spirit:fileSet>
      <spirit:name>xilinx_verilogsimulationwrapper_view_fileset</spirit:name>
      <spirit:file>
        <spirit:name>sim/bd_2aab_bni_3.sv</spirit:name>
        <spirit:fileType>systemVerilogSource</spirit:fileType>
        <spirit:logicalName>xil_defaultlib</spirit:logicalName>
      </spirit:file>
    </spirit:fileSet>
    <spirit:fileSet>
      <spirit:name>xilinx_verilogsynthesis_xilinx_com_ip_sc_util_1_0__ref_view_fileset</spirit:name>
      <spirit:file>
        <spirit:name>../../../../../ipshared/f0b6/hdl/verilog/sc_util_v1_0_4_constants.vh</spirit:name>
        <spirit:fileType>verilogSource</spirit:fileType>
        <spirit:isIncludeFile>true</spirit:isIncludeFile>
        <spirit:logicalName>smartconnect_v1_0</spirit:logicalName>
      </spirit:file>
      <spirit:file>
        <spirit:name>../../../../../ipshared/f0b6/hdl/verilog/sc_util_v1_0_4_constants_noc.vh</spirit:name>
        <spirit:fileType>verilogSource</spirit:fileType>
        <spirit:isIncludeFile>true</spirit:isIncludeFile>
        <spirit:logicalName>smartconnect_v1_0</spirit:logicalName>
      </spirit:file>
      <spirit:file>
        <spirit:name>../../../../../ipshared/f0b6/hdl/verilog/sc_util_v1_0_4_structs.svh</spirit:name>
        <spirit:fileType>systemVerilogSource</spirit:fileType>
        <spirit:isIncludeFile>true</spirit:isIncludeFile>
        <spirit:logicalName>smartconnect_v1_0</spirit:logicalName>
      </spirit:file>
      <spirit:file>
        <spirit:name>../../../../../ipshared/f0b6/hdl/sc_util_v1_0_vl_rfs.sv</spirit:name>
        <spirit:fileType>systemVerilogSource</spirit:fileType>
        <spirit:logicalName>smartconnect_v1_0</spirit:logicalName>
      </spirit:file>
      <spirit:vendorExtensions>
        <xilinx:subCoreRef>
          <xilinx:componentRef xilinx:vendor="xilinx.com" xilinx:library="ip" xilinx:name="sc_util" xilinx:version="1.0" xilinx:isGenerated="true" xilinx:checksum="4a2c0200">
            <xilinx:mode xilinx:name="copy_mode"/>
          </xilinx:componentRef>
        </xilinx:subCoreRef>
      </spirit:vendorExtensions>
    </spirit:fileSet>
    <spirit:fileSet>
      <spirit:name>xilinx_verilogsynthesis_view_fileset</spirit:name>
      <spirit:file>
        <spirit:name>../../../../../ipshared/0127/hdl/verilog/sc_node_v1_0_17_t_reqsend.svh</spirit:name>
        <spirit:fileType>systemVerilogSource</spirit:fileType>
        <spirit:isIncludeFile>true</spirit:isIncludeFile>
        <spirit:logicalName>smartconnect_v1_0</spirit:logicalName>
      </spirit:file>
      <spirit:file>
        <spirit:name>bd_2aab_bni_3_ooc.xdc</spirit:name>
        <spirit:userFileType>xdc</spirit:userFileType>
        <spirit:userFileType>USED_IN_implementation</spirit:userFileType>
        <spirit:userFileType>USED_IN_out_of_context</spirit:userFileType>
        <spirit:userFileType>USED_IN_synthesis</spirit:userFileType>
      </spirit:file>
      <spirit:file>
        <spirit:name>../../../../../ipshared/0127/hdl/sc_node_v1_0_vl_rfs.sv</spirit:name>
        <spirit:fileType>systemVerilogSource</spirit:fileType>
        <spirit:logicalName>smartconnect_v1_0</spirit:logicalName>
      </spirit:file>
    </spirit:fileSet>
    <spirit:fileSet>
      <spirit:name>xilinx_verilogsynthesiswrapper_view_fileset</spirit:name>
      <spirit:file>
        <spirit:name>synth/bd_2aab_bni_3.sv</spirit:name>
        <spirit:fileType>systemVerilogSource</spirit:fileType>
        <spirit:logicalName>xil_defaultlib</spirit:logicalName>
      </spirit:file>
    </spirit:fileSet>
  </spirit:fileSets>
  <spirit:description>SmartConnect Node (internal)</spirit:description>
  <spirit:parameters>
    <spirit:parameter>
      <spirit:name>DISABLE_IP</spirit:name>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.DISABLE_IP" spirit:order="2" spirit:minimum="0" spirit:maximum="1" spirit:rangeType="long">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>FIFO_SIZE</spirit:name>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.FIFO_SIZE" spirit:order="3" spirit:minimum="0" spirit:maximum="12" spirit:rangeType="long">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>FIFO_TYPE</spirit:name>
      <spirit:displayName>FIFO_TYPE</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.FIFO_TYPE" spirit:order="4" spirit:minimum="0" spirit:maximum="4" spirit:rangeType="long">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>FIFO_IP</spirit:name>
      <spirit:displayName>FIFO_IP</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.FIFO_IP" spirit:choiceRef="choice_list_11ba48e0" spirit:order="5">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>FIFO_OUTPUT_REG</spirit:name>
      <spirit:displayName>FIFO Register</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.FIFO_OUTPUT_REG" spirit:order="6" spirit:minimum="0" spirit:maximum="1" spirit:rangeType="long">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>ENABLE_PIPELINING</spirit:name>
      <spirit:displayName>ENABLE_PIPELINING</spirit:displayName>
      <spirit:value spirit:format="bitString" spirit:resolve="user" spirit:id="PARAM_VALUE.ENABLE_PIPELINING" spirit:order="7" spirit:bitStringLength="8">0x01</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>SYNCHRONIZATION_STAGES</spirit:name>
      <spirit:displayName>SYNCHRONIZATION_STAGES</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.SYNCHRONIZATION_STAGES" spirit:order="8" spirit:minimum="2" spirit:maximum="8" spirit:rangeType="long">3</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>NUM_SI</spirit:name>
      <spirit:displayName>NUM_SI</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.NUM_SI" spirit:order="9" spirit:minimum="1" spirit:maximum="16" spirit:rangeType="long">3</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>NUM_MI</spirit:name>
      <spirit:displayName>NUM_MI</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.NUM_MI" spirit:order="10" spirit:minimum="1" spirit:maximum="16" spirit:rangeType="long">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>CHANNEL</spirit:name>
      <spirit:displayName>CHANNEL</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.CHANNEL" spirit:choiceRef="choice_pairs_91ef8cba" spirit:order="11">4</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>USER_BITS_PER_BYTE</spirit:name>
      <spirit:displayName>USER_BITS_PER_BYTE</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.USER_BITS_PER_BYTE" spirit:order="12" spirit:minimum="0" spirit:maximum="4" spirit:rangeType="long">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>MAX_PAYLD_BYTES</spirit:name>
      <spirit:displayName>MAX_PAYLD_BYTES</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.MAX_PAYLD_BYTES" spirit:order="13" spirit:minimum="4" spirit:maximum="128" spirit:rangeType="long">4</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>ARBITER_MODE</spirit:name>
      <spirit:displayName>ARBITER_MODE</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.ARBITER_MODE" spirit:order="14" spirit:minimum="0" spirit:maximum="1" spirit:rangeType="long">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>SC_ROUTE_WIDTH</spirit:name>
      <spirit:displayName>SC_ROUTE_WIDTH</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.SC_ROUTE_WIDTH" spirit:order="15" spirit:minimum="1" spirit:maximum="64" spirit:rangeType="long">3</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>ID_WIDTH</spirit:name>
      <spirit:displayName>ID_WIDTH</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.ID_WIDTH" spirit:order="16" spirit:minimum="0" spirit:maximum="32" spirit:rangeType="long">3</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>PAYLD_WIDTH</spirit:name>
      <spirit:displayName>PAYLD_WIDTH</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.PAYLD_WIDTH" spirit:order="17" spirit:minimum="1" spirit:maximum="1854" spirit:rangeType="long">9</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>ADDR_WIDTH</spirit:name>
      <spirit:displayName>ADDR_WIDTH</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.ADDR_WIDTH" spirit:order="18" spirit:minimum="1" spirit:maximum="64" spirit:rangeType="long">32</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>USER_WIDTH</spirit:name>
      <spirit:displayName>USER_WIDTH</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.USER_WIDTH" spirit:order="19" spirit:minimum="0" spirit:maximum="512" spirit:rangeType="long">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>S_PIPELINE</spirit:name>
      <spirit:displayName>S_PIPELINE</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.S_PIPELINE" spirit:order="20" spirit:minimum="0" spirit:maximum="8" spirit:rangeType="long">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M_PIPELINE</spirit:name>
      <spirit:displayName>M_PIPELINE</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M_PIPELINE" spirit:order="21" spirit:minimum="0" spirit:maximum="8" spirit:rangeType="long">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M_SEND_PIPELINE</spirit:name>
      <spirit:displayName>M_SEND_PIPELINE</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M_SEND_PIPELINE" spirit:order="22" spirit:minimum="0" spirit:maximum="1" spirit:rangeType="long">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>S_LATENCY</spirit:name>
      <spirit:displayName>S_LATENCY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.S_LATENCY" spirit:order="23" spirit:minimum="0" spirit:maximum="15" spirit:rangeType="long">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>ACLK_RELATIONSHIP</spirit:name>
      <spirit:displayName>ACLK_RELATIONSHIP</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.ACLK_RELATIONSHIP" spirit:order="24" spirit:minimum="-16" spirit:maximum="16" spirit:rangeType="long">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>ACLKEN_CONVERSION</spirit:name>
      <spirit:displayName>ACLKEN_CONVERSION</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.ACLKEN_CONVERSION" spirit:order="25" spirit:minimum="0" spirit:maximum="3" spirit:rangeType="long">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>NUM_OUTSTANDING</spirit:name>
      <spirit:displayName>NUM_OUTSTANDING</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.NUM_OUTSTANDING" spirit:order="26" spirit:minimum="0" spirit:maximum="256" spirit:rangeType="long">8</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>S00_NUM_BYTES</spirit:name>
      <spirit:displayName>S00_NUM_BYTES</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.S00_NUM_BYTES" spirit:minimum="4" spirit:maximum="128" spirit:rangeType="long">4</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>S01_NUM_BYTES</spirit:name>
      <spirit:displayName>S01_NUM_BYTES</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.S01_NUM_BYTES" spirit:minimum="4" spirit:maximum="128" spirit:rangeType="long">4</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>S02_NUM_BYTES</spirit:name>
      <spirit:displayName>S02_NUM_BYTES</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.S02_NUM_BYTES" spirit:minimum="4" spirit:maximum="128" spirit:rangeType="long">4</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>S03_NUM_BYTES</spirit:name>
      <spirit:displayName>S03_NUM_BYTES</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.S03_NUM_BYTES" spirit:minimum="4" spirit:maximum="128" spirit:rangeType="long">4</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>S04_NUM_BYTES</spirit:name>
      <spirit:displayName>S04_NUM_BYTES</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.S04_NUM_BYTES" spirit:minimum="4" spirit:maximum="128" spirit:rangeType="long">4</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>S05_NUM_BYTES</spirit:name>
      <spirit:displayName>S05_NUM_BYTES</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.S05_NUM_BYTES" spirit:minimum="4" spirit:maximum="128" spirit:rangeType="long">4</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>S06_NUM_BYTES</spirit:name>
      <spirit:displayName>S06_NUM_BYTES</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.S06_NUM_BYTES" spirit:minimum="4" spirit:maximum="128" spirit:rangeType="long">4</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>S07_NUM_BYTES</spirit:name>
      <spirit:displayName>S07_NUM_BYTES</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.S07_NUM_BYTES" spirit:minimum="4" spirit:maximum="128" spirit:rangeType="long">4</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>S08_NUM_BYTES</spirit:name>
      <spirit:displayName>S08_NUM_BYTES</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.S08_NUM_BYTES" spirit:minimum="4" spirit:maximum="128" spirit:rangeType="long">4</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>S09_NUM_BYTES</spirit:name>
      <spirit:displayName>S09_NUM_BYTES</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.S09_NUM_BYTES" spirit:minimum="4" spirit:maximum="128" spirit:rangeType="long">4</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>S10_NUM_BYTES</spirit:name>
      <spirit:displayName>S10_NUM_BYTES</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.S10_NUM_BYTES" spirit:minimum="4" spirit:maximum="128" spirit:rangeType="long">4</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>S11_NUM_BYTES</spirit:name>
      <spirit:displayName>S11_NUM_BYTES</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.S11_NUM_BYTES" spirit:minimum="4" spirit:maximum="128" spirit:rangeType="long">4</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>S12_NUM_BYTES</spirit:name>
      <spirit:displayName>S12_NUM_BYTES</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.S12_NUM_BYTES" spirit:minimum="4" spirit:maximum="128" spirit:rangeType="long">4</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>S13_NUM_BYTES</spirit:name>
      <spirit:displayName>S13_NUM_BYTES</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.S13_NUM_BYTES" spirit:minimum="4" spirit:maximum="128" spirit:rangeType="long">4</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>S14_NUM_BYTES</spirit:name>
      <spirit:displayName>S14_NUM_BYTES</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.S14_NUM_BYTES" spirit:minimum="4" spirit:maximum="128" spirit:rangeType="long">4</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>S15_NUM_BYTES</spirit:name>
      <spirit:displayName>S15_NUM_BYTES</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.S15_NUM_BYTES" spirit:minimum="4" spirit:maximum="128" spirit:rangeType="long">4</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M00_NUM_BYTES</spirit:name>
      <spirit:displayName>M00_NUM_BYTES</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M00_NUM_BYTES" spirit:minimum="4" spirit:maximum="128" spirit:rangeType="long">4</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M01_NUM_BYTES</spirit:name>
      <spirit:displayName>M01_NUM_BYTES</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M01_NUM_BYTES" spirit:minimum="4" spirit:maximum="128" spirit:rangeType="long">4</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M02_NUM_BYTES</spirit:name>
      <spirit:displayName>M02_NUM_BYTES</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M02_NUM_BYTES" spirit:minimum="4" spirit:maximum="128" spirit:rangeType="long">4</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M03_NUM_BYTES</spirit:name>
      <spirit:displayName>M03_NUM_BYTES</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M03_NUM_BYTES" spirit:minimum="4" spirit:maximum="128" spirit:rangeType="long">4</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M04_NUM_BYTES</spirit:name>
      <spirit:displayName>M04_NUM_BYTES</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M04_NUM_BYTES" spirit:minimum="4" spirit:maximum="128" spirit:rangeType="long">4</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M05_NUM_BYTES</spirit:name>
      <spirit:displayName>M05_NUM_BYTES</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M05_NUM_BYTES" spirit:minimum="4" spirit:maximum="128" spirit:rangeType="long">4</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M06_NUM_BYTES</spirit:name>
      <spirit:displayName>M06_NUM_BYTES</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M06_NUM_BYTES" spirit:minimum="4" spirit:maximum="128" spirit:rangeType="long">4</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M07_NUM_BYTES</spirit:name>
      <spirit:displayName>M07_NUM_BYTES</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M07_NUM_BYTES" spirit:minimum="4" spirit:maximum="128" spirit:rangeType="long">4</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M08_NUM_BYTES</spirit:name>
      <spirit:displayName>M08_NUM_BYTES</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M08_NUM_BYTES" spirit:minimum="4" spirit:maximum="128" spirit:rangeType="long">4</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M09_NUM_BYTES</spirit:name>
      <spirit:displayName>M09_NUM_BYTES</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M09_NUM_BYTES" spirit:minimum="4" spirit:maximum="128" spirit:rangeType="long">4</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M10_NUM_BYTES</spirit:name>
      <spirit:displayName>M10_NUM_BYTES</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M10_NUM_BYTES" spirit:minimum="4" spirit:maximum="128" spirit:rangeType="long">4</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M11_NUM_BYTES</spirit:name>
      <spirit:displayName>M11_NUM_BYTES</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M11_NUM_BYTES" spirit:minimum="4" spirit:maximum="128" spirit:rangeType="long">4</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M12_NUM_BYTES</spirit:name>
      <spirit:displayName>M12_NUM_BYTES</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M12_NUM_BYTES" spirit:minimum="4" spirit:maximum="128" spirit:rangeType="long">4</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M13_NUM_BYTES</spirit:name>
      <spirit:displayName>M13_NUM_BYTES</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M13_NUM_BYTES" spirit:minimum="4" spirit:maximum="128" spirit:rangeType="long">4</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M14_NUM_BYTES</spirit:name>
      <spirit:displayName>M14_NUM_BYTES</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M14_NUM_BYTES" spirit:minimum="4" spirit:maximum="128" spirit:rangeType="long">4</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M15_NUM_BYTES</spirit:name>
      <spirit:displayName>M15_NUM_BYTES</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M15_NUM_BYTES" spirit:minimum="4" spirit:maximum="128" spirit:rangeType="long">4</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>S00_PRIORITY_ARB</spirit:name>
      <spirit:displayName>S00_PRIORITY_ARB</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.S00_PRIORITY_ARB" spirit:minimum="0" spirit:maximum="1" spirit:rangeType="long">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>S01_PRIORITY_ARB</spirit:name>
      <spirit:displayName>S01_PRIORITY_ARB</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.S01_PRIORITY_ARB" spirit:minimum="0" spirit:maximum="1" spirit:rangeType="long">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>S02_PRIORITY_ARB</spirit:name>
      <spirit:displayName>S02_PRIORITY_ARB</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.S02_PRIORITY_ARB" spirit:minimum="0" spirit:maximum="1" spirit:rangeType="long">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>S03_PRIORITY_ARB</spirit:name>
      <spirit:displayName>S03_PRIORITY_ARB</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.S03_PRIORITY_ARB" spirit:minimum="0" spirit:maximum="1" spirit:rangeType="long">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>S04_PRIORITY_ARB</spirit:name>
      <spirit:displayName>S04_PRIORITY_ARB</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.S04_PRIORITY_ARB" spirit:minimum="0" spirit:maximum="1" spirit:rangeType="long">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>S05_PRIORITY_ARB</spirit:name>
      <spirit:displayName>S05_PRIORITY_ARB</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.S05_PRIORITY_ARB" spirit:minimum="0" spirit:maximum="1" spirit:rangeType="long">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>S06_PRIORITY_ARB</spirit:name>
      <spirit:displayName>S06_PRIORITY_ARB</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.S06_PRIORITY_ARB" spirit:minimum="0" spirit:maximum="1" spirit:rangeType="long">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>S07_PRIORITY_ARB</spirit:name>
      <spirit:displayName>S07_PRIORITY_ARB</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.S07_PRIORITY_ARB" spirit:minimum="0" spirit:maximum="1" spirit:rangeType="long">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>S08_PRIORITY_ARB</spirit:name>
      <spirit:displayName>S08_PRIORITY_ARB</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.S08_PRIORITY_ARB" spirit:minimum="0" spirit:maximum="1" spirit:rangeType="long">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>S09_PRIORITY_ARB</spirit:name>
      <spirit:displayName>S09_PRIORITY_ARB</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.S09_PRIORITY_ARB" spirit:minimum="0" spirit:maximum="1" spirit:rangeType="long">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>S10_PRIORITY_ARB</spirit:name>
      <spirit:displayName>S10_PRIORITY_ARB</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.S10_PRIORITY_ARB" spirit:minimum="0" spirit:maximum="1" spirit:rangeType="long">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>S11_PRIORITY_ARB</spirit:name>
      <spirit:displayName>S11_PRIORITY_ARB</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.S11_PRIORITY_ARB" spirit:minimum="0" spirit:maximum="1" spirit:rangeType="long">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>S12_PRIORITY_ARB</spirit:name>
      <spirit:displayName>S12_PRIORITY_ARB</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.S12_PRIORITY_ARB" spirit:minimum="0" spirit:maximum="1" spirit:rangeType="long">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>S13_PRIORITY_ARB</spirit:name>
      <spirit:displayName>S13_PRIORITY_ARB</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.S13_PRIORITY_ARB" spirit:minimum="0" spirit:maximum="1" spirit:rangeType="long">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>S14_PRIORITY_ARB</spirit:name>
      <spirit:displayName>S14_PRIORITY_ARB</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.S14_PRIORITY_ARB" spirit:minimum="0" spirit:maximum="1" spirit:rangeType="long">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>S15_PRIORITY_ARB</spirit:name>
      <spirit:displayName>S15_PRIORITY_ARB</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.S15_PRIORITY_ARB" spirit:minimum="0" spirit:maximum="1" spirit:rangeType="long">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>Component_Name</spirit:name>
      <spirit:value spirit:resolve="user" spirit:id="PARAM_VALUE.Component_Name" spirit:order="1">bd_2aab_bni_3</spirit:value>
    </spirit:parameter>
  </spirit:parameters>
  <spirit:vendorExtensions>
    <xilinx:coreExtensions>
      <xilinx:displayName>SmartConnect Node</xilinx:displayName>
      <xilinx:xpmLibraries>
        <xilinx:xpmLibrary>XPM_MEMORY</xilinx:xpmLibrary>
        <xilinx:xpmLibrary>XPM_FIFO</xilinx:xpmLibrary>
      </xilinx:xpmLibraries>
      <xilinx:coreRevision>17</xilinx:coreRevision>
      <xilinx:configElementInfos>
        <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.ACLK.ASSOCIATED_BUSIF" xilinx:valuePermission="bd"/>
        <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.ACLK.ASSOCIATED_CLKEN" xilinx:valuePermission="bd"/>
        <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.ACLK.ASSOCIATED_PORT" xilinx:valuePermission="bd"/>
        <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.ACLK.ASSOCIATED_RESET" xilinx:valuePermission="bd"/>
        <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.ACLK.CLK_DOMAIN" xilinx:valueSource="default_prop" xilinx:valuePermission="bd"/>
        <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.ACLK.FREQ_HZ" xilinx:valueSource="user_prop" xilinx:valuePermission="bd"/>
        <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.ACLK.FREQ_TOLERANCE_HZ" xilinx:valuePermission="bd"/>
        <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.ACLK.PHASE" xilinx:valuePermission="bd"/>
        <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.ARESETN.POLARITY" xilinx:valuePermission="bd"/>
        <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.M_SC_ACLK.ASSOCIATED_BUSIF" xilinx:valuePermission="bd"/>
        <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.M_SC_ACLK.ASSOCIATED_CLKEN" xilinx:valuePermission="bd"/>
        <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.M_SC_ACLK.ASSOCIATED_PORT" xilinx:valuePermission="bd"/>
        <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.M_SC_ACLK.ASSOCIATED_RESET" xilinx:valuePermission="bd"/>
        <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.M_SC_ACLK.CLK_DOMAIN" xilinx:valueSource="default_prop" xilinx:valuePermission="bd"/>
        <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.M_SC_ACLK.FREQ_HZ" xilinx:valueSource="user_prop" xilinx:valuePermission="bd"/>
        <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.M_SC_ACLK.FREQ_TOLERANCE_HZ" xilinx:valuePermission="bd"/>
        <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.M_SC_ACLK.PHASE" xilinx:valuePermission="bd"/>
        <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.M_SC_ARESETN.POLARITY" xilinx:valuePermission="bd"/>
        <xilinx:configElementInfo xilinx:referenceId="PARAM_VALUE.ACLKEN_CONVERSION" xilinx:valueSource="user"/>
        <xilinx:configElementInfo xilinx:referenceId="PARAM_VALUE.ACLK_RELATIONSHIP" xilinx:valueSource="user"/>
        <xilinx:configElementInfo xilinx:referenceId="PARAM_VALUE.ADDR_WIDTH" xilinx:valueSource="user"/>
        <xilinx:configElementInfo xilinx:referenceId="PARAM_VALUE.CHANNEL" xilinx:valueSource="user"/>
        <xilinx:configElementInfo xilinx:referenceId="PARAM_VALUE.FIFO_SIZE" xilinx:valueSource="user"/>
        <xilinx:configElementInfo xilinx:referenceId="PARAM_VALUE.FIFO_TYPE" xilinx:valueSource="user"/>
        <xilinx:configElementInfo xilinx:referenceId="PARAM_VALUE.ID_WIDTH" xilinx:valueSource="user"/>
        <xilinx:configElementInfo xilinx:referenceId="PARAM_VALUE.M00_NUM_BYTES" xilinx:valueSource="user"/>
        <xilinx:configElementInfo xilinx:referenceId="PARAM_VALUE.M01_NUM_BYTES" xilinx:valueSource="user"/>
        <xilinx:configElementInfo xilinx:referenceId="PARAM_VALUE.M02_NUM_BYTES" xilinx:valueSource="user"/>
        <xilinx:configElementInfo xilinx:referenceId="PARAM_VALUE.M03_NUM_BYTES" xilinx:valueSource="user"/>
        <xilinx:configElementInfo xilinx:referenceId="PARAM_VALUE.M04_NUM_BYTES" xilinx:valueSource="user"/>
        <xilinx:configElementInfo xilinx:referenceId="PARAM_VALUE.M05_NUM_BYTES" xilinx:valueSource="user"/>
        <xilinx:configElementInfo xilinx:referenceId="PARAM_VALUE.M06_NUM_BYTES" xilinx:valueSource="user"/>
        <xilinx:configElementInfo xilinx:referenceId="PARAM_VALUE.M07_NUM_BYTES" xilinx:valueSource="user"/>
        <xilinx:configElementInfo xilinx:referenceId="PARAM_VALUE.M08_NUM_BYTES" xilinx:valueSource="user"/>
        <xilinx:configElementInfo xilinx:referenceId="PARAM_VALUE.M09_NUM_BYTES" xilinx:valueSource="user"/>
        <xilinx:configElementInfo xilinx:referenceId="PARAM_VALUE.M10_NUM_BYTES" xilinx:valueSource="user"/>
        <xilinx:configElementInfo xilinx:referenceId="PARAM_VALUE.M11_NUM_BYTES" xilinx:valueSource="user"/>
        <xilinx:configElementInfo xilinx:referenceId="PARAM_VALUE.M12_NUM_BYTES" xilinx:valueSource="user"/>
        <xilinx:configElementInfo xilinx:referenceId="PARAM_VALUE.M13_NUM_BYTES" xilinx:valueSource="user"/>
        <xilinx:configElementInfo xilinx:referenceId="PARAM_VALUE.M14_NUM_BYTES" xilinx:valueSource="user"/>
        <xilinx:configElementInfo xilinx:referenceId="PARAM_VALUE.M15_NUM_BYTES" xilinx:valueSource="user"/>
        <xilinx:configElementInfo xilinx:referenceId="PARAM_VALUE.MAX_PAYLD_BYTES" xilinx:valueSource="user"/>
        <xilinx:configElementInfo xilinx:referenceId="PARAM_VALUE.M_SEND_PIPELINE" xilinx:valueSource="user"/>
        <xilinx:configElementInfo xilinx:referenceId="PARAM_VALUE.NUM_MI" xilinx:valueSource="user"/>
        <xilinx:configElementInfo xilinx:referenceId="PARAM_VALUE.NUM_OUTSTANDING" xilinx:valueSource="user"/>
        <xilinx:configElementInfo xilinx:referenceId="PARAM_VALUE.NUM_SI" xilinx:valueSource="user"/>
        <xilinx:configElementInfo xilinx:referenceId="PARAM_VALUE.PAYLD_WIDTH" xilinx:valueSource="user"/>
        <xilinx:configElementInfo xilinx:referenceId="PARAM_VALUE.S00_NUM_BYTES" xilinx:valueSource="user"/>
        <xilinx:configElementInfo xilinx:referenceId="PARAM_VALUE.S01_NUM_BYTES" xilinx:valueSource="user"/>
        <xilinx:configElementInfo xilinx:referenceId="PARAM_VALUE.S02_NUM_BYTES" xilinx:valueSource="user"/>
        <xilinx:configElementInfo xilinx:referenceId="PARAM_VALUE.S03_NUM_BYTES" xilinx:valueSource="user"/>
        <xilinx:configElementInfo xilinx:referenceId="PARAM_VALUE.S04_NUM_BYTES" xilinx:valueSource="user"/>
        <xilinx:configElementInfo xilinx:referenceId="PARAM_VALUE.S05_NUM_BYTES" xilinx:valueSource="user"/>
        <xilinx:configElementInfo xilinx:referenceId="PARAM_VALUE.S06_NUM_BYTES" xilinx:valueSource="user"/>
        <xilinx:configElementInfo xilinx:referenceId="PARAM_VALUE.S07_NUM_BYTES" xilinx:valueSource="user"/>
        <xilinx:configElementInfo xilinx:referenceId="PARAM_VALUE.S08_NUM_BYTES" xilinx:valueSource="user"/>
        <xilinx:configElementInfo xilinx:referenceId="PARAM_VALUE.S09_NUM_BYTES" xilinx:valueSource="user"/>
        <xilinx:configElementInfo xilinx:referenceId="PARAM_VALUE.S10_NUM_BYTES" xilinx:valueSource="user"/>
        <xilinx:configElementInfo xilinx:referenceId="PARAM_VALUE.S11_NUM_BYTES" xilinx:valueSource="user"/>
        <xilinx:configElementInfo xilinx:referenceId="PARAM_VALUE.S12_NUM_BYTES" xilinx:valueSource="user"/>
        <xilinx:configElementInfo xilinx:referenceId="PARAM_VALUE.S13_NUM_BYTES" xilinx:valueSource="user"/>
        <xilinx:configElementInfo xilinx:referenceId="PARAM_VALUE.S14_NUM_BYTES" xilinx:valueSource="user"/>
        <xilinx:configElementInfo xilinx:referenceId="PARAM_VALUE.S15_NUM_BYTES" xilinx:valueSource="user"/>
        <xilinx:configElementInfo xilinx:referenceId="PARAM_VALUE.SC_ROUTE_WIDTH" xilinx:valueSource="user"/>
        <xilinx:configElementInfo xilinx:referenceId="PARAM_VALUE.USER_WIDTH" xilinx:valueSource="user"/>
      </xilinx:configElementInfos>
    </xilinx:coreExtensions>
    <xilinx:packagingInfo>
      <xilinx:xilinxVersion>2024.2</xilinx:xilinxVersion>
      <xilinx:checksum xilinx:scope="busInterfaces" xilinx:value="c1f27645"/>
      <xilinx:checksum xilinx:scope="fileGroups" xilinx:value="828c5144"/>
      <xilinx:checksum xilinx:scope="ports" xilinx:value="57b0d82a"/>
      <xilinx:checksum xilinx:scope="hdlParameters" xilinx:value="54a66669"/>
      <xilinx:checksum xilinx:scope="parameters" xilinx:value="936bd598"/>
    </xilinx:packagingInfo>
  </spirit:vendorExtensions>
</spirit:component>
