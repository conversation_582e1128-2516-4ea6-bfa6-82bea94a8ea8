{"schema": "xilinx.com:schema:json_instance:1.0", "ip_inst": {"xci_name": "bd_2aab_m18s2a_0", "cell_name": "m18_sc2axi", "component_reference": "xilinx.com:ip:sc_sc2axi:1.0", "ip_revision": "10", "gen_directory": ".", "parameters": {"component_parameters": {"READ_WRITE_MODE": [{"value": "READ_WRITE", "value_src": "user", "resolve_type": "user", "usage": "all"}], "AXI_ADDR_WIDTH": [{"value": "5", "value_src": "user", "resolve_type": "user", "format": "long", "usage": "all"}], "AXI_ID_WIDTH": [{"value": "3", "value_src": "user", "resolve_type": "user", "format": "long", "usage": "all"}], "AXI_RDATA_WIDTH": [{"value": "32", "value_src": "user", "resolve_type": "user", "format": "long", "usage": "all"}], "AXI_WDATA_WIDTH": [{"value": "32", "value_src": "user", "resolve_type": "user", "format": "long", "usage": "all"}], "SC_ADDR_WIDTH": [{"value": "32", "value_src": "user", "resolve_type": "user", "format": "long", "usage": "all"}], "SC_ID_WIDTH": [{"value": "3", "value_src": "user", "resolve_type": "user", "format": "long", "usage": "all"}], "SC_RDATA_WIDTH": [{"value": "32", "value_src": "user", "resolve_type": "user", "format": "long", "usage": "all"}], "SC_WDATA_WIDTH": [{"value": "32", "value_src": "user", "resolve_type": "user", "format": "long", "usage": "all"}], "SC_ARUSER_WIDTH": [{"value": "0", "value_src": "user", "resolve_type": "user", "format": "long", "usage": "all"}], "SC_AWUSER_WIDTH": [{"value": "0", "value_src": "user", "resolve_type": "user", "format": "long", "usage": "all"}], "SC_BUSER_WIDTH": [{"value": "0", "value_src": "user", "resolve_type": "user", "format": "long", "usage": "all"}], "SC_WUSER_BITS_PER_BYTE": [{"value": "0", "value_src": "user", "resolve_type": "user", "format": "long", "usage": "all"}], "SC_RUSER_BITS_PER_BYTE": [{"value": "0", "value_src": "user", "resolve_type": "user", "format": "long", "usage": "all"}], "MSC_ROUTE_WIDTH": [{"value": "12", "value_src": "user", "resolve_type": "user", "format": "long", "usage": "all"}], "SSC_ROUTE_WIDTH": [{"value": "3", "value_src": "user", "resolve_type": "user", "format": "long", "usage": "all"}], "Component_Name": [{"value": "bd_2aab_m18s2a_0", "resolve_type": "user", "usage": "all"}]}, "model_parameters": {"C_AXI_ADDR_WIDTH": [{"value": "5", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_AXI_ID_WIDTH": [{"value": "3", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_AXI_RDATA_WIDTH": [{"value": "32", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_AXI_WDATA_WIDTH": [{"value": "32", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_SC_ADDR_WIDTH": [{"value": "32", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_SC_ID_WIDTH": [{"value": "3", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_SC_RDATA_WIDTH": [{"value": "32", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_SC_WDATA_WIDTH": [{"value": "32", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_SC_RUSER_BITS_PER_BYTE": [{"value": "0", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_SC_WUSER_BITS_PER_BYTE": [{"value": "0", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_SC_ARUSER_WIDTH": [{"value": "0", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_SC_AWUSER_WIDTH": [{"value": "0", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_SC_BUSER_WIDTH": [{"value": "0", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_MSC_ROUTE_WIDTH": [{"value": "12", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_SSC_ROUTE_WIDTH": [{"value": "3", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_AWPAYLD_WIDTH": [{"value": "153", "resolve_type": "dependent", "format": "long", "usage": "all"}], "C_ARPAYLD_WIDTH": [{"value": "153", "resolve_type": "dependent", "format": "long", "usage": "all"}], "C_WPAYLD_WIDTH": [{"value": "63", "resolve_type": "dependent", "format": "long", "usage": "all"}], "C_RPAYLD_WIDTH": [{"value": "55", "resolve_type": "dependent", "format": "long", "usage": "all"}], "C_BPAYLD_WIDTH": [{"value": "9", "resolve_type": "dependent", "format": "long", "usage": "all"}]}, "project_parameters": {"ARCHITECTURE": [{"value": "zynq"}], "BASE_BOARD_PART": [{"value": ""}], "BOARD_CONNECTIONS": [{"value": ""}], "DEVICE": [{"value": "xc7z020"}], "PACKAGE": [{"value": "clg400"}], "PREFHDL": [{"value": "VERILOG"}], "SILICON_REVISION": [{"value": ""}], "SIMULATOR_LANGUAGE": [{"value": "MIXED"}], "SPEEDGRADE": [{"value": "-1"}], "STATIC_POWER": [{"value": ""}], "TEMPERATURE_GRADE": [{"value": ""}]}, "runtime_parameters": {"IPCONTEXT": [{"value": "IP_Integrator"}], "IPREVISION": [{"value": "10"}], "MANAGED": [{"value": "TRUE"}], "OUTPUTDIR": [{"value": "."}], "SELECTEDSIMMODEL": [{"value": ""}], "SHAREDDIR": [{"value": "../../../../../ipshared"}], "SWVERSION": [{"value": "2024.2"}], "SYNTHESISFLOW": [{"value": "GLOBAL"}]}}, "boundary": {"ports": {"aclk": [{"direction": "in"}], "m_sc_r_req": [{"direction": "out", "driver_value": "0"}], "m_sc_r_info": [{"direction": "out", "size_left": "0", "size_right": "0", "driver_value": "0"}], "m_sc_r_send": [{"direction": "out", "driver_value": "0"}], "m_sc_r_recv": [{"direction": "in", "driver_value": "0"}], "m_sc_r_payld": [{"direction": "out", "size_left": "54", "size_right": "0", "driver_value": "0"}], "m_sc_b_req": [{"direction": "out", "driver_value": "0"}], "m_sc_b_info": [{"direction": "out", "size_left": "0", "size_right": "0", "driver_value": "0"}], "m_sc_b_send": [{"direction": "out", "driver_value": "0"}], "m_sc_b_recv": [{"direction": "in", "driver_value": "0"}], "m_sc_b_payld": [{"direction": "out", "size_left": "8", "size_right": "0", "driver_value": "0"}], "s_sc_ar_req": [{"direction": "in", "driver_value": "0"}], "s_sc_ar_info": [{"direction": "in", "size_left": "0", "size_right": "0", "driver_value": "0"}], "s_sc_ar_send": [{"direction": "in", "driver_value": "0"}], "s_sc_ar_recv": [{"direction": "out", "driver_value": "0"}], "s_sc_ar_payld": [{"direction": "in", "size_left": "152", "size_right": "0", "driver_value": "0"}], "s_sc_aw_req": [{"direction": "in", "driver_value": "0"}], "s_sc_aw_info": [{"direction": "in", "size_left": "0", "size_right": "0", "driver_value": "0"}], "s_sc_aw_send": [{"direction": "in", "driver_value": "0"}], "s_sc_aw_recv": [{"direction": "out", "driver_value": "0"}], "s_sc_aw_payld": [{"direction": "in", "size_left": "152", "size_right": "0", "driver_value": "0"}], "s_sc_w_req": [{"direction": "in", "driver_value": "0"}], "s_sc_w_info": [{"direction": "in", "size_left": "0", "size_right": "0", "driver_value": "0"}], "s_sc_w_send": [{"direction": "in", "driver_value": "0"}], "s_sc_w_recv": [{"direction": "out", "driver_value": "0"}], "s_sc_w_payld": [{"direction": "in", "size_left": "62", "size_right": "0", "driver_value": "0"}], "m_axi_awid": [{"direction": "out", "size_left": "2", "size_right": "0"}], "m_axi_awaddr": [{"direction": "out", "size_left": "4", "size_right": "0"}], "m_axi_awlen": [{"direction": "out", "size_left": "7", "size_right": "0"}], "m_axi_awlock": [{"direction": "out", "size_left": "0", "size_right": "0"}], "m_axi_awcache": [{"direction": "out", "size_left": "3", "size_right": "0"}], "m_axi_awprot": [{"direction": "out", "size_left": "2", "size_right": "0"}], "m_axi_awqos": [{"direction": "out", "size_left": "3", "size_right": "0"}], "m_axi_awuser": [{"direction": "out", "size_left": "1023", "size_right": "0"}], "m_axi_awvalid": [{"direction": "out"}], "m_axi_awready": [{"direction": "in", "driver_value": "0x0"}], "m_axi_wdata": [{"direction": "out", "size_left": "31", "size_right": "0"}], "m_axi_wstrb": [{"direction": "out", "size_left": "3", "size_right": "0"}], "m_axi_wlast": [{"direction": "out"}], "m_axi_wuser": [{"direction": "out", "size_left": "1023", "size_right": "0"}], "m_axi_wvalid": [{"direction": "out"}], "m_axi_wready": [{"direction": "in", "driver_value": "0x0"}], "m_axi_bid": [{"direction": "in", "size_left": "2", "size_right": "0", "driver_value": "0x0"}], "m_axi_bresp": [{"direction": "in", "size_left": "1", "size_right": "0", "driver_value": "0x0"}], "m_axi_buser": [{"direction": "in", "size_left": "1023", "size_right": "0", "driver_value": "0x0"}], "m_axi_bvalid": [{"direction": "in", "driver_value": "0x0"}], "m_axi_bready": [{"direction": "out"}], "m_axi_arid": [{"direction": "out", "size_left": "2", "size_right": "0"}], "m_axi_araddr": [{"direction": "out", "size_left": "4", "size_right": "0"}], "m_axi_arlen": [{"direction": "out", "size_left": "7", "size_right": "0"}], "m_axi_arlock": [{"direction": "out", "size_left": "0", "size_right": "0"}], "m_axi_arcache": [{"direction": "out", "size_left": "3", "size_right": "0"}], "m_axi_arprot": [{"direction": "out", "size_left": "2", "size_right": "0"}], "m_axi_arqos": [{"direction": "out", "size_left": "3", "size_right": "0"}], "m_axi_aruser": [{"direction": "out", "size_left": "1023", "size_right": "0"}], "m_axi_arvalid": [{"direction": "out"}], "m_axi_arready": [{"direction": "in", "driver_value": "0x0"}], "m_axi_rid": [{"direction": "in", "size_left": "2", "size_right": "0", "driver_value": "0x0"}], "m_axi_rdata": [{"direction": "in", "size_left": "31", "size_right": "0", "driver_value": "0x00000000"}], "m_axi_rresp": [{"direction": "in", "size_left": "1", "size_right": "0", "driver_value": "0x0"}], "m_axi_rlast": [{"direction": "in", "driver_value": "0x1"}], "m_axi_ruser": [{"direction": "in", "size_left": "1023", "size_right": "0", "driver_value": "0x0"}], "m_axi_rvalid": [{"direction": "in", "driver_value": "0x0"}], "m_axi_rready": [{"direction": "out"}]}, "interfaces": {"M_SC_R": {"vlnv": "xilinx.com:interface:sc:1.0", "abstraction_type": "xilinx.com:interface:sc_rtl:1.0", "mode": "master", "port_maps": {"INFO": [{"physical_name": "m_sc_r_info"}], "PAYLD": [{"physical_name": "m_sc_r_payld"}], "RECV": [{"physical_name": "m_sc_r_recv"}], "REQ": [{"physical_name": "m_sc_r_req"}], "SEND": [{"physical_name": "m_sc_r_send"}]}}, "M_SC_B": {"vlnv": "xilinx.com:interface:sc:1.0", "abstraction_type": "xilinx.com:interface:sc_rtl:1.0", "mode": "master", "port_maps": {"INFO": [{"physical_name": "m_sc_b_info"}], "PAYLD": [{"physical_name": "m_sc_b_payld"}], "RECV": [{"physical_name": "m_sc_b_recv"}], "REQ": [{"physical_name": "m_sc_b_req"}], "SEND": [{"physical_name": "m_sc_b_send"}]}}, "S_SC_AR": {"vlnv": "xilinx.com:interface:sc:1.0", "abstraction_type": "xilinx.com:interface:sc_rtl:1.0", "mode": "slave", "port_maps": {"INFO": [{"physical_name": "s_sc_ar_info"}], "PAYLD": [{"physical_name": "s_sc_ar_payld"}], "RECV": [{"physical_name": "s_sc_ar_recv"}], "REQ": [{"physical_name": "s_sc_ar_req"}], "SEND": [{"physical_name": "s_sc_ar_send"}]}}, "S_SC_AW": {"vlnv": "xilinx.com:interface:sc:1.0", "abstraction_type": "xilinx.com:interface:sc_rtl:1.0", "mode": "slave", "port_maps": {"INFO": [{"physical_name": "s_sc_aw_info"}], "PAYLD": [{"physical_name": "s_sc_aw_payld"}], "RECV": [{"physical_name": "s_sc_aw_recv"}], "REQ": [{"physical_name": "s_sc_aw_req"}], "SEND": [{"physical_name": "s_sc_aw_send"}]}}, "S_SC_W": {"vlnv": "xilinx.com:interface:sc:1.0", "abstraction_type": "xilinx.com:interface:sc_rtl:1.0", "mode": "slave", "port_maps": {"INFO": [{"physical_name": "s_sc_w_info"}], "PAYLD": [{"physical_name": "s_sc_w_payld"}], "RECV": [{"physical_name": "s_sc_w_recv"}], "REQ": [{"physical_name": "s_sc_w_req"}], "SEND": [{"physical_name": "s_sc_w_send"}]}}, "M_AXI": {"vlnv": "xilinx.com:interface:aximm:1.0", "abstraction_type": "xilinx.com:interface:aximm_rtl:1.0", "mode": "master", "parameters": {"DATA_WIDTH": [{"value": "32", "value_permission": "bd", "resolve_type": "generated", "format": "long", "is_ips_inferred": true, "is_static_object": false}], "PROTOCOL": [{"value": "AXI4", "value_permission": "bd", "resolve_type": "generated", "is_ips_inferred": true, "is_static_object": false}], "FREQ_HZ": [{"value": "50000000", "value_src": "user_prop", "value_permission": "bd", "resolve_type": "generated", "format": "long", "is_ips_inferred": true, "is_static_object": false}], "ID_WIDTH": [{"value": "3", "value_src": "propagated", "value_permission": "bd", "resolve_type": "generated", "format": "long", "is_ips_inferred": true, "is_static_object": false}], "ADDR_WIDTH": [{"value": "5", "value_src": "propagated", "value_permission": "bd", "resolve_type": "generated", "format": "long", "is_ips_inferred": true, "is_static_object": false}], "AWUSER_WIDTH": [{"value": "1024", "value_permission": "bd", "resolve_type": "generated", "format": "long", "is_ips_inferred": true, "is_static_object": false}], "ARUSER_WIDTH": [{"value": "1024", "value_permission": "bd", "resolve_type": "generated", "format": "long", "is_ips_inferred": true, "is_static_object": false}], "WUSER_WIDTH": [{"value": "1024", "value_permission": "bd", "resolve_type": "generated", "format": "long", "is_ips_inferred": true, "is_static_object": false}], "RUSER_WIDTH": [{"value": "1024", "value_permission": "bd", "resolve_type": "generated", "format": "long", "is_ips_inferred": true, "is_static_object": false}], "BUSER_WIDTH": [{"value": "1024", "value_permission": "bd", "resolve_type": "generated", "format": "long", "is_ips_inferred": true, "is_static_object": false}], "READ_WRITE_MODE": [{"value": "READ_WRITE", "value_permission": "bd", "resolve_type": "generated", "is_ips_inferred": true, "is_static_object": false}], "HAS_BURST": [{"value": "0", "value_src": "constant", "value_permission": "bd", "resolve_type": "generated", "format": "long", "is_ips_inferred": true, "is_static_object": false}], "HAS_LOCK": [{"value": "1", "value_permission": "bd", "resolve_type": "generated", "format": "long", "is_ips_inferred": true, "is_static_object": false}], "HAS_PROT": [{"value": "1", "value_permission": "bd", "resolve_type": "generated", "format": "long", "is_ips_inferred": true, "is_static_object": false}], "HAS_CACHE": [{"value": "1", "value_permission": "bd", "resolve_type": "generated", "format": "long", "is_ips_inferred": true, "is_static_object": false}], "HAS_QOS": [{"value": "1", "value_permission": "bd", "resolve_type": "generated", "format": "long", "is_ips_inferred": true, "is_static_object": false}], "HAS_REGION": [{"value": "0", "value_src": "constant", "value_permission": "bd", "resolve_type": "generated", "format": "long", "is_ips_inferred": true, "is_static_object": false}], "HAS_WSTRB": [{"value": "1", "value_permission": "bd", "resolve_type": "generated", "format": "long", "is_ips_inferred": true, "is_static_object": false}], "HAS_BRESP": [{"value": "1", "value_permission": "bd", "resolve_type": "generated", "format": "long", "is_ips_inferred": true, "is_static_object": false}], "HAS_RRESP": [{"value": "1", "value_permission": "bd", "resolve_type": "generated", "format": "long", "is_ips_inferred": true, "is_static_object": false}], "SUPPORTS_NARROW_BURST": [{"value": "0", "value_permission": "bd", "resolve_type": "generated", "format": "long", "is_ips_inferred": true, "is_static_object": false}], "NUM_READ_OUTSTANDING": [{"value": "2", "value_permission": "bd", "resolve_type": "generated", "format": "long", "is_ips_inferred": true, "is_static_object": false}], "NUM_WRITE_OUTSTANDING": [{"value": "2", "value_permission": "bd", "resolve_type": "generated", "format": "long", "is_ips_inferred": true, "is_static_object": false}], "MAX_BURST_LENGTH": [{"value": "256", "value_permission": "bd", "resolve_type": "generated", "format": "long", "is_ips_inferred": true, "is_static_object": false}], "PHASE": [{"value": "0.0", "value_permission": "bd", "resolve_type": "generated", "format": "float", "is_ips_inferred": true, "is_static_object": false}], "CLK_DOMAIN": [{"value": "ZynqDesign_processing_system7_0_0_FCLK_CLK0", "value_src": "default_prop", "value_permission": "bd", "resolve_type": "generated", "is_ips_inferred": true, "is_static_object": false}], "NUM_READ_THREADS": [{"value": "1", "value_permission": "bd", "resolve_type": "generated", "format": "long", "is_ips_inferred": true, "is_static_object": false}], "NUM_WRITE_THREADS": [{"value": "1", "value_permission": "bd", "resolve_type": "generated", "format": "long", "is_ips_inferred": true, "is_static_object": false}], "RUSER_BITS_PER_BYTE": [{"value": "0", "value_permission": "bd", "resolve_type": "generated", "format": "long", "is_ips_inferred": true, "is_static_object": false}], "WUSER_BITS_PER_BYTE": [{"value": "0", "value_permission": "bd", "resolve_type": "generated", "format": "long", "is_ips_inferred": true, "is_static_object": false}], "INSERT_VIP": [{"value": "0", "resolve_type": "user", "format": "long", "usage": "simulation.rtl", "is_ips_inferred": true, "is_static_object": false}]}, "port_maps": {"ARADDR": [{"physical_name": "m_axi_araddr"}], "ARCACHE": [{"physical_name": "m_axi_arcache"}], "ARID": [{"physical_name": "m_axi_arid"}], "ARLEN": [{"physical_name": "m_axi_arlen"}], "ARLOCK": [{"physical_name": "m_axi_arlock"}], "ARPROT": [{"physical_name": "m_axi_arprot"}], "ARQOS": [{"physical_name": "m_axi_arqos"}], "ARREADY": [{"physical_name": "m_axi_arready"}], "ARUSER": [{"physical_name": "m_axi_aruser"}], "ARVALID": [{"physical_name": "m_axi_arvalid"}], "AWADDR": [{"physical_name": "m_axi_awaddr"}], "AWCACHE": [{"physical_name": "m_axi_awcache"}], "AWID": [{"physical_name": "m_axi_awid"}], "AWLEN": [{"physical_name": "m_axi_awlen"}], "AWLOCK": [{"physical_name": "m_axi_awlock"}], "AWPROT": [{"physical_name": "m_axi_awprot"}], "AWQOS": [{"physical_name": "m_axi_awqos"}], "AWREADY": [{"physical_name": "m_axi_awready"}], "AWUSER": [{"physical_name": "m_axi_awuser"}], "AWVALID": [{"physical_name": "m_axi_awvalid"}], "BID": [{"physical_name": "m_axi_bid"}], "BREADY": [{"physical_name": "m_axi_bready"}], "BRESP": [{"physical_name": "m_axi_bresp"}], "BUSER": [{"physical_name": "m_axi_buser"}], "BVALID": [{"physical_name": "m_axi_bvalid"}], "RDATA": [{"physical_name": "m_axi_rdata"}], "RID": [{"physical_name": "m_axi_rid"}], "RLAST": [{"physical_name": "m_axi_rlast"}], "RREADY": [{"physical_name": "m_axi_rready"}], "RRESP": [{"physical_name": "m_axi_rresp"}], "RUSER": [{"physical_name": "m_axi_ruser"}], "RVALID": [{"physical_name": "m_axi_rvalid"}], "WDATA": [{"physical_name": "m_axi_wdata"}], "WLAST": [{"physical_name": "m_axi_wlast"}], "WREADY": [{"physical_name": "m_axi_wready"}], "WSTRB": [{"physical_name": "m_axi_wstrb"}], "WUSER": [{"physical_name": "m_axi_wuser"}], "WVALID": [{"physical_name": "m_axi_wvalid"}]}}, "aclk": {"vlnv": "xilinx.com:signal:clock:1.0", "abstraction_type": "xilinx.com:signal:clock_rtl:1.0", "mode": "slave", "parameters": {"ASSOCIATED_BUSIF": [{"value": "M_AXI:S_SC_AW:S_SC_AR:S_SC_W:M_SC_R:M_SC_B", "value_src": "constant", "value_permission": "bd", "usage": "all"}], "FREQ_HZ": [{"value": "50000000", "value_src": "user_prop", "value_permission": "bd", "resolve_type": "generated", "format": "long", "is_ips_inferred": true, "is_static_object": false}], "FREQ_TOLERANCE_HZ": [{"value": "0", "value_permission": "bd", "resolve_type": "generated", "format": "long", "is_ips_inferred": true, "is_static_object": false}], "PHASE": [{"value": "0.0", "value_permission": "bd", "resolve_type": "generated", "format": "float", "is_ips_inferred": true, "is_static_object": false}], "CLK_DOMAIN": [{"value": "ZynqDesign_processing_system7_0_0_FCLK_CLK0", "value_src": "default_prop", "value_permission": "bd", "resolve_type": "generated", "is_ips_inferred": true, "is_static_object": false}], "ASSOCIATED_PORT": [{"value": "", "value_permission": "bd", "resolve_type": "generated", "is_ips_inferred": true, "is_static_object": false}], "ASSOCIATED_RESET": [{"value": "", "value_permission": "bd", "resolve_type": "generated", "is_ips_inferred": true, "is_static_object": false}], "INSERT_VIP": [{"value": "0", "resolve_type": "user", "format": "long", "usage": "simulation.rtl", "is_ips_inferred": true, "is_static_object": false}]}, "port_maps": {"CLK": [{"physical_name": "aclk"}]}}}}}}