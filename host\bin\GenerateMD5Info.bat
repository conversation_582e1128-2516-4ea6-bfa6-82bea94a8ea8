@echo off
:: ################################################################
:: # Name:     wrws_update.bat                                    #
:: # Purpose:  Wrap the headless update for windows               #
:: # OS:       Windows                                            #
:: ################################################################
:: #          #
:: #          #
set platformPath=%1
set binFilePath=%2
:: #echo platform:%platformPath%
:: #echo binFile:%binFilePath%
if exist %platformPath% (
    goto checkBinFilePath
) else (
	goto end
)
:checkBinFilePath
if exist %binFilePath% (
	goto checkFileType
) else (
	goto end
)
:checkFileType
FOR %%i IN ("%platformPath%") DO SET FileAttrib=%%~ai
IF %FileAttrib:~0,1% neq d (
	goto end
)
FOR %%i IN ("%binFilePath%") DO SET FileAttrib=%%~ai
IF %FileAttrib:~0,1%==d (
	goto end
)
set javaPath=%platformPath%/host/ide/platform/eclipse/jre/bin/java.exe
if exist %javaPath% (
	goto GenerateMD5Info
) else (
	goto end
)
:GenerateMD5Info
%javaPath% -jar %platformPath%/host/bin/GenerateMD5Info.jar %platformPath%/tmp/ %binFilePath%
:end