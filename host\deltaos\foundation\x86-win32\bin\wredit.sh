#!/bin/sh
################################################################
# Name:     wredit.sh                                          #
# Purpose:  open file in a Wind River Workbench                #
# OS:       All						       #
################################################################


################################################################
# Function:	Name:		pFindWindHome                  #
#	 	Purpose:	Find the install-root and set  #
#				the variable MY_WIND_HOME      #
#				to it, if neccessary           #
#		Arguments:	None                           #
################################################################
pFindWindHome(){
	SHELLHOME=`dirname $0`
	if [ "$SHELLHOME" = "." -o "$SHELLHOME" = "" ]; then
		MY_WIND_HOME="$PWD"
	else
		MY_WIND_HOME="$SHELLHOME"
	fi
	while true; do 
		if [ -f "$MY_WIND_HOME/install.properties" ]; then	
			break;
		else
			if [ -f "$MY_WIND_HOME/package.properties" ]; then
				WORKBENCH_NAME=`basename $MY_WIND_HOME`
			fi
			WIND_HOMES_PARENT=`dirname $MY_WIND_HOME`
			if [ "$WIND_HOMES_PARENT" = "$MY_WIND_HOME" ]; then
				MY_WIND_HOME=""
				break;
			else
				MY_WIND_HOME=$WIND_HOMES_PARENT
			fi
		fi
	done
	if [ "$MY_WIND_HOME" = "" ]; then
		echo "Could not find the root of your installation!"
		exit 1
	fi
    uname=`uname`
    case $uname in
        Linux)
                WRENV="wrenv.linux"
                WRWB="wrwb-x86-linux2.gtk"
                ;;
        SunOS | Solaris)
                WRENV="wrenv.solaris"
                WRWB="wrwb-sun4-solaris2.gtk"
                ;;
        windowsNT | windows32 | *)
                WRENV="wrenv.exe"
                WRWB="wrwb-x86-win32.exe"
                ;;
    esac
	# Although we have found the relative WIND_HOME it's important to use the wrenv-WIND_HOME
	# workbench does the same. This could lead to different paths for WIND_HOME (remote/local)
	MY_WIND_HOME=`$MY_WIND_HOME/$WRENV -p $WORKBENCH_NAME -f plain -o print_vars | grep WIND_HOME= | cut -d= -f 2`
}
################################################################
# Function:	Name:		pFindWorkbenchExe              #
#	 	Purpose:	find the startup executable    #
#		Arguments:	None                           #
################################################################
pFindWorkbenchExe(){
	WIND_WRWB_PATH=`$MY_WIND_HOME/$WRENV -p $WORKBENCH_NAME -f plain -o print_vars | grep WIND_WRWB_PATH | cut -d= -f 2`
}
#########
# Do it #
#########
pFindWindHome
pFindWorkbenchExe

# make args absolute
ARGS=""
while [ -n "$1" ]; do
	case $1 in
		-*)
			# don't touch options
			ARGS="$ARGS $1"
			;;
		*)
			dir=`dirname $1`
			name=`basename $1`
			fullname=`cd $dir; pwd`/$name
			ARGS="$ARGS $fullname"
			;;
	esac
	shift
done

# Bug 1334: wb executable cannot handle slashes in commandline
if [ "$WRWB" = "wrwb-x86-win32.exe" ]; then
	cd $WIND_WRWB_PATH
	.\\$WRWB --launcher.openFile $ARGS
else
	 $WIND_WRWB_PATH/$WRWB --launcher.openFile $ARGS
fi

exit_status=`echo $?`
# Exit 13 is a failure in the startup
if [ "$exit_status" = "13" ]; then
	echo "The application failed to start."
	echo "Please check the logfiles for more info:"
	echo "<user-home>/.$WORKBENCH_NAME/configuration/<time-since-1970-in-seconds>.log"
	echo "<workspace-dir>/.metadata/.log or"
fi
exit $exit_status
