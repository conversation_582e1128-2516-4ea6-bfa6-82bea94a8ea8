/*
 *@file:dbMemory.h
 *@brief:
 *             <li>���Թ������ڴ��������</li>
 * @implements:    DTA.1.3.11   DTA.1.3.10   DTA.1.3.12   DTA.1.3.14   DTA.1.3.8
 */

#ifndef _DBMEMORY_H
#define _DBMEMORY_H

/************************ͷ�ļ�********************************/

#include "vmkTypes.h"
#include "taErrorDefine.h"

#ifdef __cplusplus
extern "C" {
#endif


/************************�궨��********************************/

/************************���Ͷ���******************************/

/*�ڴ���ʶ����ֽ���*/
typedef enum
{
    Align_8Bit = 1,
    Align_16Bit = 2,
    Align_32Bit = 4,
    Align_64Bit = 8,
    Align_Error = 0xFFFFFFFF,
    Align_None = Align_8Bit
} T_DB_AccessMemoryAlign;

/*�ڴ������*/
typedef struct
{
    /*д�ڴ� */
    T_TA_ReturnCode (*WriteMemory)(UINT32 sessionID, UINT32 desAddr, const UINT8  *srcAddr, UINT32 size,T_DB_AccessMemoryAlign align,BOOL isForce);

    /*���ڴ�*/
    T_TA_ReturnCode (*ReadMemory)(UINT32 sessionID, UINT32 readAddr, UINT8  *outBuf, UINT32  size,T_DB_AccessMemoryAlign align,BOOL isForce);

    /*ˢCache*/
    void (*FlushCache)(UINT32 sessionID, UINT32 addr, UINT32 len);
} T_DB_MemoryOper;

/************************�ӿ�����******************************/

/*
 * @brief:
 *     ����ȫ���ڴ������ָ��Ķ��ڴ溯�����ж��ڴ����
 * @param[in]: sessionID:���ԻỰID
 * @param[in]: readAddr:����ȡ���ݵ��ڴ���ʼ��ַ
 * @param[out]: outBuf:���ڴ洢��ȡ���ݵĻ����ַ
 * @param[in]: size:����ȡ���ݵĴ�С
 * @param[in]: align: �ڴ���뷽ʽ
 * @param[in]: isForce: �Ƿ�ǿ�Ʒ����ڴ棬����MMUӳ�����
 * @return:
 *        TA_OK: �����ɹ�
 *        TA_FAIL: ���ڴ�ʧ��
 * @tracedREQ: RTA.3
 * @implements: DTA.1.3.11
 */
T_TA_ReturnCode taReadMemory(UINT32 sessionID, UINT32 readAddr, UINT8 *outBuf, UINT32 size, T_DB_AccessMemoryAlign align,BOOL isForce);

/*
 * @brief:
 *     ����ȫ���ڴ������ָ���д�ڴ溯������д�ڴ����
 * @param[in]: sessionID:���ԻỰID
 * @param[in]: desAddr: ��д�����ݵ��ڴ���ʼ��ַ
 * @param[in]: srcAddr: ����д�����ݵĻ����ַ
 * @param[in]: size: ��д�����ݴ�С
 * @param[in]: align: �ڴ���뷽ʽ
 * @param[in]: isForce: �Ƿ�ǿ�Ʒ����ڴ棬����MMUӳ�����
 * @return:
 *        TA_OK: �����ɹ�
 *        TA_FAIL: д�ڴ����ʧ��
 * @tracedREQ: RTA.3
 * @implements: DTA.1.3.10
 */
T_TA_ReturnCode taWriteMemory(UINT32 sessionID, UINT32 desAddr, const UINT8 *srcAddr, UINT32 size,T_DB_AccessMemoryAlign align,BOOL isForce);

/*
 * @brief:
 *   ����ȫ���ڴ������ָ���ˢ��Cache��������ˢ��Cache����
 * @param[in]: sessionID:���ԻỰID
 * @param[in]: addr:������Cache�ڴ��ַ
 * @param[in]: len:������Cache���ڴ泤��
 * @tracedREQ: RTA.3
 * @implements: DTA.1.3.12
 */
void taDebugFlushCache(UINT32 sessionID, UINT32 addr, UINT32 len);

/*
 * @brief:
 *      ��ȡ��ַ��������
 * @param[in]: addr:��ַ
 * @param[in]: size:����
 * @param[in]: vmID:����ID
 * @param[out]: alignSize:�������
 * @return:
 *     TA_OK:�Ϸ��Ķ������
 *     GDB_INVALID_ALIGN_SIZE:�Ƿ��Ķ������
 * @tracedREQ: RTA.3
 * @implements: DTA.1.3.14
 */
T_TA_ReturnCode taGetOperateSize(UINT32 addr,
UINT32 size,
INT32 vmID,
T_DB_AccessMemoryAlign *alignSize);

/**
 * @brief
 * 	TAֱ���ڴ���ʳ�ʼ��
 * @return:
 *      ��
 * @tracedREQ: RTA.3
 * @implements: DTA.1.3.8
 */
T_VOID taInitDirectMemory(T_VOID);

#ifdef __cplusplus
}
#endif/*__cplusplus*/

#endif

