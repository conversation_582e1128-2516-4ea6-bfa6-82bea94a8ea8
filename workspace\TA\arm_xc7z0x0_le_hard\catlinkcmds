

/*	begin IDE defined variables	*/
".text_size" = 0x3a84c;
".rodata_size" = 0x1f04;
".data_size" = 0x12ce;
".mmu_tbl_size" = 0x4000;
".ARM_exidx_size" = 0x8;
".init_array_size" = 0x4;
".drvcfg_sec_size" = 0x7d8;
".ARM.attributes_size" = 0x33;
".bss_size" = 0x5cb110;
".heap_size" = 0x400000;
".stack_size" = 0x18000;
".debug_line_size" = 0x47afd;
".debug_info_size" = 0xb6fe9;
".debug_abbrev_size" = 0x15d3d;
".debug_aranges_size" = 0x1358;
".debug_str_size" = 0x3a848;
".comment_size" = 0x56;
".debug_frame_size" = 0x8230;
".debug_ranges_size" = 0x18;
".debug_macro_size" = 0x17344;
".debug_loclists_size" = 0xe92c;
".debug_rnglists_size" = 0xf55;
".debug_line_str_size" = 0x133;
/*	end IDE defined variables	*/
/* memory-config in eclipse by lwj*/
/*9 Jul 2025 15:13:57 GMT*/

ENTRY(_start)

MEMORY 
{
vmk	:	ORIGIN = 0x100000,	LENGTH = 0x1000000
}

SECTIONS	{

.text   ((0x100000 + (0x00001000-1))& ~ (0x00001000-1))	:
{
".text_start" = .;
_text_start = .;

KEEP (*(.vectors))    *(.boot) *(.image_info)   *(.text)    *(.text.*)    *(.gnu.linkonce.t.*)    *(.plt)    *(.gnu_warning)    *(.gcc_execpt_table)    *(.glue_7)    *(.glue_7t)    *(.vfp11_veneer)    *(.ARM.extab)    *(.gnu.linkonce.armextab.*)    *(.note.gnu.build-id) ;
_text_end = .;
}	>vmk
_text_size = SIZEOF(.text);

.init   ((".text_start" + "_text_size" + (0x00000002-1))& ~ (0x00000002-1))	:
{
".init_start" = .;
_init_start = .;

KEEP (*(.init));
_init_end = .;
}	>vmk
_init_size = SIZEOF(.init);

.fini   ((".init_start" + "_init_size" + (0x00000002-1))& ~ (0x00000002-1))	:
{
".fini_start" = .;
.fini.start = .;

KEEP (*(.fini));
.fini.end = .;
}	>vmk
.fini.size = SIZEOF(.fini);

.rodata   ((".fini_start" + ".fini.size" + (0x00000002-1))& ~ (0x00000002-1))	:
{
".rodata_start" = .;
__rodata_start = .;

*(.rodata) *(.rodata.*) *(.gnu.linkonce.r.*);
__rodata_end = .;
}	>vmk
__rodata_size = SIZEOF(.rodata);

.rodata1   ((".rodata_start" + "__rodata_size" + (0x00000002-1))& ~ (0x00000002-1))	:
{
".rodata1_start" = .;
__rodata1_start = .;

*(.rodata1) *(.rodata1.*);
__rodata1_end = .;
}	>vmk
__rodata1_size = SIZEOF(.rodata1);

.sdata2   ((".rodata1_start" + "__rodata1_size" + (0x00000002-1))& ~ (0x00000002-1))	:
{
".sdata2_start" = .;
__sdata2_start = .;

*(.sdata2) *(.sdata2.*) *(.gnu.linkonce.s2.*);
__sdata2_end = .;
}	>vmk
__sdata2_size = SIZEOF(.sdata2);

.sbss2   ((".sdata2_start" + "__sdata2_size" + (0x00000002-1))& ~ (0x00000002-1))	:
{
".sbss2_start" = .;
__sbss2_start = .;

*(.sbss2) *(.sbss2.*) *(.gnu.linkonce.sb2.*);
__sbss2_end = .;
}	>vmk
__sbss2_size = SIZEOF(.sbss2);

.data   ((".sbss2_start" + "__sbss2_size" + (0x00001000-1))& ~ (0x00001000-1))	:
{
".data_start" = .;
__data_start = .;

*(.data) *(.data.*) *(.gnu.linkonce.d.*) *(.jcr) *(.got) *(.got.plt) *(.DeltaSVMConfig);
__data_end = .;
}	>vmk
__data_size = SIZEOF(.data);

.data1   ((".data_start" + "__data_size" + (0x00000002-1))& ~ (0x00000002-1))	:
{
".data1_start" = .;
__data1_start = .;

*(.data1) *(.data1.*);
__data1_end = .;
}	>vmk
__data1_size = SIZEOF(.data1);

.got   ((".data1_start" + "__data1_size" + (0x00000002-1))& ~ (0x00000002-1))	:
{
".got_start" = .;
.got.start = .;

*(.got);
.got.end = .;
}	>vmk
.got.size = SIZEOF(.got);

.ctors   ((".got_start" + ".got.size" + (0x00000002-1))& ~ (0x00000002-1))	:
{
".ctors_start" = .;
__CTOR_LIST__ = .;

___CTORS_LIST___ = .;
 KEEP (*crtbegin.o(.ctors)) KEEP (*(EXCLUDE_FILE(*crtend.o) .ctors)) KEEP (*(SORT(.ctors.*))) KEEP (*(.ctors)) ___CTORS_END___ = .;
__CTOR_END__ = .;
}	>vmk
__CTOR_SIZE__ = SIZEOF(.ctors);

.dtors   ((".ctors_start" + "__CTOR_SIZE__" + (0x00000002-1))& ~ (0x00000002-1))	:
{
".dtors_start" = .;
__DTOR_LIST__ = .;

___DTORS_LIST___ = .;
 KEEP (*crtbegin.o(.dtors)) KEEP (*(EXCLUDE_FILE(*crtend.o) .dtors)) KEEP (*(SORT(.dtors.*))) KEEP (*(.dtors)) ___DTORS_END___ = .;
__DTOR_END__ = .;
}	>vmk
__DTOR_SIZE__ = SIZEOF(.dtors);

.fixup   ((".dtors_start" + "__DTOR_SIZE__" + (0x00000002-1))& ~ (0x00000002-1))	:
{
".fixup_start" = .;
__fixup_start = .;

*(.fixup);
__fixup_end = .;
}	>vmk
__fixup_size = SIZEOF(.fixup);

.eh_frame   ((".fixup_start" + "__fixup_size" + (0x00000002-1))& ~ (0x00000002-1))	:
{
".eh_frame_start" = .;
.eh_frame.start = .;

*(.eh_frame);
.eh_frame.end = .;
}	>vmk
.eh_frame.size = SIZEOF(.eh_frame);

.eh_framehdr   ((".eh_frame_start" + ".eh_frame.size" + (0x00000002-1))& ~ (0x00000002-1))	:
{
".eh_framehdr_start" = .;
__eh_framehdr_start = .;

*(.eh_framehdr);
__eh_framehdr_end = .;
}	>vmk
__eh_framehdr_size = SIZEOF(.eh_framehdr);

.gcc_except_table   ((".eh_framehdr_start" + "__eh_framehdr_size" + (0x00000002-1))& ~ (0x00000002-1))	:
{
".gcc_except_table_start" = .;
.gcc_except_table.start = .;

*(.gcc_except_table);
.gcc_except_table.end = .;
}	>vmk
.gcc_except_table.size = SIZEOF(.gcc_except_table);

.mmu_tbl   ((".gcc_except_table_start" + ".gcc_except_table.size" + (0x00004000-1))& ~ (0x00004000-1))	:
{
".mmu_tbl_start" = .;
__mmu_tbl_start = .;

*(.mmu_tbl);
__mmu_tbl_end = .;
}	>vmk
__mmu_tbl_size = SIZEOF(.mmu_tbl);

.ARM_exidx   ((".mmu_tbl_start" + "__mmu_tbl_size" + (0x00000004-1))& ~ (0x00000004-1))	:
{
".ARM_exidx_start" = .;
__exidx_start = .;

*(.ARM.exidx*) *(.gnu.linkonce.armexidix.*.*);
__exidx_end = .;
}	>vmk
__exidx_size = SIZEOF(.ARM_exidx);

.preinit_array   ((".ARM_exidx_start" + "__exidx_size" + (0x00000002-1))& ~ (0x00000002-1))	:
{
".preinit_array_start" = .;
__preinit_array_start = .;

KEEP (*(SORT(.preinit_array.*))) KEEP (*(.preinit_array));
__preinit_array_end = .;
}	>vmk
__preinit_array_size = SIZEOF(.preinit_array);

.init_array   ((".preinit_array_start" + "__preinit_array_size" + (0x00000002-1))& ~ (0x00000002-1))	:
{
".init_array_start" = .;
__init_array_start = .;

KEEP (*(SORT(.init_array.*))) KEEP (*(.init_array));
__init_array_end = .;
}	>vmk
__init_array_size = SIZEOF(.init_array);

.fini_array   ((".init_array_start" + "__init_array_size" + (0x00000002-1))& ~ (0x00000002-1))	:
{
".fini_array_start" = .;
__fini_array_start = .;

KEEP (*(SORT(.fini_array.*))) KEEP (*(.fini_array));
__fini_array_end = .;
}	>vmk
__fini_array_size = SIZEOF(.fini_array);

.drvcfg_sec   ((".fini_array_start" + "__fini_array_size" + (0x00000008-1))& ~ (0x00000008-1))	:
{
".drvcfg_sec_start" = .;
__drvcfgsecdata_start = .;

KEEP (*(.drvcfg_sec));
__drvcfgsecdata_end = .;
}	>vmk
__drvcfgsecdata_size = SIZEOF(.drvcfg_sec);

.ARM.attributes   ((".drvcfg_sec_start" + "__drvcfgsecdata_size" + (0x00000002-1))& ~ (0x00000002-1))	:
{
".ARM.attributes_start" = .;
__ARM.attributes_start = .;

*(.ARM.attributes);
__ARM.attributes_end = .;
}	>vmk
__ARM.attributes_size = SIZEOF(.ARM.attributes);

.sdata   ((".ARM.attributes_start" + "__ARM.attributes_size" + (0x00000002-1))& ~ (0x00000002-1))	:
{
".sdata_start" = .;
__sdata_start = .;

*(.sdata) *(.sdata.*) *(.gnu.linkonce.s.*);
__sdata_end = .;
}	>vmk
__sdata_size = SIZEOF(.sdata);

.sbss   ((".sdata_start" + "__sdata_size" + (0x00000010-1))& ~ (0x00000010-1))	(NOLOAD)	:
{
".sbss_start" = .;
__sbss_start = .;

*(.sbss) *(.sbss.*) *(.gnu.linkonce.sb.*);
__sbss_end = .;
}	>vmk
__sbss_size = SIZEOF(.sbss);

.tdata   ((".sbss_start" + "__sbss_size" + (0x00000002-1))& ~ (0x00000002-1))	:
{
".tdata_start" = .;
__tdata_start = .;

*(.tdata) *(.tdata.*) *(.gnu.linkonce.td.*);
__tdata_end = .;
}	>vmk
__tdata_size = SIZEOF(.tdata);

.tbss   ((".tdata_start" + "__tdata_size" + (0x00000002-1))& ~ (0x00000002-1))	:
{
".tbss_start" = .;
__tbss_start = .;

*(.tbss) *(.tbss.*) *(.gnu.linkonce.tb.*);
__tbss_end = .;
}	>vmk
__tbss_size = SIZEOF(.tbss);

.bss   ((".tbss_start" + "__tbss_size" + (0x00001000-1))& ~ (0x00001000-1))	(NOLOAD)	:
{
".bss_start" = .;
__bss_start = .;

*(.bss) *(.bss.*) *(.gnu.linkonce.b.*) *(COMMON)      ;
__bss_end = .;
}	>vmk
__bss_size = SIZEOF(.bss);

.heap   ((".bss_start" + "__bss_size" + (0x00000010-1))& ~ (0x00000010-1))	(NOLOAD)	:
{
".heap_start" = .;
_heap_start = .;

_heap = .;
 HeapBase = .;
 . += 0x400000;
 HeapLimit = .;
_heap_end = .;
}	>vmk
_heap_size = SIZEOF(.heap);

.stack   ((".heap_start" + "_heap_size" + (0x00000010-1))& ~ (0x00000010-1))	(NOLOAD)	:
{
".stack_start" = .;
_stack_end = .;

. += 0x4000;
  . = ALIGN(16);
  _stack = .;
  __stack = _stack;
  . = ALIGN(16);
  _irq_stack_end = .;
  . += 0x4000;
  . = ALIGN(16);
  __irq_stack = .;
  _supervisor_stack_end = .;
  . += 0x4000;
  . = ALIGN(16);
  __supervisor_stack = .;
  _abort_stack_end = .;
  . += 0x4000;
  . = ALIGN(16);
  __abort_stack = .;
  _fiq_stack_end = .;
  . += 0x4000;
  . = ALIGN(16);
  __fiq_stack = .;
  _undef_stack_end = .;
  . += 0x4000;
  . = ALIGN(16);
  __undef_stack = .;
 _SDA_BASE_ = __sdata_start + ((__sbss_end - __sdata_start) / 2 );
  _SDA2_BASE_ = __sdata2_start + ((__sbss2_end - __sdata2_start) / 2 );
_stack_start = .;
}	>vmk
_stack_size = SIZEOF(.stack);


}

load__text_start =( 0x00001000-1 )&~(0x00001000 -1 );
load___rodata_start =( load__text_start + _text_size + ( 0x00000002 -1 ))&~(0x00000002 -1 );
load___data_start =( load___rodata_start + __rodata_size + ( 0x00001000 -1 ))&~(0x00001000 -1 );
load___bss_start =( load___data_start + __data_size + ( 0x00001000 -1 ))&~(0x00001000 -1 );
