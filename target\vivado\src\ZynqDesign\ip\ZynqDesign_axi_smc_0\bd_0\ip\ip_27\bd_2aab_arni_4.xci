{"schema": "xilinx.com:schema:json_instance:1.0", "ip_inst": {"xci_name": "bd_2aab_arni_4", "cell_name": "i_ar_node_4", "component_reference": "xilinx.com:ip:sc_node:1.0", "ip_revision": "17", "gen_directory": ".", "parameters": {"component_parameters": {"DISABLE_IP": [{"value": "0", "resolve_type": "user", "format": "long", "usage": "all"}], "FIFO_SIZE": [{"value": "0", "value_src": "user", "resolve_type": "user", "format": "long", "usage": "all"}], "FIFO_TYPE": [{"value": "0", "value_src": "user", "resolve_type": "user", "format": "long", "usage": "all"}], "FIFO_IP": [{"value": "0", "resolve_type": "user", "format": "long", "usage": "all"}], "FIFO_OUTPUT_REG": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "ENABLE_PIPELINING": [{"value": "0x01", "resolve_type": "user", "format": "bitString", "usage": "all"}], "SYNCHRONIZATION_STAGES": [{"value": "3", "resolve_type": "user", "format": "long", "usage": "all"}], "NUM_SI": [{"value": "1", "value_src": "user", "resolve_type": "user", "format": "long", "usage": "all"}], "NUM_MI": [{"value": "2", "value_src": "user", "resolve_type": "user", "format": "long", "usage": "all"}], "CHANNEL": [{"value": "2", "value_src": "user", "resolve_type": "user", "format": "long", "usage": "all"}], "USER_BITS_PER_BYTE": [{"value": "0", "resolve_type": "user", "format": "long", "usage": "all"}], "MAX_PAYLD_BYTES": [{"value": "4", "value_src": "user", "resolve_type": "user", "format": "long", "usage": "all"}], "ARBITER_MODE": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "SC_ROUTE_WIDTH": [{"value": "12", "value_src": "user", "resolve_type": "user", "format": "long", "usage": "all"}], "ID_WIDTH": [{"value": "3", "value_src": "user", "resolve_type": "user", "format": "long", "usage": "all"}], "PAYLD_WIDTH": [{"value": "155", "value_src": "user", "resolve_type": "user", "format": "long", "usage": "all"}], "ADDR_WIDTH": [{"value": "32", "value_src": "user", "resolve_type": "user", "format": "long", "usage": "all"}], "USER_WIDTH": [{"value": "0", "value_src": "user", "resolve_type": "user", "format": "long", "usage": "all"}], "S_PIPELINE": [{"value": "0", "resolve_type": "user", "format": "long", "usage": "all"}], "M_PIPELINE": [{"value": "0", "resolve_type": "user", "format": "long", "usage": "all"}], "M_SEND_PIPELINE": [{"value": "0", "value_src": "user", "resolve_type": "user", "format": "long", "usage": "all"}], "S_LATENCY": [{"value": "0", "resolve_type": "user", "format": "long", "usage": "all"}], "ACLK_RELATIONSHIP": [{"value": "1", "value_src": "user", "resolve_type": "user", "format": "long", "usage": "all"}], "ACLKEN_CONVERSION": [{"value": "0", "value_src": "user", "resolve_type": "user", "format": "long", "usage": "all"}], "NUM_OUTSTANDING": [{"value": "8", "value_src": "user", "resolve_type": "user", "format": "long", "usage": "all"}], "S00_NUM_BYTES": [{"value": "4", "value_src": "user", "resolve_type": "user", "format": "long", "usage": "all"}], "S01_NUM_BYTES": [{"value": "4", "value_src": "user", "resolve_type": "user", "format": "long", "usage": "all"}], "S02_NUM_BYTES": [{"value": "4", "value_src": "user", "resolve_type": "user", "format": "long", "usage": "all"}], "S03_NUM_BYTES": [{"value": "4", "value_src": "user", "resolve_type": "user", "format": "long", "usage": "all"}], "S04_NUM_BYTES": [{"value": "4", "value_src": "user", "resolve_type": "user", "format": "long", "usage": "all"}], "S05_NUM_BYTES": [{"value": "4", "value_src": "user", "resolve_type": "user", "format": "long", "usage": "all"}], "S06_NUM_BYTES": [{"value": "4", "value_src": "user", "resolve_type": "user", "format": "long", "usage": "all"}], "S07_NUM_BYTES": [{"value": "4", "value_src": "user", "resolve_type": "user", "format": "long", "usage": "all"}], "S08_NUM_BYTES": [{"value": "4", "value_src": "user", "resolve_type": "user", "format": "long", "usage": "all"}], "S09_NUM_BYTES": [{"value": "4", "value_src": "user", "resolve_type": "user", "format": "long", "usage": "all"}], "S10_NUM_BYTES": [{"value": "4", "value_src": "user", "resolve_type": "user", "format": "long", "usage": "all"}], "S11_NUM_BYTES": [{"value": "4", "value_src": "user", "resolve_type": "user", "format": "long", "usage": "all"}], "S12_NUM_BYTES": [{"value": "4", "value_src": "user", "resolve_type": "user", "format": "long", "usage": "all"}], "S13_NUM_BYTES": [{"value": "4", "value_src": "user", "resolve_type": "user", "format": "long", "usage": "all"}], "S14_NUM_BYTES": [{"value": "4", "value_src": "user", "resolve_type": "user", "format": "long", "usage": "all"}], "S15_NUM_BYTES": [{"value": "4", "value_src": "user", "resolve_type": "user", "format": "long", "usage": "all"}], "M00_NUM_BYTES": [{"value": "4", "value_src": "user", "resolve_type": "user", "format": "long", "usage": "all"}], "M01_NUM_BYTES": [{"value": "4", "value_src": "user", "resolve_type": "user", "format": "long", "usage": "all"}], "M02_NUM_BYTES": [{"value": "4", "value_src": "user", "resolve_type": "user", "format": "long", "usage": "all"}], "M03_NUM_BYTES": [{"value": "4", "value_src": "user", "resolve_type": "user", "format": "long", "usage": "all"}], "M04_NUM_BYTES": [{"value": "4", "value_src": "user", "resolve_type": "user", "format": "long", "usage": "all"}], "M05_NUM_BYTES": [{"value": "4", "value_src": "user", "resolve_type": "user", "format": "long", "usage": "all"}], "M06_NUM_BYTES": [{"value": "4", "value_src": "user", "resolve_type": "user", "format": "long", "usage": "all"}], "M07_NUM_BYTES": [{"value": "4", "value_src": "user", "resolve_type": "user", "format": "long", "usage": "all"}], "M08_NUM_BYTES": [{"value": "4", "value_src": "user", "resolve_type": "user", "format": "long", "usage": "all"}], "M09_NUM_BYTES": [{"value": "4", "value_src": "user", "resolve_type": "user", "format": "long", "usage": "all"}], "M10_NUM_BYTES": [{"value": "4", "value_src": "user", "resolve_type": "user", "format": "long", "usage": "all"}], "M11_NUM_BYTES": [{"value": "4", "value_src": "user", "resolve_type": "user", "format": "long", "usage": "all"}], "M12_NUM_BYTES": [{"value": "4", "value_src": "user", "resolve_type": "user", "format": "long", "usage": "all"}], "M13_NUM_BYTES": [{"value": "4", "value_src": "user", "resolve_type": "user", "format": "long", "usage": "all"}], "M14_NUM_BYTES": [{"value": "4", "value_src": "user", "resolve_type": "user", "format": "long", "usage": "all"}], "M15_NUM_BYTES": [{"value": "4", "value_src": "user", "resolve_type": "user", "format": "long", "usage": "all"}], "S00_PRIORITY_ARB": [{"value": "0", "resolve_type": "user", "format": "long", "usage": "all"}], "S01_PRIORITY_ARB": [{"value": "0", "resolve_type": "user", "format": "long", "usage": "all"}], "S02_PRIORITY_ARB": [{"value": "0", "resolve_type": "user", "format": "long", "usage": "all"}], "S03_PRIORITY_ARB": [{"value": "0", "resolve_type": "user", "format": "long", "usage": "all"}], "S04_PRIORITY_ARB": [{"value": "0", "resolve_type": "user", "format": "long", "usage": "all"}], "S05_PRIORITY_ARB": [{"value": "0", "resolve_type": "user", "format": "long", "usage": "all"}], "S06_PRIORITY_ARB": [{"value": "0", "resolve_type": "user", "format": "long", "usage": "all"}], "S07_PRIORITY_ARB": [{"value": "0", "resolve_type": "user", "format": "long", "usage": "all"}], "S08_PRIORITY_ARB": [{"value": "0", "resolve_type": "user", "format": "long", "usage": "all"}], "S09_PRIORITY_ARB": [{"value": "0", "resolve_type": "user", "format": "long", "usage": "all"}], "S10_PRIORITY_ARB": [{"value": "0", "resolve_type": "user", "format": "long", "usage": "all"}], "S11_PRIORITY_ARB": [{"value": "0", "resolve_type": "user", "format": "long", "usage": "all"}], "S12_PRIORITY_ARB": [{"value": "0", "resolve_type": "user", "format": "long", "usage": "all"}], "S13_PRIORITY_ARB": [{"value": "0", "resolve_type": "user", "format": "long", "usage": "all"}], "S14_PRIORITY_ARB": [{"value": "0", "resolve_type": "user", "format": "long", "usage": "all"}], "S15_PRIORITY_ARB": [{"value": "0", "resolve_type": "user", "format": "long", "usage": "all"}], "Component_Name": [{"value": "bd_2aab_arni_4", "resolve_type": "user", "usage": "all"}]}, "model_parameters": {"C_FAMILY": [{"value": "zynq", "resolve_type": "generated", "usage": "all"}], "C_FIFO_IP": [{"value": "0", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_DISABLE_IP": [{"value": "0", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_FIFO_SIZE": [{"value": "0", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_FIFO_TYPE": [{"value": "0", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_FIFO_OUTPUT_REG": [{"value": "1", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_ENABLE_PIPELINING": [{"value": "0x01", "resolve_type": "generated", "format": "bitString", "usage": "all"}], "C_SYNCHRONIZATION_STAGES": [{"value": "3", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_NUM_SI": [{"value": "1", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_NUM_MI": [{"value": "2", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_CHANNEL": [{"value": "2", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_PAYLD_WIDTH": [{"value": "155", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_S_NUM_BYTES_ARRAY": [{"value": "0x00000004", "resolve_type": "generated", "format": "bitString", "usage": "all"}], "C_M_NUM_BYTES_ARRAY": [{"value": "0x0000000400000004", "resolve_type": "generated", "format": "bitString", "usage": "all"}], "C_PRIORITY_ARB_ARRAY": [{"value": "0b0", "resolve_type": "generated", "format": "bitString", "usage": "all"}], "C_USER_BITS_PER_BYTE": [{"value": "0", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_ARBITER_MODE": [{"value": "1", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_SC_ROUTE_WIDTH": [{"value": "12", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_ID_WIDTH": [{"value": "3", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_ADDR_WIDTH": [{"value": "32", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_USER_WIDTH": [{"value": "0", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_MAX_PAYLD_BYTES": [{"value": "4", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_S_PIPELINE": [{"value": "0", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_M_PIPELINE": [{"value": "0", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_M_SEND_PIPELINE": [{"value": "0", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_S_LATENCY": [{"value": "0", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_NUM_OUTSTANDING": [{"value": "8", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_ACLK_RELATIONSHIP": [{"value": "1", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_ACLKEN_CONVERSION": [{"value": "0", "resolve_type": "generated", "format": "long", "usage": "all"}]}, "project_parameters": {"ARCHITECTURE": [{"value": "zynq"}], "BASE_BOARD_PART": [{"value": ""}], "BOARD_CONNECTIONS": [{"value": ""}], "DEVICE": [{"value": "xc7z020"}], "PACKAGE": [{"value": "clg400"}], "PREFHDL": [{"value": "VERILOG"}], "SILICON_REVISION": [{"value": ""}], "SIMULATOR_LANGUAGE": [{"value": "MIXED"}], "SPEEDGRADE": [{"value": "-1"}], "STATIC_POWER": [{"value": ""}], "TEMPERATURE_GRADE": [{"value": ""}]}, "runtime_parameters": {"IPCONTEXT": [{"value": "IP_Integrator"}], "IPREVISION": [{"value": "17"}], "MANAGED": [{"value": "TRUE"}], "OUTPUTDIR": [{"value": "."}], "SELECTEDSIMMODEL": [{"value": ""}], "SHAREDDIR": [{"value": "../../../../../ipshared"}], "SWVERSION": [{"value": "2024.2"}], "SYNTHESISFLOW": [{"value": "GLOBAL"}]}}, "boundary": {"ports": {"s_sc_aclk": [{"direction": "in"}], "s_sc_aresetn": [{"direction": "in", "driver_value": "0x1"}], "s_sc_req": [{"direction": "in", "size_left": "0", "size_right": "0", "driver_value": "0"}], "s_sc_info": [{"direction": "in", "size_left": "0", "size_right": "0", "driver_value": "0"}], "s_sc_send": [{"direction": "in", "size_left": "0", "size_right": "0"}], "s_sc_recv": [{"direction": "out", "size_left": "0", "size_right": "0"}], "s_sc_payld": [{"direction": "in", "size_left": "154", "size_right": "0"}], "m_sc_aclk": [{"direction": "in"}], "m_sc_aresetn": [{"direction": "in", "driver_value": "0x1"}], "m_sc_recv": [{"direction": "in", "size_left": "1", "size_right": "0"}], "m_sc_send": [{"direction": "out", "size_left": "1", "size_right": "0"}], "m_sc_req": [{"direction": "out", "size_left": "1", "size_right": "0"}], "m_sc_info": [{"direction": "out", "size_left": "1", "size_right": "0"}], "m_sc_payld": [{"direction": "out", "size_left": "154", "size_right": "0"}]}, "interfaces": {"S_SC": {"vlnv": "xilinx.com:interface:sc:1.0", "abstraction_type": "xilinx.com:interface:sc_rtl:1.0", "mode": "slave", "port_maps": {"INFO": [{"physical_name": "s_sc_info"}], "PAYLD": [{"physical_name": "s_sc_payld"}], "RECV": [{"physical_name": "s_sc_recv"}], "REQ": [{"physical_name": "s_sc_req"}], "SEND": [{"physical_name": "s_sc_send"}]}}, "M_SC": {"vlnv": "xilinx.com:interface:sc:1.0", "abstraction_type": "xilinx.com:interface:sc_rtl:1.0", "mode": "master", "port_maps": {"INFO": [{"physical_name": "m_sc_info"}], "PAYLD": [{"physical_name": "m_sc_payld"}], "RECV": [{"physical_name": "m_sc_recv"}], "REQ": [{"physical_name": "m_sc_req"}], "SEND": [{"physical_name": "m_sc_send"}]}}, "aclk": {"vlnv": "xilinx.com:signal:clock:1.0", "abstraction_type": "xilinx.com:signal:clock_rtl:1.0", "mode": "slave", "parameters": {"ASSOCIATED_BUSIF": [{"value": "S_AXIS_ARB:M_AXIS_ARB:S_SC", "value_src": "constant", "value_permission": "bd", "usage": "all"}], "ASSOCIATED_RESET": [{"value": "s_sc_aresetn", "value_src": "constant", "value_permission": "bd", "usage": "all"}], "ASSOCIATED_CLKEN": [{"value": "s_sc_aclken", "value_src": "constant", "value_permission": "bd", "usage": "all"}], "FREQ_HZ": [{"value": "50000000", "value_src": "user_prop", "value_permission": "bd", "resolve_type": "generated", "format": "long", "is_ips_inferred": true, "is_static_object": false}], "FREQ_TOLERANCE_HZ": [{"value": "0", "value_permission": "bd", "resolve_type": "generated", "format": "long", "is_ips_inferred": true, "is_static_object": false}], "PHASE": [{"value": "0.0", "value_permission": "bd", "resolve_type": "generated", "format": "float", "is_ips_inferred": true, "is_static_object": false}], "CLK_DOMAIN": [{"value": "ZynqDesign_processing_system7_0_0_FCLK_CLK0", "value_src": "default_prop", "value_permission": "bd", "resolve_type": "generated", "is_ips_inferred": true, "is_static_object": false}], "ASSOCIATED_PORT": [{"value": "", "value_permission": "bd", "resolve_type": "generated", "is_ips_inferred": true, "is_static_object": false}], "INSERT_VIP": [{"value": "0", "resolve_type": "user", "format": "long", "usage": "simulation.rtl", "is_ips_inferred": true, "is_static_object": false}]}, "port_maps": {"CLK": [{"physical_name": "s_sc_aclk"}]}}, "aresetn": {"vlnv": "xilinx.com:signal:reset:1.0", "abstraction_type": "xilinx.com:signal:reset_rtl:1.0", "mode": "slave", "parameters": {"POLARITY": [{"value": "ACTIVE_LOW", "value_src": "constant", "value_permission": "bd", "usage": "all"}], "INSERT_VIP": [{"value": "0", "resolve_type": "user", "format": "long", "usage": "simulation.rtl", "is_ips_inferred": true, "is_static_object": false}]}, "port_maps": {"RST": [{"physical_name": "s_sc_aresetn"}]}}, "m_sc_aclk": {"vlnv": "xilinx.com:signal:clock:1.0", "abstraction_type": "xilinx.com:signal:clock_rtl:1.0", "mode": "slave", "parameters": {"ASSOCIATED_BUSIF": [{"value": "M_SC", "value_src": "constant", "value_permission": "bd", "usage": "all"}], "ASSOCIATED_RESET": [{"value": "m_sc_aresetn", "value_src": "constant", "value_permission": "bd", "usage": "all"}], "ASSOCIATED_CLKEN": [{"value": "m_sc_ac<PERSON>en", "value_src": "constant", "value_permission": "bd", "usage": "all"}], "FREQ_HZ": [{"value": "50000000", "value_src": "user_prop", "value_permission": "bd", "resolve_type": "generated", "format": "long", "is_ips_inferred": true, "is_static_object": false}], "FREQ_TOLERANCE_HZ": [{"value": "0", "value_permission": "bd", "resolve_type": "generated", "format": "long", "is_ips_inferred": true, "is_static_object": false}], "PHASE": [{"value": "0.0", "value_permission": "bd", "resolve_type": "generated", "format": "float", "is_ips_inferred": true, "is_static_object": false}], "CLK_DOMAIN": [{"value": "ZynqDesign_processing_system7_0_0_FCLK_CLK0", "value_src": "default_prop", "value_permission": "bd", "resolve_type": "generated", "is_ips_inferred": true, "is_static_object": false}], "ASSOCIATED_PORT": [{"value": "", "value_permission": "bd", "resolve_type": "generated", "is_ips_inferred": true, "is_static_object": false}], "INSERT_VIP": [{"value": "0", "resolve_type": "user", "format": "long", "usage": "simulation.rtl", "is_ips_inferred": true, "is_static_object": false}]}, "port_maps": {"CLK": [{"physical_name": "m_sc_aclk"}]}}, "m_sc_aresetn": {"vlnv": "xilinx.com:signal:reset:1.0", "abstraction_type": "xilinx.com:signal:reset_rtl:1.0", "mode": "slave", "parameters": {"POLARITY": [{"value": "ACTIVE_LOW", "value_src": "constant", "value_permission": "bd", "usage": "all"}], "INSERT_VIP": [{"value": "0", "resolve_type": "user", "format": "long", "usage": "simulation.rtl", "is_ips_inferred": true, "is_static_object": false}]}, "port_maps": {"RST": [{"physical_name": "m_sc_aresetn"}]}}}}}}