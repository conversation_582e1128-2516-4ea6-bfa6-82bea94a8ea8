#!/bin/sh
################################################################
# Name:     wrcomponent                                        #
# Purpose:  Launch the Wind River Component                    #
#           Application from Commandline                       #
#	    Will use a sane set of default arguments for the   #
#           application in case none is specified              #
# OS:       Unix's,Windows                                     #
################################################################
################################################################
# Function:     Name:           pPrepare		       #
#               Purpose:        Prepare this script for ops    #
#               Arguments:      None			       #
################################################################
pPrepare(){
	HOME=`dirname $0`
	if [ ! -f /bin/sh ]; then
		SH="sh"
	else
		SH="/bin/sh"
	fi
	# Always refresh the workspace before doing anything
	ARGS="-refresh $*"
}
################################################################
# Function:	Name:		pScript			       #
#		Purpose:	Run this script		       #
#		Arguments:	Arguments from the shell       #
################################################################
pScript(){
	pPrepare $*
	$SH $HOME/launchEclipseApplication.sh com.windriver.ide.headless.HeadlessImportApplication $ARGS
        exit_status=$?
        if [ ! "$exit_status" = "0" ]; then
                echo "Problems during import(exit $exit_status), please check the log file for details!"
        fi
        exit "$exit_status"
}
pScript $*
