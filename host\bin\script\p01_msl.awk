BEGIN	{
	Count = 0
	Flag = 0
	ModuleName = ""
	FS="[[:space:]+]"
	print "#include <mslClb.h>\n\n"
	print ".text\n\n"
}

/LIBRARY/{ 
	if( Flag == 0 )
	{
		i = NF
		while( i > 1 )
		{
			if( $i != "" )
			{
				ModuleName = $i
				printf "LLDR_CALLLIB_MSLMODDEF(%s,%c%s%c)\n\n", ModuleName, 34, <PERSON><PERSON>leName, 34
				break
			}
			i--
		}
	}
}

{
	if( Flag == 1 )
	{
		i = 1
		while( i <= NF )
		{
			if( $i != "" )
			{
				printf "LLDR_CALLLIB_MSLFUNDEF(%s,%s,%d)\n", ModuleName, $i, Count 
				Count++
				break
			}
			i++
		}
	}
}

/EXPORTS/	{ Flag = 1 }

END	{
	print "\n.end\n"
}


