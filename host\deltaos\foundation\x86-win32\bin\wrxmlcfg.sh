#!/bin/sh
#######################################################
# Name:     wrxmlcfg                                  #
# Purpose:  launches the Wind River XML configuration #
#           application from command line.            #
# OS:       Unix's,Windows                            #
#######################################################
#######################################################
# Function: Name:      pPrepare		                  #
#           Purpose:   prepares this script for ops   #
#           Arguments: none			                  #
#######################################################
pPrepare(){
	HOME=`dirname $0`
	if [ ! -f /bin/sh ]; then
		SH="sh"
	else
		SH="/bin/sh"
	fi
	# Always refresh the workspace before doing anything
	ARGS="-refresh $*"
}
#######################################################
# Function:	Name:      pScript			              #
#		    Purpose:   runs this script	              #
#		    Arguments: arguments from the shell       #
#######################################################
pScript(){
	pPrepare $*
	#$SH $HOME/launchEclipseApplication.sh com.windriver.ide.headless.xml.HeadlessXMLConfigurationApplication $ARGS
        exit_status=0
        if [ ! "$exit_status" = "0" ]; then
                echo "Problems during XML configuration(exit $exit_status), please check the log file for details!"
        fi
        exit "$exit_status"
}
pScript $*
