#!/bin/sh
################################################################
# Name:     wrws_script                                        #
# Purpose:  Launch the Wind River Headless Script              #
#           Application from Commandline                       #
#	      Will use a sane set of default arguments for the   #
#           application in case none is specified              #
# OS:       Unix's,Windows                                     #
################################################################
################################################################
# Function:     Name:           pPrepare		               #
#               Purpose:        Prepare this script for ops    #
#               Arguments:      None			         #
################################################################
pPrepare(){
	HOME=`dirname $0`
	if [ ! -f /bin/sh ]; then
		SH="sh"
	else
		SH="/bin/sh"
	fi
	# Always refresh the workspace before doing anything
	ARGS="-refresh $*"
}
################################################################
# Function:	Name:		pScript			               #
#		Purpose:	Run this script		               #
#		Arguments:	Arguments from the shell               #
################################################################
pScript(){
	pPrepare $*
	$SH $HOME/launchEclipseApplication.sh com.windriver.ide.headless.HeadlessScriptApplication $ARGS
	exit_status=$?
	exit "$exit_status"
}
pScript $*
