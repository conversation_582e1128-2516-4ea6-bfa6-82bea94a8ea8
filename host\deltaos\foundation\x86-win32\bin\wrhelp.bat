@echo off
:: ####################################################################
:: # Name:     wrhelp.bat                                             #
:: # Purpose:  Launch the standalone help application or help server  #
:: # OS:       Windows                                                #
:: ####################################################################
:: # Usage:
:: #   wrhelp          -- Launch a local web browser
:: #   wrhelp start    -- Start infocenter web server
:: #   wrhelp shutdown -- Stop infocenter web server   
:: #
:: # Please edit port for infocenter below as needed.
:: #
:: # For details and background information, see also
:: # http://help.eclipse.org/galileo/topic/org.eclipse.platform.doc.isv/guide/ua_help_setup_standalone.htm
:: # http://help.eclipse.org/galileo/topic/org.eclipse.platform.doc.isv/guide/ua_help_setup_infocenter.htm
:: ####################################################################

Rem User configuration area. Edit as needed.
SETLOCAL
if "%WIND_HELP_PORT%" == "" set WIND_HELP_PORT=27132
if "%WIND_HELP_WS%" == "" set WIND_HELP_WS=%USERPROFILE%\.workbench-3.2.0-helpws

Rem System configuration. Usually no edit required.
if "%WIND_HOST_TYPE%" == "" set WIND_HOST_TYPE=x86-win32
if "%WIND_JRE_HOME%" == ""  set WIND_JRE_HOME=%~dp0\..\..\..\jre\1.6.0_21\%WIND_HOST_TYPE%
if "%WIND_WRWB_PATH%" == "" set WIND_WRWB_PATH=%~dp0\..\..\wrwb\platform\%WIND_HOST_TYPE%\eclipse
if "%COMPUTERNAME%" == "" set COMPUTERNAME=localhost

Rem Command dispatch.
if "%1" == "" GOTO checkInstance
if "%1" == "start" GOTO checkInstance
if "%1" == "shutdown" GOTO stopInfocenter
echo.
echo "Usage: %~nx0 [start|shutdown]"
GOTO errorEnd

Rem Check if another instance is running already.
:checkInstance
IF EXIST "%WIND_HELP_WS%\.metadata\.applicationlock" (
  DEL "%WIND_HELP_WS%\.metadata\.applicationlock" > NUL: 2> NUL:
  IF EXIST "%WIND_HELP_WS%\.metadata\.applicationlock" GOTO errorWs
)
if "%1" == "" GOTO runHelp
if "%1" == "start" GOTO startInfocenter

:runHelp
echo.
echo Launching web browser for stand-alone help...
echo This may take a while to come up.
echo.
echo Note: To run the help server permanently and on a fixed port, you can use
echo "%~nx0 start". This is recommended for bookmarking and team deployment.
echo.  
SET CMD=%WIND_JRE_HOME%\bin\javaw -classpath %WIND_WRWB_PATH%\plugins\org.eclipse.help.base_*.jar
SET CMD=%CMD% org.eclipse.help.standalone.Help -command displayHelpWindow -nl en -locales en
SET CMD=%CMD% -eclipsehome %WIND_WRWB_PATH% -noexec -data "%WIND_HELP_WS%" -vmargs -Xmx256m -XX:MaxPermSize=128m
Rem Execute the command
start %CMD%
echo Standalone Help is coming up, please wait...
@REM wait 10 seconds...
ping ************ -n 1 -w 10000 > nul
GOTO end

:startInfocenter
echo.
echo Launching web server for help browsing. Please direct your Browsers to URL
echo.
echo   http://%COMPUTERNAME%:%WIND_HELP_PORT%/help/index.jsp
echo.
echo If this should not work, please shut down the server as indicated below,
echo and try setting the WIND_HELP_PORT variable to a value other than "%WIND_HELP_PORT%".
echo.   
echo When done, shut down the server with
echo   %~dpnx0 shutdown
echo.
:stopInfocenter
SET CMD=%WIND_JRE_HOME%\bin\java -classpath %WIND_WRWB_PATH%\plugins\org.eclipse.help.base_*.jar
SET CMD=%CMD% org.eclipse.help.standalone.Infocenter -command %1 -port %WIND_HELP_PORT% -nl en -locales en 
SET CMD=%CMD% -eclipsehome %WIND_WRWB_PATH% -noexec -data "%WIND_HELP_WS%" -vmargs -Xmx256m -XX:MaxPermSize=128m
Rem Execute the command
%CMD%
GOTO end

:errorWs
echo.
echo Error: Another instance of %~nx0 seems to be running already.
echo.
echo Please run
echo   %~dpnx0 shutdown
echo.
echo If this does not help, then check file
echo   "%WIND_HELP_WS%\.metadata\.applicationlock"

:errorEnd
echo.
pause

:end
ENDLOCAL 
