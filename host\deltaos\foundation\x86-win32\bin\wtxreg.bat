@ECHO OFF
REM
REM Copyright (c) 2004-2006 Wind River Systems, Inc. 
REM 
REM The right to copy, distribute, modify, or otherwise make use 
REM of this software may be licensed only pursuant to the terms 
REM of an applicable Wind River license agreement. 
REM
REM Usage: wtxreg <args>
REM
REM
REM modification history
REM --------------------
REM 01a,22dec04,p_b  written

REM Check environement variables
IF "%WIND_FOUNDATION_PATH%" == "" GOTO notfound
IF "%WIND_HOST_TYPE%" == "" GOTO notfound

REM run wtxreg TCL wrapper
SET WTXTCL=%WIND_FOUNDATION_PATH%\\%WIND_HOST_TYPE%\\bin\\wtxtcl

%WTXTCL% %WIND_FOUNDATION_PATH%\\resource\\tcl\\wtxreg.tcl %* 

GOTO end

:notfound
	ECHO WIND_FOUNDATION_PATH and WIND_HOST_TYPE must be set
	ECHO exit.
GOTO end

:end
