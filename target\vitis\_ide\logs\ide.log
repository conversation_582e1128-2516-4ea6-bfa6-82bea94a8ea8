2025-07-01 20:47:17 [info] : [Platform Server] Creating platform platform  output stream finished - platform. 
2025-07-01 20:53:04 [info] : [Platform Server] Generate platform output stream finished - platform. 
2025-07-01 20:53:53 [info] : [Platform Server] Generate platform output stream finished - platform. 
2025-07-01 20:53:56 [info] : [Build Server] Build output stream finished - hello_world::. 
2025-07-02 21:22:19 [info] : [Program Flash Server] command program_flash -f C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\workspace\app\arm_xc7z0x0_le_hard_123131\make\app.bin -offset 0x600000 -flash_type qspi-x4-single -fsbl C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\platform\zynq_fsbl\build\fsbl.elf -verify -url TCP:127.0.0.1:3121 
2025-07-02 21:24:00 [info] : [Debug Manager] Successfully verified the target connection used  Local 
2025-07-02 21:24:00 [info] : [Debug Manager] Starting launch in 'run' mode using configuration: {"type":"tcf-debug","request":"launch","name":"hello_world_app_hw_1","debugType":"baremetal-zynq","attachToRunningTargetOptions":{"targetSetupMode":"standalone","executeScript":true,"scriptPath":""},"autoAttachProcessChildren":false,"target":{"targetConnectionId":"Local","peersIniPath":"../../_ide/.peers.ini","context":"zynq"},"pathMap":[],"targetSetup":{"resetSystem":true,"programDevice":true,"resetAPU":false,"bitstreamFile":"${workspaceFolder}\\hello_world\\_ide\\bitstream\\ZynqDesign_wrapper.bit","zynqInitialization":{"isFsbl":false,"usingFSBL":{"initWithFSBL":true,"fsblFile":"${workspaceFolder}\\platform\\export\\platform\\sw\\boot\\fsbl.elf","fsblExitSymbol":"FsblHandoffJtagExit"},"usingPs7Init":{"runPs7Init":true,"runPs7PostInit":true,"ps7InitTclFile":"${workspaceFolder}\\hello_world\\_ide\\psinit\\ps7_init.tcl"}},"downloadElf":[{"core":"ps7_cortexa9_0","resetProcessor":true,"elfFile":"C:\\Users\\<USER>\\Documents\\Works\\ML007\\tool_ide_1\\workspace\\TA\\arm_xc7z0x0_le_hard\\make\\TA.elf","stopAtEntry":false}],"crossTriggerBreakpoints":{"isSelected":false,"breakpoints":[]}},"internalConsoleOptions":"openOnSessionStart","__configUri":"file:///c%3A/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/hello_world/_ide/launch.json"} 
2025-07-02 21:24:01 [info] : [Debug Manager] Disabling breakpoints in the tool to enable run mode for 'hello_world_app_hw_1 [hello_world]'. 
2025-07-02 21:24:02 [info] : [Debug Manager] Launch session has started on the configuration 'hello_world_app_hw_1 [hello_world]' 
2025-07-02 21:25:18 [info] : [Debug Manager] Launch session has terminated on the configuration 'hello_world_app_hw_1 [hello_world]' 
2025-07-02 21:25:18 [info] : [Debug Manager] Enabling breakpoints after running the application using 'hello_world_app_hw_1 [hello_world]'. 
2025-07-02 21:25:57 [info] : [Debug Manager] Successfully verified the target connection used  Local 
2025-07-02 21:25:57 [info] : [Debug Manager] Starting launch in 'debug' mode using configuration: {"type":"tcf-debug","request":"launch","name":"hello_world_app_hw_1","debugType":"baremetal-zynq","attachToRunningTargetOptions":{"targetSetupMode":"standalone","executeScript":true,"scriptPath":""},"autoAttachProcessChildren":false,"target":{"targetConnectionId":"Local","peersIniPath":"../../_ide/.peers.ini","context":"zynq"},"pathMap":[],"targetSetup":{"resetSystem":true,"programDevice":true,"resetAPU":false,"bitstreamFile":"${workspaceFolder}\\hello_world\\_ide\\bitstream\\ZynqDesign_wrapper.bit","zynqInitialization":{"isFsbl":false,"usingFSBL":{"initWithFSBL":true,"fsblFile":"${workspaceFolder}\\platform\\export\\platform\\sw\\boot\\fsbl.elf","fsblExitSymbol":"FsblHandoffJtagExit"},"usingPs7Init":{"runPs7Init":true,"runPs7PostInit":true,"ps7InitTclFile":"${workspaceFolder}\\hello_world\\_ide\\psinit\\ps7_init.tcl"}},"downloadElf":[{"core":"ps7_cortexa9_0","resetProcessor":true,"elfFile":"C:\\Users\\<USER>\\Documents\\Works\\ML007\\tool_ide_1\\workspace\\TA\\arm_xc7z0x0_le_hard\\make\\TA.elf","stopAtEntry":false}],"crossTriggerBreakpoints":{"isSelected":false,"breakpoints":[]}},"internalConsoleOptions":"openOnSessionStart","__configUri":"file:///c%3A/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/hello_world/_ide/launch.json"} 
2025-07-02 21:25:57 [info] : [Debug Manager] Added breakpoint:main 
2025-07-02 21:25:57 [info] : [Debug Manager] Added breakpoint:_exit 
2025-07-02 21:25:58 [info] : [Debug Manager] Launch session has started on the configuration 'hello_world_app_hw_1 [hello_world]' 
2025-07-02 21:26:32 [info] : [Debug Manager] Removed breakpoint: main 
2025-07-02 21:26:32 [info] : [Debug Manager] Removed breakpoint: _exit 
2025-07-02 21:26:32 [info] : [Debug Manager] Launch session has terminated on the configuration 'hello_world_app_hw_1 [hello_world]' 
2025-07-02 21:27:02 [info] : [Debug Manager] Successfully verified the target connection used  Local 
2025-07-02 21:27:02 [info] : [Debug Manager] Starting launch in 'debug' mode using configuration: {"type":"tcf-debug","request":"launch","name":"hello_world_app_hw_1","debugType":"baremetal-zynq","attachToRunningTargetOptions":{"targetSetupMode":"standalone","executeScript":true,"scriptPath":""},"autoAttachProcessChildren":false,"target":{"targetConnectionId":"Local","peersIniPath":"../../_ide/.peers.ini","context":"zynq"},"pathMap":[],"targetSetup":{"resetSystem":true,"programDevice":true,"resetAPU":false,"bitstreamFile":"${workspaceFolder}\\hello_world\\_ide\\bitstream\\ZynqDesign_wrapper.bit","zynqInitialization":{"isFsbl":false,"usingFSBL":{"initWithFSBL":true,"fsblFile":"${workspaceFolder}\\platform\\export\\platform\\sw\\boot\\fsbl.elf","fsblExitSymbol":"FsblHandoffJtagExit"},"usingPs7Init":{"runPs7Init":true,"runPs7PostInit":true,"ps7InitTclFile":"${workspaceFolder}\\hello_world\\_ide\\psinit\\ps7_init.tcl"}},"downloadElf":[{"core":"ps7_cortexa9_0","resetProcessor":true,"elfFile":"C:\\Users\\<USER>\\Documents\\Works\\ML007\\tool_ide_1\\workspace\\TA\\arm_xc7z0x0_le_hard\\make\\TA.elf","stopAtEntry":false}],"crossTriggerBreakpoints":{"isSelected":false,"breakpoints":[]}},"internalConsoleOptions":"openOnSessionStart","__configUri":"file:///c%3A/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/hello_world/_ide/launch.json"} 
2025-07-02 21:27:02 [info] : [Debug Manager] Added breakpoint:main 
2025-07-02 21:27:02 [info] : [Debug Manager] Added breakpoint:_exit 
2025-07-02 21:27:03 [info] : [Debug Manager] Launch session has started on the configuration 'hello_world_app_hw_1 [hello_world]' 
2025-07-02 21:27:20 [info] : [Debug Manager] Removed breakpoint: main 
2025-07-02 21:27:20 [info] : [Debug Manager] Removed breakpoint: _exit 
2025-07-02 21:27:20 [info] : [Debug Manager] Launch session has terminated on the configuration 'hello_world_app_hw_1 [hello_world]' 
2025-07-02 21:27:48 [info] : [Debug Manager] Successfully verified the target connection used  Local 
2025-07-02 21:27:48 [info] : [Debug Manager] Starting launch in 'debug' mode using configuration: {"type":"tcf-debug","request":"launch","name":"hello_world_app_hw_1","debugType":"baremetal-zynq","attachToRunningTargetOptions":{"targetSetupMode":"standalone","executeScript":true,"scriptPath":""},"autoAttachProcessChildren":false,"target":{"targetConnectionId":"Local","peersIniPath":"../../_ide/.peers.ini","context":"zynq"},"pathMap":[],"targetSetup":{"resetSystem":true,"programDevice":true,"resetAPU":false,"bitstreamFile":"${workspaceFolder}\\hello_world\\_ide\\bitstream\\ZynqDesign_wrapper.bit","zynqInitialization":{"isFsbl":false,"usingFSBL":{"initWithFSBL":true,"fsblFile":"${workspaceFolder}\\platform\\export\\platform\\sw\\boot\\fsbl.elf","fsblExitSymbol":"FsblHandoffJtagExit"},"usingPs7Init":{"runPs7Init":true,"runPs7PostInit":true,"ps7InitTclFile":"${workspaceFolder}\\hello_world\\_ide\\psinit\\ps7_init.tcl"}},"downloadElf":[{"core":"ps7_cortexa9_0","resetProcessor":true,"elfFile":"C:\\Users\\<USER>\\Documents\\Works\\ML007\\tool_ide_1\\workspace\\TA\\arm_xc7z0x0_le_hard\\make\\TA.elf","stopAtEntry":false}],"crossTriggerBreakpoints":{"isSelected":false,"breakpoints":[]}},"internalConsoleOptions":"openOnSessionStart","__configUri":"file:///c%3A/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/hello_world/_ide/launch.json"} 
2025-07-02 21:27:48 [info] : [Debug Manager] Added breakpoint:main 
2025-07-02 21:27:48 [info] : [Debug Manager] Added breakpoint:_exit 
2025-07-02 21:27:48 [info] : [Debug Manager] Launch session has started on the configuration 'hello_world_app_hw_1 [hello_world]' 
2025-07-02 21:29:40 [info] : [Debug Manager] Removed breakpoint: main 
2025-07-02 21:29:40 [info] : [Debug Manager] Removed breakpoint: _exit 
2025-07-02 21:29:40 [info] : [Debug Manager] Launch session has terminated on the configuration 'hello_world_app_hw_1 [hello_world]' 
2025-07-02 21:30:09 [info] : [Debug Manager] Successfully verified the target connection used  Local 
2025-07-02 21:30:09 [info] : [Debug Manager] Starting launch in 'debug' mode using configuration: {"type":"tcf-debug","request":"launch","name":"hello_world_app_hw_1","debugType":"baremetal-zynq","attachToRunningTargetOptions":{"targetSetupMode":"standalone","executeScript":true,"scriptPath":""},"autoAttachProcessChildren":false,"target":{"targetConnectionId":"Local","peersIniPath":"../../_ide/.peers.ini","context":"zynq"},"pathMap":[],"targetSetup":{"resetSystem":true,"programDevice":true,"resetAPU":false,"bitstreamFile":"${workspaceFolder}\\hello_world\\_ide\\bitstream\\ZynqDesign_wrapper.bit","zynqInitialization":{"isFsbl":false,"usingFSBL":{"initWithFSBL":true,"fsblFile":"${workspaceFolder}\\platform\\export\\platform\\sw\\boot\\fsbl.elf","fsblExitSymbol":"FsblHandoffJtagExit"},"usingPs7Init":{"runPs7Init":true,"runPs7PostInit":true,"ps7InitTclFile":"${workspaceFolder}\\hello_world\\_ide\\psinit\\ps7_init.tcl"}},"downloadElf":[{"core":"ps7_cortexa9_0","resetProcessor":true,"elfFile":"C:\\Users\\<USER>\\Documents\\Works\\ML007\\tool_ide_1\\workspace\\TA\\arm_xc7z0x0_le_hard\\make\\TA.elf","stopAtEntry":false}],"crossTriggerBreakpoints":{"isSelected":false,"breakpoints":[]}},"internalConsoleOptions":"openOnSessionStart","__configUri":"file:///c%3A/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/hello_world/_ide/launch.json"} 
2025-07-02 21:30:09 [info] : [Debug Manager] Added breakpoint:main 
2025-07-02 21:30:09 [info] : [Debug Manager] Added breakpoint:_exit 
2025-07-02 21:30:09 [info] : [Debug Manager] Launch session has started on the configuration 'hello_world_app_hw_1 [hello_world]' 
2025-07-02 21:32:29 [info] : [Debug Manager] Removed breakpoint: main 
2025-07-02 21:32:29 [info] : [Debug Manager] Removed breakpoint: _exit 
2025-07-02 21:32:29 [info] : [Debug Manager] Launch session has terminated on the configuration 'hello_world_app_hw_1 [hello_world]' 
2025-07-02 21:34:10 [info] : [Program Flash Server] command program_flash -f C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\workspace\app\arm_xc7z0x0_le_hard_123131\make\app.bin -offset 0x600000 -flash_type qspi-x4-single -fsbl C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\platform\zynq_fsbl\build\fsbl.elf -verify -url TCP:127.0.0.1:3121 
2025-07-02 21:35:19 [info] : [Debug Manager] Successfully verified the target connection used  Local 
2025-07-02 21:35:19 [info] : [Debug Manager] Starting launch in 'debug' mode using configuration: {"type":"tcf-debug","request":"launch","name":"hello_world_app_hw_1","debugType":"baremetal-zynq","attachToRunningTargetOptions":{"targetSetupMode":"standalone","executeScript":true,"scriptPath":""},"autoAttachProcessChildren":false,"target":{"targetConnectionId":"Local","peersIniPath":"../../_ide/.peers.ini","context":"zynq"},"pathMap":[],"targetSetup":{"resetSystem":true,"programDevice":true,"resetAPU":false,"bitstreamFile":"${workspaceFolder}\\hello_world\\_ide\\bitstream\\ZynqDesign_wrapper.bit","zynqInitialization":{"isFsbl":false,"usingFSBL":{"initWithFSBL":true,"fsblFile":"${workspaceFolder}\\platform\\export\\platform\\sw\\boot\\fsbl.elf","fsblExitSymbol":"FsblHandoffJtagExit"},"usingPs7Init":{"runPs7Init":true,"runPs7PostInit":true,"ps7InitTclFile":"${workspaceFolder}\\hello_world\\_ide\\psinit\\ps7_init.tcl"}},"downloadElf":[{"core":"ps7_cortexa9_0","resetProcessor":true,"elfFile":"C:\\Users\\<USER>\\Documents\\Works\\ML007\\tool_ide_1\\workspace\\TA\\arm_xc7z0x0_le_hard\\make\\TA.elf","stopAtEntry":false}],"crossTriggerBreakpoints":{"isSelected":false,"breakpoints":[]}},"internalConsoleOptions":"openOnSessionStart","__configUri":"file:///c%3A/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/hello_world/_ide/launch.json"} 
2025-07-02 21:35:19 [info] : [Debug Manager] Added breakpoint:main 
2025-07-02 21:35:19 [info] : [Debug Manager] Added breakpoint:_exit 
2025-07-02 21:35:19 [info] : [Debug Manager] Launch session has started on the configuration 'hello_world_app_hw_1 [hello_world]' 
2025-07-02 21:37:24 [info] : [Debug Manager] Removed breakpoint: main 
2025-07-02 21:37:24 [info] : [Debug Manager] Removed breakpoint: _exit 
2025-07-02 21:37:24 [info] : [Debug Manager] Launch session has terminated on the configuration 'hello_world_app_hw_1 [hello_world]' 
2025-07-02 21:37:36 [info] : [Debug Manager] Successfully verified the target connection used  Local 
2025-07-02 21:37:36 [info] : [Debug Manager] Starting launch in 'debug' mode using configuration: {"type":"tcf-debug","request":"launch","name":"hello_world_app_hw_1","debugType":"baremetal-zynq","attachToRunningTargetOptions":{"targetSetupMode":"standalone","executeScript":true,"scriptPath":""},"autoAttachProcessChildren":false,"target":{"targetConnectionId":"Local","peersIniPath":"../../_ide/.peers.ini","context":"zynq"},"pathMap":[],"targetSetup":{"resetSystem":true,"programDevice":true,"resetAPU":false,"bitstreamFile":"${workspaceFolder}\\hello_world\\_ide\\bitstream\\ZynqDesign_wrapper.bit","zynqInitialization":{"isFsbl":false,"usingFSBL":{"initWithFSBL":true,"fsblFile":"${workspaceFolder}\\platform\\export\\platform\\sw\\boot\\fsbl.elf","fsblExitSymbol":"FsblHandoffJtagExit"},"usingPs7Init":{"runPs7Init":true,"runPs7PostInit":true,"ps7InitTclFile":"${workspaceFolder}\\hello_world\\_ide\\psinit\\ps7_init.tcl"}},"downloadElf":[{"core":"ps7_cortexa9_0","resetProcessor":true,"elfFile":"C:\\Users\\<USER>\\Documents\\Works\\ML007\\tool_ide_1\\workspace\\TA\\arm_xc7z0x0_le_hard\\make\\TA.elf","stopAtEntry":false}],"crossTriggerBreakpoints":{"isSelected":false,"breakpoints":[]}},"internalConsoleOptions":"openOnSessionStart","__configUri":"file:///c%3A/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/hello_world/_ide/launch.json"} 
2025-07-02 21:37:36 [info] : [Debug Manager] Added breakpoint:main 
2025-07-02 21:37:36 [info] : [Debug Manager] Added breakpoint:_exit 
2025-07-02 21:37:37 [info] : [Debug Manager] Launch session has started on the configuration 'hello_world_app_hw_1 [hello_world]' 
2025-07-02 21:38:10 [info] : [Debug Manager] Removed breakpoint: main 
2025-07-02 21:38:10 [info] : [Debug Manager] Removed breakpoint: _exit 
2025-07-02 21:38:10 [info] : [Debug Manager] Launch session has terminated on the configuration 'hello_world_app_hw_1 [hello_world]' 
2025-07-02 21:40:03 [info] : [Debug Manager] Successfully verified the target connection used  Local 
2025-07-02 21:40:03 [info] : [Debug Manager] Starting launch in 'debug' mode using configuration: {"type":"tcf-debug","request":"launch","name":"hello_world_app_hw_1","debugType":"baremetal-zynq","attachToRunningTargetOptions":{"targetSetupMode":"standalone","executeScript":true,"scriptPath":""},"autoAttachProcessChildren":false,"target":{"targetConnectionId":"Local","peersIniPath":"../../_ide/.peers.ini","context":"zynq"},"pathMap":[],"targetSetup":{"resetSystem":true,"programDevice":true,"resetAPU":false,"bitstreamFile":"${workspaceFolder}\\hello_world\\_ide\\bitstream\\ZynqDesign_wrapper.bit","zynqInitialization":{"isFsbl":false,"usingFSBL":{"initWithFSBL":true,"fsblFile":"${workspaceFolder}\\platform\\export\\platform\\sw\\boot\\fsbl.elf","fsblExitSymbol":"FsblHandoffJtagExit"},"usingPs7Init":{"runPs7Init":true,"runPs7PostInit":true,"ps7InitTclFile":"${workspaceFolder}\\hello_world\\_ide\\psinit\\ps7_init.tcl"}},"downloadElf":[{"core":"ps7_cortexa9_0","resetProcessor":true,"elfFile":"C:\\Users\\<USER>\\Documents\\Works\\ML007\\tool_ide_1\\workspace\\TA\\arm_xc7z0x0_le_hard\\make\\TA.elf","stopAtEntry":false}],"crossTriggerBreakpoints":{"isSelected":false,"breakpoints":[]}},"internalConsoleOptions":"openOnSessionStart","__configUri":"file:///c%3A/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/hello_world/_ide/launch.json"} 
2025-07-02 21:40:03 [info] : [Debug Manager] Added breakpoint:main 
2025-07-02 21:40:03 [info] : [Debug Manager] Added breakpoint:_exit 
2025-07-02 21:40:04 [info] : [Debug Manager] Launch session has started on the configuration 'hello_world_app_hw_1 [hello_world]' 
2025-07-02 21:41:56 [info] : [Debug Manager] Removed breakpoint: main 
2025-07-02 21:41:56 [info] : [Debug Manager] Removed breakpoint: _exit 
2025-07-02 21:41:56 [info] : [Debug Manager] Launch session has terminated on the configuration 'hello_world_app_hw_1 [hello_world]' 
2025-07-02 21:42:05 [info] : [Debug Manager] Successfully verified the target connection used  Local 
2025-07-02 21:42:05 [info] : [Debug Manager] Starting launch in 'debug' mode using configuration: {"type":"tcf-debug","request":"launch","name":"hello_world_app_hw_1","debugType":"baremetal-zynq","attachToRunningTargetOptions":{"targetSetupMode":"standalone","executeScript":true,"scriptPath":""},"autoAttachProcessChildren":false,"target":{"targetConnectionId":"Local","peersIniPath":"../../_ide/.peers.ini","context":"zynq"},"pathMap":[],"targetSetup":{"resetSystem":true,"programDevice":true,"resetAPU":false,"bitstreamFile":"${workspaceFolder}\\hello_world\\_ide\\bitstream\\ZynqDesign_wrapper.bit","zynqInitialization":{"isFsbl":false,"usingFSBL":{"initWithFSBL":true,"fsblFile":"${workspaceFolder}\\platform\\export\\platform\\sw\\boot\\fsbl.elf","fsblExitSymbol":"FsblHandoffJtagExit"},"usingPs7Init":{"runPs7Init":true,"runPs7PostInit":true,"ps7InitTclFile":"${workspaceFolder}\\hello_world\\_ide\\psinit\\ps7_init.tcl"}},"downloadElf":[{"core":"ps7_cortexa9_0","resetProcessor":true,"elfFile":"C:\\Users\\<USER>\\Documents\\Works\\ML007\\tool_ide_1\\workspace\\TA\\arm_xc7z0x0_le_hard\\make\\TA.elf","stopAtEntry":false}],"crossTriggerBreakpoints":{"isSelected":false,"breakpoints":[]}},"internalConsoleOptions":"openOnSessionStart","__configUri":"file:///c%3A/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/hello_world/_ide/launch.json"} 
2025-07-02 21:42:05 [info] : [Debug Manager] Added breakpoint:main 
2025-07-02 21:42:05 [info] : [Debug Manager] Added breakpoint:_exit 
2025-07-02 21:42:06 [info] : [Debug Manager] Launch session has started on the configuration 'hello_world_app_hw_1 [hello_world]' 
2025-07-02 21:43:47 [info] : [Debug Manager] Removed breakpoint: main 
2025-07-02 21:43:47 [info] : [Debug Manager] Removed breakpoint: _exit 
2025-07-02 21:43:47 [info] : [Debug Manager] Launch session has terminated on the configuration 'hello_world_app_hw_1 [hello_world]' 
2025-07-02 21:43:56 [info] : [Debug Manager] Successfully verified the target connection used  Local 
2025-07-02 21:43:56 [info] : [Debug Manager] Starting launch in 'debug' mode using configuration: {"type":"tcf-debug","request":"launch","name":"hello_world_app_hw_1","debugType":"baremetal-zynq","attachToRunningTargetOptions":{"targetSetupMode":"standalone","executeScript":true,"scriptPath":""},"autoAttachProcessChildren":false,"target":{"targetConnectionId":"Local","peersIniPath":"../../_ide/.peers.ini","context":"zynq"},"pathMap":[],"targetSetup":{"resetSystem":true,"programDevice":true,"resetAPU":false,"bitstreamFile":"${workspaceFolder}\\hello_world\\_ide\\bitstream\\ZynqDesign_wrapper.bit","zynqInitialization":{"isFsbl":false,"usingFSBL":{"initWithFSBL":true,"fsblFile":"${workspaceFolder}\\platform\\export\\platform\\sw\\boot\\fsbl.elf","fsblExitSymbol":"FsblHandoffJtagExit"},"usingPs7Init":{"runPs7Init":true,"runPs7PostInit":true,"ps7InitTclFile":"${workspaceFolder}\\hello_world\\_ide\\psinit\\ps7_init.tcl"}},"downloadElf":[{"core":"ps7_cortexa9_0","resetProcessor":true,"elfFile":"C:\\Users\\<USER>\\Documents\\Works\\ML007\\tool_ide_1\\workspace\\TA\\arm_xc7z0x0_le_hard\\make\\TA.elf","stopAtEntry":false}],"crossTriggerBreakpoints":{"isSelected":false,"breakpoints":[]}},"internalConsoleOptions":"openOnSessionStart","__configUri":"file:///c%3A/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/hello_world/_ide/launch.json"} 
2025-07-02 21:43:56 [info] : [Debug Manager] Added breakpoint:main 
2025-07-02 21:43:56 [info] : [Debug Manager] Added breakpoint:_exit 
2025-07-02 21:43:57 [info] : [Debug Manager] Launch session has started on the configuration 'hello_world_app_hw_1 [hello_world]' 
2025-07-02 21:44:06 [info] : [Debug Manager] Removed breakpoint: main 
2025-07-02 21:44:06 [info] : [Debug Manager] Removed breakpoint: _exit 
2025-07-02 21:44:06 [info] : [Debug Manager] Launch session has terminated on the configuration 'hello_world_app_hw_1 [hello_world]' 
2025-07-02 21:44:16 [info] : [Debug Manager] Successfully verified the target connection used  Local 
2025-07-02 21:44:16 [info] : [Debug Manager] Starting launch in 'debug' mode using configuration: {"type":"tcf-debug","request":"launch","name":"hello_world_app_hw_1","debugType":"baremetal-zynq","attachToRunningTargetOptions":{"targetSetupMode":"standalone","executeScript":true,"scriptPath":""},"autoAttachProcessChildren":false,"target":{"targetConnectionId":"Local","peersIniPath":"../../_ide/.peers.ini","context":"zynq"},"pathMap":[],"targetSetup":{"resetSystem":true,"programDevice":true,"resetAPU":false,"bitstreamFile":"${workspaceFolder}\\hello_world\\_ide\\bitstream\\ZynqDesign_wrapper.bit","zynqInitialization":{"isFsbl":false,"usingFSBL":{"initWithFSBL":true,"fsblFile":"${workspaceFolder}\\platform\\export\\platform\\sw\\boot\\fsbl.elf","fsblExitSymbol":"FsblHandoffJtagExit"},"usingPs7Init":{"runPs7Init":true,"runPs7PostInit":true,"ps7InitTclFile":"${workspaceFolder}\\hello_world\\_ide\\psinit\\ps7_init.tcl"}},"downloadElf":[{"core":"ps7_cortexa9_0","resetProcessor":true,"elfFile":"C:\\Users\\<USER>\\Documents\\Works\\ML007\\tool_ide_1\\workspace\\TA\\arm_xc7z0x0_le_hard\\make\\TA.elf","stopAtEntry":false}],"crossTriggerBreakpoints":{"isSelected":false,"breakpoints":[]}},"internalConsoleOptions":"openOnSessionStart","__configUri":"file:///c%3A/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/hello_world/_ide/launch.json"} 
2025-07-02 21:44:16 [info] : [Debug Manager] Added breakpoint:main 
2025-07-02 21:44:16 [info] : [Debug Manager] Added breakpoint:_exit 
2025-07-02 21:44:17 [info] : [Debug Manager] Launch session has started on the configuration 'hello_world_app_hw_1 [hello_world]' 
2025-07-02 21:45:18 [info] : [Debug Manager] Removed breakpoint: main 
2025-07-02 21:45:18 [info] : [Debug Manager] Removed breakpoint: _exit 
2025-07-02 21:45:18 [info] : [Debug Manager] Launch session has terminated on the configuration 'hello_world_app_hw_1 [hello_world]' 
2025-07-02 21:45:27 [info] : [Debug Manager] Successfully verified the target connection used  Local 
2025-07-02 21:45:27 [info] : [Debug Manager] Starting launch in 'debug' mode using configuration: {"type":"tcf-debug","request":"launch","name":"hello_world_app_hw_1","debugType":"baremetal-zynq","attachToRunningTargetOptions":{"targetSetupMode":"standalone","executeScript":true,"scriptPath":""},"autoAttachProcessChildren":false,"target":{"targetConnectionId":"Local","peersIniPath":"../../_ide/.peers.ini","context":"zynq"},"pathMap":[],"targetSetup":{"resetSystem":true,"programDevice":true,"resetAPU":false,"bitstreamFile":"${workspaceFolder}\\hello_world\\_ide\\bitstream\\ZynqDesign_wrapper.bit","zynqInitialization":{"isFsbl":false,"usingFSBL":{"initWithFSBL":true,"fsblFile":"${workspaceFolder}\\platform\\export\\platform\\sw\\boot\\fsbl.elf","fsblExitSymbol":"FsblHandoffJtagExit"},"usingPs7Init":{"runPs7Init":true,"runPs7PostInit":true,"ps7InitTclFile":"${workspaceFolder}\\hello_world\\_ide\\psinit\\ps7_init.tcl"}},"downloadElf":[{"core":"ps7_cortexa9_0","resetProcessor":true,"elfFile":"C:\\Users\\<USER>\\Documents\\Works\\ML007\\tool_ide_1\\workspace\\TA\\arm_xc7z0x0_le_hard\\make\\TA.elf","stopAtEntry":false}],"crossTriggerBreakpoints":{"isSelected":false,"breakpoints":[]}},"internalConsoleOptions":"openOnSessionStart","__configUri":"file:///c%3A/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/hello_world/_ide/launch.json"} 
2025-07-02 21:45:28 [info] : [Debug Manager] Added breakpoint:main 
2025-07-02 21:45:28 [info] : [Debug Manager] Added breakpoint:_exit 
2025-07-02 21:45:28 [info] : [Debug Manager] Launch session has started on the configuration 'hello_world_app_hw_1 [hello_world]' 
2025-07-02 21:45:46 [info] : [Debug Manager] Removed breakpoint: main 
2025-07-02 21:45:46 [info] : [Debug Manager] Removed breakpoint: _exit 
2025-07-02 21:45:46 [info] : [Debug Manager] Launch session has terminated on the configuration 'hello_world_app_hw_1 [hello_world]' 
2025-07-02 21:45:56 [info] : [Debug Manager] Successfully verified the target connection used  Local 
2025-07-02 21:45:56 [info] : [Debug Manager] Starting launch in 'debug' mode using configuration: {"type":"tcf-debug","request":"launch","name":"hello_world_app_hw_1","debugType":"baremetal-zynq","attachToRunningTargetOptions":{"targetSetupMode":"standalone","executeScript":true,"scriptPath":""},"autoAttachProcessChildren":false,"target":{"targetConnectionId":"Local","peersIniPath":"../../_ide/.peers.ini","context":"zynq"},"pathMap":[],"targetSetup":{"resetSystem":true,"programDevice":true,"resetAPU":false,"bitstreamFile":"${workspaceFolder}\\hello_world\\_ide\\bitstream\\ZynqDesign_wrapper.bit","zynqInitialization":{"isFsbl":false,"usingFSBL":{"initWithFSBL":true,"fsblFile":"${workspaceFolder}\\platform\\export\\platform\\sw\\boot\\fsbl.elf","fsblExitSymbol":"FsblHandoffJtagExit"},"usingPs7Init":{"runPs7Init":true,"runPs7PostInit":true,"ps7InitTclFile":"${workspaceFolder}\\hello_world\\_ide\\psinit\\ps7_init.tcl"}},"downloadElf":[{"core":"ps7_cortexa9_0","resetProcessor":true,"elfFile":"C:\\Users\\<USER>\\Documents\\Works\\ML007\\tool_ide_1\\workspace\\TA\\arm_xc7z0x0_le_hard\\make\\TA.elf","stopAtEntry":false}],"crossTriggerBreakpoints":{"isSelected":false,"breakpoints":[]}},"internalConsoleOptions":"openOnSessionStart","__configUri":"file:///c%3A/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/hello_world/_ide/launch.json"} 
2025-07-02 21:45:57 [info] : [Debug Manager] Added breakpoint:main 
2025-07-02 21:45:57 [info] : [Debug Manager] Added breakpoint:_exit 
2025-07-02 21:45:57 [info] : [Debug Manager] Launch session has started on the configuration 'hello_world_app_hw_1 [hello_world]' 
2025-07-02 21:51:11 [info] : [Debug Manager] Removed breakpoint: main 
2025-07-02 21:51:11 [info] : [Debug Manager] Removed breakpoint: _exit 
2025-07-02 21:51:11 [info] : [Debug Manager] Launch session has terminated on the configuration 'hello_world_app_hw_1 [hello_world]' 
2025-07-02 21:51:45 [info] : [Program Flash Server] command program_flash -f C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\workspace\app\arm_xc7z0x0_le_hard_123131\make\app.bin -offset 0x600000 -flash_type qspi-x4-single -fsbl C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\platform\zynq_fsbl\build\fsbl.elf -verify -url TCP:127.0.0.1:3121 
2025-07-02 21:52:41 [info] : [Debug Manager] Successfully verified the target connection used  Local 
2025-07-02 21:52:41 [info] : [Debug Manager] Starting launch in 'debug' mode using configuration: {"type":"tcf-debug","request":"launch","name":"hello_world_app_hw_1","debugType":"baremetal-zynq","attachToRunningTargetOptions":{"targetSetupMode":"standalone","executeScript":true,"scriptPath":""},"autoAttachProcessChildren":false,"target":{"targetConnectionId":"Local","peersIniPath":"../../_ide/.peers.ini","context":"zynq"},"pathMap":[],"targetSetup":{"resetSystem":true,"programDevice":true,"resetAPU":false,"bitstreamFile":"${workspaceFolder}\\hello_world\\_ide\\bitstream\\ZynqDesign_wrapper.bit","zynqInitialization":{"isFsbl":false,"usingFSBL":{"initWithFSBL":true,"fsblFile":"${workspaceFolder}\\platform\\export\\platform\\sw\\boot\\fsbl.elf","fsblExitSymbol":"FsblHandoffJtagExit"},"usingPs7Init":{"runPs7Init":true,"runPs7PostInit":true,"ps7InitTclFile":"${workspaceFolder}\\hello_world\\_ide\\psinit\\ps7_init.tcl"}},"downloadElf":[{"core":"ps7_cortexa9_0","resetProcessor":true,"elfFile":"C:\\Users\\<USER>\\Documents\\Works\\ML007\\tool_ide_1\\workspace\\TA\\arm_xc7z0x0_le_hard\\make\\TA.elf","stopAtEntry":false}],"crossTriggerBreakpoints":{"isSelected":false,"breakpoints":[]}},"internalConsoleOptions":"openOnSessionStart","__configUri":"file:///c%3A/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/hello_world/_ide/launch.json"} 
2025-07-02 21:52:41 [info] : [Debug Manager] Added breakpoint:main 
2025-07-02 21:52:41 [info] : [Debug Manager] Added breakpoint:_exit 
2025-07-02 21:52:42 [info] : [Debug Manager] Launch session has started on the configuration 'hello_world_app_hw_1 [hello_world]' 
2025-07-02 21:55:16 [info] : [Debug Manager] Removed breakpoint: main 
2025-07-02 21:55:16 [info] : [Debug Manager] Removed breakpoint: _exit 
2025-07-02 21:55:16 [info] : [Debug Manager] Launch session has terminated on the configuration 'hello_world_app_hw_1 [hello_world]' 
2025-07-02 21:55:40 [info] : [Program Flash Server] command program_flash -f C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\workspace\app\arm_xc7z0x0_le_hard_123131\make\app.bin -offset 0x600000 -flash_type qspi-x4-single -fsbl C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\platform\zynq_fsbl\build\fsbl.elf -verify 
2025-07-02 21:56:23 [info] : [Debug Manager] Successfully verified the target connection used  Local 
2025-07-02 21:56:23 [info] : [Debug Manager] Starting launch in 'debug' mode using configuration: {"type":"tcf-debug","request":"launch","name":"hello_world_app_hw_1","debugType":"baremetal-zynq","attachToRunningTargetOptions":{"targetSetupMode":"standalone","executeScript":true,"scriptPath":""},"autoAttachProcessChildren":false,"target":{"targetConnectionId":"Local","peersIniPath":"../../_ide/.peers.ini","context":"zynq"},"pathMap":[],"targetSetup":{"resetSystem":true,"programDevice":true,"resetAPU":false,"bitstreamFile":"${workspaceFolder}\\hello_world\\_ide\\bitstream\\ZynqDesign_wrapper.bit","zynqInitialization":{"isFsbl":false,"usingFSBL":{"initWithFSBL":true,"fsblFile":"${workspaceFolder}\\platform\\export\\platform\\sw\\boot\\fsbl.elf","fsblExitSymbol":"FsblHandoffJtagExit"},"usingPs7Init":{"runPs7Init":true,"runPs7PostInit":true,"ps7InitTclFile":"${workspaceFolder}\\hello_world\\_ide\\psinit\\ps7_init.tcl"}},"downloadElf":[{"core":"ps7_cortexa9_0","resetProcessor":true,"elfFile":"C:\\Users\\<USER>\\Documents\\Works\\ML007\\tool_ide_1\\workspace\\TA\\arm_xc7z0x0_le_hard\\make\\TA.elf","stopAtEntry":false}],"crossTriggerBreakpoints":{"isSelected":false,"breakpoints":[]}},"internalConsoleOptions":"openOnSessionStart","__configUri":"file:///c%3A/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/hello_world/_ide/launch.json"} 
2025-07-02 21:56:23 [info] : [Debug Manager] Added breakpoint:main 
2025-07-02 21:56:23 [info] : [Debug Manager] Added breakpoint:_exit 
2025-07-02 21:56:24 [info] : [Debug Manager] Launch session has started on the configuration 'hello_world_app_hw_1 [hello_world]' 
2025-07-02 21:56:35 [info] : [Debug Manager] Removed breakpoint: main 
2025-07-02 21:56:35 [info] : [Debug Manager] Removed breakpoint: _exit 
2025-07-02 21:56:35 [info] : [Debug Manager] Launch session has terminated on the configuration 'hello_world_app_hw_1 [hello_world]' 
2025-07-02 22:19:21 [info] : [Program Flash Server] command program_flash -f C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\workspace\app\arm_xc7z0x0_le_hard_123131\make\app.bin -offset 0x600000 -flash_type qspi-x4-single -fsbl C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\platform\zynq_fsbl\build\fsbl.elf -verify -url TCP:127.0.0.1:3121 
2025-07-02 22:20:51 [info] : [Debug Manager] Successfully verified the target connection used  Local 
2025-07-02 22:20:51 [info] : [Debug Manager] Starting launch in 'debug' mode using configuration: {"type":"tcf-debug","request":"launch","name":"hello_world_app_hw_1","debugType":"baremetal-zynq","attachToRunningTargetOptions":{"targetSetupMode":"standalone","executeScript":true,"scriptPath":""},"autoAttachProcessChildren":false,"target":{"targetConnectionId":"Local","peersIniPath":"../../_ide/.peers.ini","context":"zynq"},"pathMap":[],"targetSetup":{"resetSystem":true,"programDevice":true,"resetAPU":false,"bitstreamFile":"${workspaceFolder}\\hello_world\\_ide\\bitstream\\ZynqDesign_wrapper.bit","zynqInitialization":{"isFsbl":false,"usingFSBL":{"initWithFSBL":true,"fsblFile":"${workspaceFolder}\\platform\\export\\platform\\sw\\boot\\fsbl.elf","fsblExitSymbol":"FsblHandoffJtagExit"},"usingPs7Init":{"runPs7Init":true,"runPs7PostInit":true,"ps7InitTclFile":"${workspaceFolder}\\hello_world\\_ide\\psinit\\ps7_init.tcl"}},"downloadElf":[{"core":"ps7_cortexa9_0","resetProcessor":true,"elfFile":"C:\\Users\\<USER>\\Documents\\Works\\ML007\\tool_ide_1\\workspace\\TA\\arm_xc7z0x0_le_hard\\make\\TA.elf","stopAtEntry":false}],"crossTriggerBreakpoints":{"isSelected":false,"breakpoints":[]}},"internalConsoleOptions":"openOnSessionStart","__configUri":"file:///c%3A/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/hello_world/_ide/launch.json"} 
2025-07-02 22:20:51 [info] : [Debug Manager] Added breakpoint:main 
2025-07-02 22:20:51 [info] : [Debug Manager] Added breakpoint:_exit 
2025-07-02 22:20:51 [info] : [Debug Manager] Launch session has started on the configuration 'hello_world_app_hw_1 [hello_world]' 
2025-07-02 22:24:06 [info] : [Program Flash Server] command program_flash -f C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\workspace\app\arm_xc7z0x0_le_hard_123131\make\app.bin -offset 0x600000 -flash_type qspi-x4-single -fsbl C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\platform\zynq_fsbl\build\fsbl.elf -verify -url TCP:127.0.0.1:3121 
2025-07-02 22:24:17 [info] : [Debug Manager] Removed breakpoint: main 
2025-07-02 22:24:17 [info] : [Debug Manager] Removed breakpoint: _exit 
2025-07-02 22:24:17 [info] : [Debug Manager] Launch session has terminated on the configuration 'hello_world_app_hw_1 [hello_world]' 
2025-07-02 22:24:31 [info] : [Debug Manager] Successfully verified the target connection used  Local 
2025-07-02 22:24:31 [info] : [Debug Manager] Starting launch in 'debug' mode using configuration: {"type":"tcf-debug","request":"launch","name":"hello_world_app_hw_1","debugType":"baremetal-zynq","attachToRunningTargetOptions":{"targetSetupMode":"standalone","executeScript":true,"scriptPath":""},"autoAttachProcessChildren":false,"target":{"targetConnectionId":"Local","peersIniPath":"../../_ide/.peers.ini","context":"zynq"},"pathMap":[],"targetSetup":{"resetSystem":true,"programDevice":true,"resetAPU":false,"bitstreamFile":"${workspaceFolder}\\hello_world\\_ide\\bitstream\\ZynqDesign_wrapper.bit","zynqInitialization":{"isFsbl":false,"usingFSBL":{"initWithFSBL":true,"fsblFile":"${workspaceFolder}\\platform\\export\\platform\\sw\\boot\\fsbl.elf","fsblExitSymbol":"FsblHandoffJtagExit"},"usingPs7Init":{"runPs7Init":true,"runPs7PostInit":true,"ps7InitTclFile":"${workspaceFolder}\\hello_world\\_ide\\psinit\\ps7_init.tcl"}},"downloadElf":[{"core":"ps7_cortexa9_0","resetProcessor":true,"elfFile":"C:\\Users\\<USER>\\Documents\\Works\\ML007\\tool_ide_1\\workspace\\TA\\arm_xc7z0x0_le_hard\\make\\TA.elf","stopAtEntry":false}],"crossTriggerBreakpoints":{"isSelected":false,"breakpoints":[]}},"internalConsoleOptions":"openOnSessionStart","__configUri":"file:///c%3A/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/hello_world/_ide/launch.json"} 
2025-07-02 22:24:31 [info] : [Debug Manager] Added breakpoint:main 
2025-07-02 22:24:31 [info] : [Debug Manager] Added breakpoint:_exit 
2025-07-02 22:24:32 [info] : [Debug Manager] Launch session has started on the configuration 'hello_world_app_hw_1 [hello_world]' 
2025-07-02 22:24:44 [info] : [Debug Manager] Removed breakpoint: main 
2025-07-02 22:24:44 [info] : [Debug Manager] Removed breakpoint: _exit 
2025-07-02 22:24:44 [info] : [Debug Manager] Launch session has terminated on the configuration 'hello_world_app_hw_1 [hello_world]' 
2025-07-02 22:24:54 [info] : [Debug Manager] Successfully verified the target connection used  Local 
2025-07-02 22:24:54 [info] : [Debug Manager] Starting launch in 'debug' mode using configuration: {"type":"tcf-debug","request":"launch","name":"hello_world_app_hw_1","debugType":"baremetal-zynq","attachToRunningTargetOptions":{"targetSetupMode":"standalone","executeScript":true,"scriptPath":""},"autoAttachProcessChildren":false,"target":{"targetConnectionId":"Local","peersIniPath":"../../_ide/.peers.ini","context":"zynq"},"pathMap":[],"targetSetup":{"resetSystem":true,"programDevice":true,"resetAPU":false,"bitstreamFile":"${workspaceFolder}\\hello_world\\_ide\\bitstream\\ZynqDesign_wrapper.bit","zynqInitialization":{"isFsbl":false,"usingFSBL":{"initWithFSBL":true,"fsblFile":"${workspaceFolder}\\platform\\export\\platform\\sw\\boot\\fsbl.elf","fsblExitSymbol":"FsblHandoffJtagExit"},"usingPs7Init":{"runPs7Init":true,"runPs7PostInit":true,"ps7InitTclFile":"${workspaceFolder}\\hello_world\\_ide\\psinit\\ps7_init.tcl"}},"downloadElf":[{"core":"ps7_cortexa9_0","resetProcessor":true,"elfFile":"C:\\Users\\<USER>\\Documents\\Works\\ML007\\tool_ide_1\\workspace\\TA\\arm_xc7z0x0_le_hard\\make\\TA.elf","stopAtEntry":false}],"crossTriggerBreakpoints":{"isSelected":false,"breakpoints":[]}},"internalConsoleOptions":"openOnSessionStart","__configUri":"file:///c%3A/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/hello_world/_ide/launch.json"} 
2025-07-02 22:24:54 [info] : [Debug Manager] Added breakpoint:main 
2025-07-02 22:24:54 [info] : [Debug Manager] Added breakpoint:_exit 
2025-07-02 22:24:54 [info] : [Debug Manager] Launch session has started on the configuration 'hello_world_app_hw_1 [hello_world]' 
2025-07-02 22:26:10 [info] : [Debug Manager] Removed breakpoint: main 
2025-07-02 22:26:10 [info] : [Debug Manager] Removed breakpoint: _exit 
2025-07-02 22:26:10 [info] : [Debug Manager] Launch session has terminated on the configuration 'hello_world_app_hw_1 [hello_world]' 
2025-07-02 22:37:48 [info] : [Program Flash Server] command program_flash -f C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\workspace\app\arm_xc7z0x0_le_hard_123131\make\app.bin -offset 0x600000 -flash_type qspi-x4-single -fsbl C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\platform\zynq_fsbl\build\fsbl.elf -verify -url TCP:127.0.0.1:3121 
2025-07-02 22:39:28 [info] : [Debug Manager] Successfully verified the target connection used  Local 
2025-07-02 22:39:28 [info] : [Debug Manager] Starting launch in 'debug' mode using configuration: {"type":"tcf-debug","request":"launch","name":"hello_world_app_hw_1","debugType":"baremetal-zynq","attachToRunningTargetOptions":{"targetSetupMode":"standalone","executeScript":true,"scriptPath":""},"autoAttachProcessChildren":false,"target":{"targetConnectionId":"Local","peersIniPath":"../../_ide/.peers.ini","context":"zynq"},"pathMap":[],"targetSetup":{"resetSystem":true,"programDevice":true,"resetAPU":false,"bitstreamFile":"${workspaceFolder}\\hello_world\\_ide\\bitstream\\ZynqDesign_wrapper.bit","zynqInitialization":{"isFsbl":false,"usingFSBL":{"initWithFSBL":true,"fsblFile":"${workspaceFolder}\\platform\\export\\platform\\sw\\boot\\fsbl.elf","fsblExitSymbol":"FsblHandoffJtagExit"},"usingPs7Init":{"runPs7Init":true,"runPs7PostInit":true,"ps7InitTclFile":"${workspaceFolder}\\hello_world\\_ide\\psinit\\ps7_init.tcl"}},"downloadElf":[{"core":"ps7_cortexa9_0","resetProcessor":true,"elfFile":"C:\\Users\\<USER>\\Documents\\Works\\ML007\\tool_ide_1\\workspace\\TA\\arm_xc7z0x0_le_hard\\make\\TA.elf","stopAtEntry":false}],"crossTriggerBreakpoints":{"isSelected":false,"breakpoints":[]}},"internalConsoleOptions":"openOnSessionStart","__configUri":"file:///c%3A/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/hello_world/_ide/launch.json"} 
2025-07-02 22:39:28 [info] : [Debug Manager] Added breakpoint:main 
2025-07-02 22:39:28 [info] : [Debug Manager] Added breakpoint:_exit 
2025-07-02 22:39:28 [info] : [Debug Manager] Launch session has started on the configuration 'hello_world_app_hw_1 [hello_world]' 
2025-07-02 22:39:51 [info] : [Debug Manager] Removed breakpoint: main 
2025-07-02 22:39:51 [info] : [Debug Manager] Removed breakpoint: _exit 
2025-07-02 22:39:51 [info] : [Debug Manager] Launch session has terminated on the configuration 'hello_world_app_hw_1 [hello_world]' 
2025-07-02 22:45:13 [info] : [Platform Server] Updating ZynqDesign_wrapper XSA for platform platform output stream finished - platform. 
2025-07-02 22:45:23 [info] : [Platform Server] Generate platform output stream finished - platform. 
2025-07-04 19:12:34 [info] : [Program Flash Server] command program_flash -f C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\workspace\app2\arm_xc7z0x0_le_hard_1232131\make\app2.bin -offset 0x -flash_type qspi-x4-single -fsbl C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\platform\zynq_fsbl\build\fsbl.elf -url TCP:127.0.0.1:3121 
2025-07-04 19:13:01 [info] : [Program Flash Server] command program_flash -f C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\workspace\app2\arm_xc7z0x0_le_hard_1232131\make\app2.bin -offset 0x600000 -flash_type qspi-x4-single -fsbl C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\platform\zynq_fsbl\build\fsbl.elf -verify -url TCP:127.0.0.1:3121 
2025-07-04 19:14:19 [info] : [Debug Manager] Successfully verified the target connection used  Local 
2025-07-04 19:14:19 [info] : [Debug Manager] Starting launch in 'debug' mode using configuration: {"type":"tcf-debug","request":"launch","name":"hello_world_app_hw_1","debugType":"baremetal-zynq","attachToRunningTargetOptions":{"targetSetupMode":"standalone","executeScript":true,"scriptPath":""},"autoAttachProcessChildren":false,"target":{"targetConnectionId":"Local","peersIniPath":"../../_ide/.peers.ini","context":"zynq"},"pathMap":[],"targetSetup":{"resetSystem":true,"programDevice":true,"resetAPU":false,"bitstreamFile":"${workspaceFolder}\\hello_world\\_ide\\bitstream\\ZynqDesign_wrapper.bit","zynqInitialization":{"isFsbl":false,"usingFSBL":{"initWithFSBL":true,"fsblFile":"${workspaceFolder}\\platform\\export\\platform\\sw\\boot\\fsbl.elf","fsblExitSymbol":"FsblHandoffJtagExit"},"usingPs7Init":{"runPs7Init":true,"runPs7PostInit":true,"ps7InitTclFile":"${workspaceFolder}\\hello_world\\_ide\\psinit\\ps7_init.tcl"}},"downloadElf":[{"core":"ps7_cortexa9_0","resetProcessor":true,"elfFile":"C:\\Users\\<USER>\\Documents\\Works\\ML007\\tool_ide_1\\workspace\\TA\\arm_xc7z0x0_le_hard\\make\\TA.elf","stopAtEntry":false}],"crossTriggerBreakpoints":{"isSelected":false,"breakpoints":[]}},"internalConsoleOptions":"openOnSessionStart","__configUri":"file:///c%3A/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/hello_world/_ide/launch.json"} 
2025-07-04 19:14:20 [info] : [Debug Manager] Added breakpoint:main 
2025-07-04 19:14:20 [info] : [Debug Manager] Added breakpoint:_exit 
2025-07-04 19:14:20 [info] : [Debug Manager] Launch session has started on the configuration 'hello_world_app_hw_1 [hello_world]' 
2025-07-04 19:14:39 [info] : [Debug Manager] Removed breakpoint: main 
2025-07-04 19:14:39 [info] : [Debug Manager] Removed breakpoint: _exit 
2025-07-04 19:14:39 [info] : [Debug Manager] Launch session has terminated on the configuration 'hello_world_app_hw_1 [hello_world]' 
2025-07-04 19:22:30 [info] : [Debug Manager] Successfully verified the target connection used  Local 
2025-07-04 19:22:30 [info] : [Debug Manager] Starting launch in 'debug' mode using configuration: {"type":"tcf-debug","request":"launch","name":"hello_world_app_hw_1","debugType":"baremetal-zynq","attachToRunningTargetOptions":{"targetSetupMode":"standalone","executeScript":true,"scriptPath":""},"autoAttachProcessChildren":false,"target":{"targetConnectionId":"Local","peersIniPath":"../../_ide/.peers.ini","context":"zynq"},"pathMap":[],"targetSetup":{"resetSystem":true,"programDevice":true,"resetAPU":false,"bitstreamFile":"${workspaceFolder}\\hello_world\\_ide\\bitstream\\ZynqDesign_wrapper.bit","zynqInitialization":{"isFsbl":false,"usingFSBL":{"initWithFSBL":true,"fsblFile":"${workspaceFolder}\\platform\\export\\platform\\sw\\boot\\fsbl.elf","fsblExitSymbol":"FsblHandoffJtagExit"},"usingPs7Init":{"runPs7Init":true,"runPs7PostInit":true,"ps7InitTclFile":"${workspaceFolder}\\hello_world\\_ide\\psinit\\ps7_init.tcl"}},"downloadElf":[{"core":"ps7_cortexa9_0","resetProcessor":true,"elfFile":"C:\\Users\\<USER>\\Documents\\Works\\ML007\\tool_ide_1\\workspace\\TA\\arm_xc7z0x0_le_hard\\make\\TA.elf","stopAtEntry":false}],"crossTriggerBreakpoints":{"isSelected":false,"breakpoints":[]}},"internalConsoleOptions":"openOnSessionStart","__configUri":"file:///c%3A/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/hello_world/_ide/launch.json"} 
2025-07-04 19:22:30 [info] : [Debug Manager] Added breakpoint:main 
2025-07-04 19:22:30 [info] : [Debug Manager] Added breakpoint:_exit 
2025-07-04 19:22:31 [info] : [Debug Manager] Launch session has started on the configuration 'hello_world_app_hw_1 [hello_world]' 
2025-07-04 19:22:45 [info] : [Debug Manager] Removed breakpoint: main 
2025-07-04 19:22:45 [info] : [Debug Manager] Removed breakpoint: _exit 
2025-07-04 19:22:45 [info] : [Debug Manager] Launch session has terminated on the configuration 'hello_world_app_hw_1 [hello_world]' 
2025-07-04 19:26:28 [info] : [Debug Manager] Successfully verified the target connection used  Local 
2025-07-04 19:26:28 [info] : [Debug Manager] Starting launch in 'debug' mode using configuration: {"type":"tcf-debug","request":"launch","name":"hello_world_app_hw_1","debugType":"baremetal-zynq","attachToRunningTargetOptions":{"targetSetupMode":"standalone","executeScript":true,"scriptPath":""},"autoAttachProcessChildren":false,"target":{"targetConnectionId":"Local","peersIniPath":"../../_ide/.peers.ini","context":"zynq"},"pathMap":[],"targetSetup":{"resetSystem":true,"programDevice":true,"resetAPU":false,"bitstreamFile":"${workspaceFolder}\\hello_world\\_ide\\bitstream\\ZynqDesign_wrapper.bit","zynqInitialization":{"isFsbl":false,"usingFSBL":{"initWithFSBL":true,"fsblFile":"${workspaceFolder}\\platform\\export\\platform\\sw\\boot\\fsbl.elf","fsblExitSymbol":"FsblHandoffJtagExit"},"usingPs7Init":{"runPs7Init":true,"runPs7PostInit":true,"ps7InitTclFile":"${workspaceFolder}\\hello_world\\_ide\\psinit\\ps7_init.tcl"}},"downloadElf":[{"core":"ps7_cortexa9_0","resetProcessor":true,"elfFile":"C:\\Users\\<USER>\\Documents\\Works\\ML007\\tool_ide_1\\workspace\\TA\\arm_xc7z0x0_le_hard\\make\\TA.elf","stopAtEntry":false}],"crossTriggerBreakpoints":{"isSelected":false,"breakpoints":[]}},"internalConsoleOptions":"openOnSessionStart","__configUri":"file:///c%3A/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/hello_world/_ide/launch.json"} 
2025-07-04 19:26:28 [info] : [Debug Manager] Added breakpoint:main 
2025-07-04 19:26:28 [info] : [Debug Manager] Added breakpoint:_exit 
2025-07-04 19:26:29 [info] : [Debug Manager] Launch session has started on the configuration 'hello_world_app_hw_1 [hello_world]' 
2025-07-04 19:30:23 [info] : [Debug Manager] Removed breakpoint: main 
2025-07-04 19:30:23 [info] : [Debug Manager] Removed breakpoint: _exit 
2025-07-04 19:30:23 [info] : [Debug Manager] Launch session has terminated on the configuration 'hello_world_app_hw_1 [hello_world]' 
2025-07-04 19:30:35 [info] : [Debug Manager] Successfully verified the target connection used  Local 
2025-07-04 19:30:35 [info] : [Debug Manager] Starting launch in 'debug' mode using configuration: {"type":"tcf-debug","request":"launch","name":"hello_world_app_hw_1","debugType":"baremetal-zynq","attachToRunningTargetOptions":{"targetSetupMode":"standalone","executeScript":true,"scriptPath":""},"autoAttachProcessChildren":false,"target":{"targetConnectionId":"Local","peersIniPath":"../../_ide/.peers.ini","context":"zynq"},"pathMap":[],"targetSetup":{"resetSystem":true,"programDevice":true,"resetAPU":false,"bitstreamFile":"${workspaceFolder}\\hello_world\\_ide\\bitstream\\ZynqDesign_wrapper.bit","zynqInitialization":{"isFsbl":false,"usingFSBL":{"initWithFSBL":true,"fsblFile":"${workspaceFolder}\\platform\\export\\platform\\sw\\boot\\fsbl.elf","fsblExitSymbol":"FsblHandoffJtagExit"},"usingPs7Init":{"runPs7Init":true,"runPs7PostInit":true,"ps7InitTclFile":"${workspaceFolder}\\hello_world\\_ide\\psinit\\ps7_init.tcl"}},"downloadElf":[{"core":"ps7_cortexa9_0","resetProcessor":true,"elfFile":"C:\\Users\\<USER>\\Documents\\Works\\ML007\\tool_ide_1\\workspace\\TA\\arm_xc7z0x0_le_hard\\make\\TA.elf","stopAtEntry":false}],"crossTriggerBreakpoints":{"isSelected":false,"breakpoints":[]}},"internalConsoleOptions":"openOnSessionStart","__configUri":"file:///c%3A/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/hello_world/_ide/launch.json"} 
2025-07-04 19:30:35 [info] : [Debug Manager] Added breakpoint:main 
2025-07-04 19:30:35 [info] : [Debug Manager] Added breakpoint:_exit 
2025-07-04 19:30:36 [info] : [Debug Manager] Launch session has started on the configuration 'hello_world_app_hw_1 [hello_world]' 
2025-07-04 19:32:25 [info] : [Debug Manager] Removed breakpoint: main 
2025-07-04 19:32:25 [info] : [Debug Manager] Removed breakpoint: _exit 
2025-07-04 19:32:25 [info] : [Debug Manager] Launch session has terminated on the configuration 'hello_world_app_hw_1 [hello_world]' 
2025-07-04 19:32:37 [info] : [Debug Manager] Successfully verified the target connection used  Local 
2025-07-04 19:32:37 [info] : [Debug Manager] Starting launch in 'debug' mode using configuration: {"type":"tcf-debug","request":"launch","name":"hello_world_app_hw_1","debugType":"baremetal-zynq","attachToRunningTargetOptions":{"targetSetupMode":"standalone","executeScript":true,"scriptPath":""},"autoAttachProcessChildren":false,"target":{"targetConnectionId":"Local","peersIniPath":"../../_ide/.peers.ini","context":"zynq"},"pathMap":[],"targetSetup":{"resetSystem":true,"programDevice":true,"resetAPU":false,"bitstreamFile":"${workspaceFolder}\\hello_world\\_ide\\bitstream\\ZynqDesign_wrapper.bit","zynqInitialization":{"isFsbl":false,"usingFSBL":{"initWithFSBL":true,"fsblFile":"${workspaceFolder}\\platform\\export\\platform\\sw\\boot\\fsbl.elf","fsblExitSymbol":"FsblHandoffJtagExit"},"usingPs7Init":{"runPs7Init":true,"runPs7PostInit":true,"ps7InitTclFile":"${workspaceFolder}\\hello_world\\_ide\\psinit\\ps7_init.tcl"}},"downloadElf":[{"core":"ps7_cortexa9_0","resetProcessor":true,"elfFile":"C:\\Users\\<USER>\\Documents\\Works\\ML007\\tool_ide_1\\workspace\\TA\\arm_xc7z0x0_le_hard\\make\\TA.elf","stopAtEntry":false}],"crossTriggerBreakpoints":{"isSelected":false,"breakpoints":[]}},"internalConsoleOptions":"openOnSessionStart","__configUri":"file:///c%3A/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/hello_world/_ide/launch.json"} 
2025-07-04 19:32:37 [info] : [Debug Manager] Added breakpoint:main 
2025-07-04 19:32:37 [info] : [Debug Manager] Added breakpoint:_exit 
2025-07-04 19:32:37 [info] : [Debug Manager] Launch session has started on the configuration 'hello_world_app_hw_1 [hello_world]' 
2025-07-04 19:33:24 [info] : [Debug Manager] Removed breakpoint: main 
2025-07-04 19:33:24 [info] : [Debug Manager] Removed breakpoint: _exit 
2025-07-04 19:33:24 [info] : [Debug Manager] Launch session has terminated on the configuration 'hello_world_app_hw_1 [hello_world]' 
2025-07-04 19:54:27 [info] : [Debug Manager] Successfully verified the target connection used  Local 
2025-07-04 19:54:27 [info] : [Debug Manager] Starting launch in 'debug' mode using configuration: {"type":"tcf-debug","request":"launch","name":"hello_world_app_hw_1","debugType":"baremetal-zynq","attachToRunningTargetOptions":{"targetSetupMode":"standalone","executeScript":true,"scriptPath":""},"autoAttachProcessChildren":false,"target":{"targetConnectionId":"Local","peersIniPath":"../../_ide/.peers.ini","context":"zynq"},"pathMap":[],"targetSetup":{"resetSystem":true,"programDevice":true,"resetAPU":false,"bitstreamFile":"${workspaceFolder}\\hello_world\\_ide\\bitstream\\ZynqDesign_wrapper.bit","zynqInitialization":{"isFsbl":false,"usingFSBL":{"initWithFSBL":true,"fsblFile":"${workspaceFolder}\\platform\\export\\platform\\sw\\boot\\fsbl.elf","fsblExitSymbol":"FsblHandoffJtagExit"},"usingPs7Init":{"runPs7Init":true,"runPs7PostInit":true,"ps7InitTclFile":"${workspaceFolder}\\hello_world\\_ide\\psinit\\ps7_init.tcl"}},"downloadElf":[{"core":"ps7_cortexa9_0","resetProcessor":true,"elfFile":"C:\\Users\\<USER>\\Documents\\Works\\ML007\\tool_ide_1\\workspace\\TA\\arm_xc7z0x0_le_hard\\make\\TA.elf","stopAtEntry":false}],"crossTriggerBreakpoints":{"isSelected":false,"breakpoints":[]}},"internalConsoleOptions":"openOnSessionStart","__configUri":"file:///c%3A/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/hello_world/_ide/launch.json"} 
2025-07-04 19:54:27 [info] : [Debug Manager] Added breakpoint:main 
2025-07-04 19:54:27 [info] : [Debug Manager] Added breakpoint:_exit 
2025-07-04 19:54:28 [info] : [Debug Manager] Launch session has started on the configuration 'hello_world_app_hw_1 [hello_world]' 
2025-07-04 19:55:14 [info] : [Debug Manager] Removed breakpoint: main 
2025-07-04 19:55:14 [info] : [Debug Manager] Removed breakpoint: _exit 
2025-07-04 19:55:14 [info] : [Debug Manager] Launch session has terminated on the configuration 'hello_world_app_hw_1 [hello_world]' 
2025-07-04 19:57:42 [info] : [Program Flash Server] command program_flash -f C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\workspace\app2\arm_xc7z0x0_le_hard_1232131\make\app2.bin -offset 0x600000 -flash_type qspi-x4-single -fsbl C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\platform\zynq_fsbl\build\fsbl.elf -verify -url TCP:127.0.0.1:3121 
2025-07-04 19:58:31 [info] : [Debug Manager] Successfully verified the target connection used  Local 
2025-07-04 19:58:31 [info] : [Debug Manager] Starting launch in 'debug' mode using configuration: {"type":"tcf-debug","request":"launch","name":"hello_world_app_hw_1","debugType":"baremetal-zynq","attachToRunningTargetOptions":{"targetSetupMode":"standalone","executeScript":true,"scriptPath":""},"autoAttachProcessChildren":false,"target":{"targetConnectionId":"Local","peersIniPath":"../../_ide/.peers.ini","context":"zynq"},"pathMap":[],"targetSetup":{"resetSystem":true,"programDevice":true,"resetAPU":false,"bitstreamFile":"${workspaceFolder}\\hello_world\\_ide\\bitstream\\ZynqDesign_wrapper.bit","zynqInitialization":{"isFsbl":false,"usingFSBL":{"initWithFSBL":true,"fsblFile":"${workspaceFolder}\\platform\\export\\platform\\sw\\boot\\fsbl.elf","fsblExitSymbol":"FsblHandoffJtagExit"},"usingPs7Init":{"runPs7Init":true,"runPs7PostInit":true,"ps7InitTclFile":"${workspaceFolder}\\hello_world\\_ide\\psinit\\ps7_init.tcl"}},"downloadElf":[{"core":"ps7_cortexa9_0","resetProcessor":true,"elfFile":"C:\\Users\\<USER>\\Documents\\Works\\ML007\\tool_ide_1\\workspace\\TA\\arm_xc7z0x0_le_hard\\make\\TA.elf","stopAtEntry":false}],"crossTriggerBreakpoints":{"isSelected":false,"breakpoints":[]}},"internalConsoleOptions":"openOnSessionStart","__configUri":"file:///c%3A/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/hello_world/_ide/launch.json"} 
2025-07-04 19:58:31 [info] : [Debug Manager] Added breakpoint:main 
2025-07-04 19:58:31 [info] : [Debug Manager] Added breakpoint:_exit 
2025-07-04 19:58:32 [info] : [Debug Manager] Launch session has started on the configuration 'hello_world_app_hw_1 [hello_world]' 
2025-07-04 20:09:56 [info] : [Debug Manager] Removed breakpoint: main 
2025-07-04 20:09:56 [info] : [Debug Manager] Removed breakpoint: _exit 
2025-07-04 20:09:56 [info] : [Debug Manager] Launch session has terminated on the configuration 'hello_world_app_hw_1 [hello_world]' 
2025-07-04 20:10:23 [info] : [Program Flash Server] command program_flash -f C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\workspace\app2\arm_xc7z0x0_le_hard_1232131\make\app2.bin -offset 0x600000 -flash_type qspi-x4-single -fsbl C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\platform\zynq_fsbl\build\fsbl.elf -verify -url TCP:127.0.0.1:3121 
2025-07-04 20:12:49 [info] : [Debug Manager] Successfully verified the target connection used  Local 
2025-07-04 20:12:49 [info] : [Debug Manager] Starting launch in 'debug' mode using configuration: {"type":"tcf-debug","request":"launch","name":"hello_world_app_hw_1","debugType":"baremetal-zynq","attachToRunningTargetOptions":{"targetSetupMode":"standalone","executeScript":true,"scriptPath":""},"autoAttachProcessChildren":false,"target":{"targetConnectionId":"Local","peersIniPath":"../../_ide/.peers.ini","context":"zynq"},"pathMap":[],"targetSetup":{"resetSystem":true,"programDevice":true,"resetAPU":false,"bitstreamFile":"${workspaceFolder}\\hello_world\\_ide\\bitstream\\ZynqDesign_wrapper.bit","zynqInitialization":{"isFsbl":false,"usingFSBL":{"initWithFSBL":true,"fsblFile":"${workspaceFolder}\\platform\\export\\platform\\sw\\boot\\fsbl.elf","fsblExitSymbol":"FsblHandoffJtagExit"},"usingPs7Init":{"runPs7Init":true,"runPs7PostInit":true,"ps7InitTclFile":"${workspaceFolder}\\hello_world\\_ide\\psinit\\ps7_init.tcl"}},"downloadElf":[{"core":"ps7_cortexa9_0","resetProcessor":true,"elfFile":"C:\\Users\\<USER>\\Documents\\Works\\ML007\\tool_ide_1\\workspace\\TA\\arm_xc7z0x0_le_hard\\make\\TA.elf","stopAtEntry":false}],"crossTriggerBreakpoints":{"isSelected":false,"breakpoints":[]}},"internalConsoleOptions":"openOnSessionStart","__configUri":"file:///c%3A/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/hello_world/_ide/launch.json"} 
2025-07-04 20:12:49 [info] : [Debug Manager] Added breakpoint:main 
2025-07-04 20:12:49 [info] : [Debug Manager] Added breakpoint:_exit 
2025-07-04 20:12:50 [info] : [Debug Manager] Launch session has started on the configuration 'hello_world_app_hw_1 [hello_world]' 
2025-07-04 20:14:33 [info] : [Debug Manager] Removed breakpoint: main 
2025-07-04 20:14:33 [info] : [Debug Manager] Removed breakpoint: _exit 
2025-07-04 20:14:33 [info] : [Debug Manager] Launch session has terminated on the configuration 'hello_world_app_hw_1 [hello_world]' 
2025-07-04 20:14:42 [info] : [Debug Manager] Successfully verified the target connection used  Local 
2025-07-04 20:14:42 [info] : [Debug Manager] Starting launch in 'debug' mode using configuration: {"type":"tcf-debug","request":"launch","name":"hello_world_app_hw_1","debugType":"baremetal-zynq","attachToRunningTargetOptions":{"targetSetupMode":"standalone","executeScript":true,"scriptPath":""},"autoAttachProcessChildren":false,"target":{"targetConnectionId":"Local","peersIniPath":"../../_ide/.peers.ini","context":"zynq"},"pathMap":[],"targetSetup":{"resetSystem":true,"programDevice":true,"resetAPU":false,"bitstreamFile":"${workspaceFolder}\\hello_world\\_ide\\bitstream\\ZynqDesign_wrapper.bit","zynqInitialization":{"isFsbl":false,"usingFSBL":{"initWithFSBL":true,"fsblFile":"${workspaceFolder}\\platform\\export\\platform\\sw\\boot\\fsbl.elf","fsblExitSymbol":"FsblHandoffJtagExit"},"usingPs7Init":{"runPs7Init":true,"runPs7PostInit":true,"ps7InitTclFile":"${workspaceFolder}\\hello_world\\_ide\\psinit\\ps7_init.tcl"}},"downloadElf":[{"core":"ps7_cortexa9_0","resetProcessor":true,"elfFile":"C:\\Users\\<USER>\\Documents\\Works\\ML007\\tool_ide_1\\workspace\\TA\\arm_xc7z0x0_le_hard\\make\\TA.elf","stopAtEntry":false}],"crossTriggerBreakpoints":{"isSelected":false,"breakpoints":[]}},"internalConsoleOptions":"openOnSessionStart","__configUri":"file:///c%3A/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/hello_world/_ide/launch.json"} 
2025-07-04 20:14:42 [info] : [Debug Manager] Added breakpoint:main 
2025-07-04 20:14:42 [info] : [Debug Manager] Added breakpoint:_exit 
2025-07-04 20:14:43 [info] : [Debug Manager] Launch session has started on the configuration 'hello_world_app_hw_1 [hello_world]' 
2025-07-04 20:16:59 [info] : [Debug Manager] Removed breakpoint: main 
2025-07-04 20:16:59 [info] : [Debug Manager] Removed breakpoint: _exit 
2025-07-04 20:16:59 [info] : [Debug Manager] Launch session has terminated on the configuration 'hello_world_app_hw_1 [hello_world]' 
2025-07-04 20:17:08 [info] : [Debug Manager] Successfully verified the target connection used  Local 
2025-07-04 20:17:08 [info] : [Debug Manager] Starting launch in 'debug' mode using configuration: {"type":"tcf-debug","request":"launch","name":"hello_world_app_hw_1","debugType":"baremetal-zynq","attachToRunningTargetOptions":{"targetSetupMode":"standalone","executeScript":true,"scriptPath":""},"autoAttachProcessChildren":false,"target":{"targetConnectionId":"Local","peersIniPath":"../../_ide/.peers.ini","context":"zynq"},"pathMap":[],"targetSetup":{"resetSystem":true,"programDevice":true,"resetAPU":false,"bitstreamFile":"${workspaceFolder}\\hello_world\\_ide\\bitstream\\ZynqDesign_wrapper.bit","zynqInitialization":{"isFsbl":false,"usingFSBL":{"initWithFSBL":true,"fsblFile":"${workspaceFolder}\\platform\\export\\platform\\sw\\boot\\fsbl.elf","fsblExitSymbol":"FsblHandoffJtagExit"},"usingPs7Init":{"runPs7Init":true,"runPs7PostInit":true,"ps7InitTclFile":"${workspaceFolder}\\hello_world\\_ide\\psinit\\ps7_init.tcl"}},"downloadElf":[{"core":"ps7_cortexa9_0","resetProcessor":true,"elfFile":"C:\\Users\\<USER>\\Documents\\Works\\ML007\\tool_ide_1\\workspace\\TA\\arm_xc7z0x0_le_hard\\make\\TA.elf","stopAtEntry":false}],"crossTriggerBreakpoints":{"isSelected":false,"breakpoints":[]}},"internalConsoleOptions":"openOnSessionStart","__configUri":"file:///c%3A/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/hello_world/_ide/launch.json"} 
2025-07-04 20:17:08 [info] : [Debug Manager] Added breakpoint:main 
2025-07-04 20:17:08 [info] : [Debug Manager] Added breakpoint:_exit 
2025-07-04 20:17:09 [info] : [Debug Manager] Launch session has started on the configuration 'hello_world_app_hw_1 [hello_world]' 
2025-07-04 20:19:06 [info] : [Debug Manager] Removed breakpoint: main 
2025-07-04 20:19:06 [info] : [Debug Manager] Removed breakpoint: _exit 
2025-07-04 20:19:06 [info] : [Debug Manager] Launch session has terminated on the configuration 'hello_world_app_hw_1 [hello_world]' 
2025-07-04 20:38:51 [info] : [Debug Manager] Successfully verified the target connection used  Local 
2025-07-04 20:38:51 [info] : [Debug Manager] Starting launch in 'debug' mode using configuration: {"type":"tcf-debug","request":"launch","name":"hello_world_app_hw_1","debugType":"baremetal-zynq","attachToRunningTargetOptions":{"targetSetupMode":"standalone","executeScript":true,"scriptPath":""},"autoAttachProcessChildren":false,"target":{"targetConnectionId":"Local","peersIniPath":"../../_ide/.peers.ini","context":"zynq"},"pathMap":[],"targetSetup":{"resetSystem":true,"programDevice":true,"resetAPU":false,"bitstreamFile":"${workspaceFolder}\\hello_world\\_ide\\bitstream\\ZynqDesign_wrapper.bit","zynqInitialization":{"isFsbl":false,"usingFSBL":{"initWithFSBL":true,"fsblFile":"${workspaceFolder}\\platform\\export\\platform\\sw\\boot\\fsbl.elf","fsblExitSymbol":"FsblHandoffJtagExit"},"usingPs7Init":{"runPs7Init":true,"runPs7PostInit":true,"ps7InitTclFile":"${workspaceFolder}\\hello_world\\_ide\\psinit\\ps7_init.tcl"}},"downloadElf":[{"core":"ps7_cortexa9_0","resetProcessor":true,"elfFile":"C:\\Users\\<USER>\\Documents\\Works\\ML007\\tool_ide_1\\workspace\\TA\\arm_xc7z0x0_le_hard\\make\\TA.elf","stopAtEntry":false}],"crossTriggerBreakpoints":{"isSelected":false,"breakpoints":[]}},"internalConsoleOptions":"openOnSessionStart","__configUri":"file:///c%3A/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/hello_world/_ide/launch.json"} 
2025-07-04 20:38:51 [info] : [Debug Manager] Added breakpoint:main 
2025-07-04 20:38:51 [info] : [Debug Manager] Added breakpoint:_exit 
2025-07-04 20:38:51 [info] : [Debug Manager] Launch session has started on the configuration 'hello_world_app_hw_1 [hello_world]' 
2025-07-04 20:43:09 [info] : [Debug Manager] Removed breakpoint: main 
2025-07-04 20:43:09 [info] : [Debug Manager] Removed breakpoint: _exit 
2025-07-04 20:43:09 [info] : [Debug Manager] Launch session has terminated on the configuration 'hello_world_app_hw_1 [hello_world]' 
2025-07-04 20:43:20 [info] : [Debug Manager] Successfully verified the target connection used  Local 
2025-07-04 20:43:20 [info] : [Debug Manager] Starting launch in 'debug' mode using configuration: {"type":"tcf-debug","request":"launch","name":"hello_world_app_hw_1","debugType":"baremetal-zynq","attachToRunningTargetOptions":{"targetSetupMode":"standalone","executeScript":true,"scriptPath":""},"autoAttachProcessChildren":false,"target":{"targetConnectionId":"Local","peersIniPath":"../../_ide/.peers.ini","context":"zynq"},"pathMap":[],"targetSetup":{"resetSystem":true,"programDevice":true,"resetAPU":false,"bitstreamFile":"${workspaceFolder}\\hello_world\\_ide\\bitstream\\ZynqDesign_wrapper.bit","zynqInitialization":{"isFsbl":false,"usingFSBL":{"initWithFSBL":true,"fsblFile":"${workspaceFolder}\\platform\\export\\platform\\sw\\boot\\fsbl.elf","fsblExitSymbol":"FsblHandoffJtagExit"},"usingPs7Init":{"runPs7Init":true,"runPs7PostInit":true,"ps7InitTclFile":"${workspaceFolder}\\hello_world\\_ide\\psinit\\ps7_init.tcl"}},"downloadElf":[{"core":"ps7_cortexa9_0","resetProcessor":true,"elfFile":"C:\\Users\\<USER>\\Documents\\Works\\ML007\\tool_ide_1\\workspace\\TA\\arm_xc7z0x0_le_hard\\make\\TA.elf","stopAtEntry":false}],"crossTriggerBreakpoints":{"isSelected":false,"breakpoints":[]}},"internalConsoleOptions":"openOnSessionStart","__configUri":"file:///c%3A/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/hello_world/_ide/launch.json"} 
2025-07-04 20:43:20 [info] : [Debug Manager] Added breakpoint:main 
2025-07-04 20:43:20 [info] : [Debug Manager] Added breakpoint:_exit 
2025-07-04 20:43:21 [info] : [Debug Manager] Launch session has started on the configuration 'hello_world_app_hw_1 [hello_world]' 
2025-07-04 20:47:16 [info] : [Debug Manager] Removed breakpoint: main 
2025-07-04 20:47:16 [info] : [Debug Manager] Removed breakpoint: _exit 
2025-07-04 20:47:16 [info] : [Debug Manager] Launch session has terminated on the configuration 'hello_world_app_hw_1 [hello_world]' 
2025-07-04 20:47:25 [info] : [Debug Manager] Successfully verified the target connection used  Local 
2025-07-04 20:47:25 [info] : [Debug Manager] Starting launch in 'debug' mode using configuration: {"type":"tcf-debug","request":"launch","name":"hello_world_app_hw_1","debugType":"baremetal-zynq","attachToRunningTargetOptions":{"targetSetupMode":"standalone","executeScript":true,"scriptPath":""},"autoAttachProcessChildren":false,"target":{"targetConnectionId":"Local","peersIniPath":"../../_ide/.peers.ini","context":"zynq"},"pathMap":[],"targetSetup":{"resetSystem":true,"programDevice":true,"resetAPU":false,"bitstreamFile":"${workspaceFolder}\\hello_world\\_ide\\bitstream\\ZynqDesign_wrapper.bit","zynqInitialization":{"isFsbl":false,"usingFSBL":{"initWithFSBL":true,"fsblFile":"${workspaceFolder}\\platform\\export\\platform\\sw\\boot\\fsbl.elf","fsblExitSymbol":"FsblHandoffJtagExit"},"usingPs7Init":{"runPs7Init":true,"runPs7PostInit":true,"ps7InitTclFile":"${workspaceFolder}\\hello_world\\_ide\\psinit\\ps7_init.tcl"}},"downloadElf":[{"core":"ps7_cortexa9_0","resetProcessor":true,"elfFile":"C:\\Users\\<USER>\\Documents\\Works\\ML007\\tool_ide_1\\workspace\\TA\\arm_xc7z0x0_le_hard\\make\\TA.elf","stopAtEntry":false}],"crossTriggerBreakpoints":{"isSelected":false,"breakpoints":[]}},"internalConsoleOptions":"openOnSessionStart","__configUri":"file:///c%3A/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/hello_world/_ide/launch.json"} 
2025-07-04 20:47:25 [info] : [Debug Manager] Added breakpoint:main 
2025-07-04 20:47:25 [info] : [Debug Manager] Added breakpoint:_exit 
2025-07-04 20:47:26 [info] : [Debug Manager] Launch session has started on the configuration 'hello_world_app_hw_1 [hello_world]' 
2025-07-04 20:49:11 [info] : [Debug Manager] Removed breakpoint: main 
2025-07-04 20:49:11 [info] : [Debug Manager] Removed breakpoint: _exit 
2025-07-04 20:49:11 [info] : [Debug Manager] Launch session has terminated on the configuration 'hello_world_app_hw_1 [hello_world]' 
2025-07-04 20:49:33 [info] : [Debug Manager] Successfully verified the target connection used  Local 
2025-07-04 20:49:33 [info] : [Debug Manager] Starting launch in 'debug' mode using configuration: {"type":"tcf-debug","request":"launch","name":"hello_world_app_hw_1","debugType":"baremetal-zynq","attachToRunningTargetOptions":{"targetSetupMode":"standalone","executeScript":true,"scriptPath":""},"autoAttachProcessChildren":false,"target":{"targetConnectionId":"Local","peersIniPath":"../../_ide/.peers.ini","context":"zynq"},"pathMap":[],"targetSetup":{"resetSystem":true,"programDevice":true,"resetAPU":false,"bitstreamFile":"${workspaceFolder}\\hello_world\\_ide\\bitstream\\ZynqDesign_wrapper.bit","zynqInitialization":{"isFsbl":false,"usingFSBL":{"initWithFSBL":true,"fsblFile":"${workspaceFolder}\\platform\\export\\platform\\sw\\boot\\fsbl.elf","fsblExitSymbol":"FsblHandoffJtagExit"},"usingPs7Init":{"runPs7Init":true,"runPs7PostInit":true,"ps7InitTclFile":"${workspaceFolder}\\hello_world\\_ide\\psinit\\ps7_init.tcl"}},"downloadElf":[{"core":"ps7_cortexa9_0","resetProcessor":true,"elfFile":"C:\\Users\\<USER>\\Documents\\Works\\ML007\\tool_ide_1\\workspace\\TA\\arm_xc7z0x0_le_hard\\make\\TA.elf","stopAtEntry":false}],"crossTriggerBreakpoints":{"isSelected":false,"breakpoints":[]}},"internalConsoleOptions":"openOnSessionStart","__configUri":"file:///c%3A/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/hello_world/_ide/launch.json"} 
2025-07-04 20:49:33 [info] : [Debug Manager] Added breakpoint:main 
2025-07-04 20:49:33 [info] : [Debug Manager] Added breakpoint:_exit 
2025-07-04 20:49:33 [info] : [Debug Manager] Launch session has started on the configuration 'hello_world_app_hw_1 [hello_world]' 
2025-07-04 20:51:30 [info] : [Debug Manager] Removed breakpoint: main 
2025-07-04 20:51:30 [info] : [Debug Manager] Removed breakpoint: _exit 
2025-07-04 20:51:30 [info] : [Debug Manager] Launch session has terminated on the configuration 'hello_world_app_hw_1 [hello_world]' 
2025-07-04 20:51:49 [info] : [Debug Manager] Successfully verified the target connection used  Local 
2025-07-04 20:51:49 [info] : [Debug Manager] Starting launch in 'debug' mode using configuration: {"type":"tcf-debug","request":"launch","name":"hello_world_app_hw_1","debugType":"baremetal-zynq","attachToRunningTargetOptions":{"targetSetupMode":"standalone","executeScript":true,"scriptPath":""},"autoAttachProcessChildren":false,"target":{"targetConnectionId":"Local","peersIniPath":"../../_ide/.peers.ini","context":"zynq"},"pathMap":[],"targetSetup":{"resetSystem":true,"programDevice":true,"resetAPU":false,"bitstreamFile":"${workspaceFolder}\\hello_world\\_ide\\bitstream\\ZynqDesign_wrapper.bit","zynqInitialization":{"isFsbl":false,"usingFSBL":{"initWithFSBL":true,"fsblFile":"${workspaceFolder}\\platform\\export\\platform\\sw\\boot\\fsbl.elf","fsblExitSymbol":"FsblHandoffJtagExit"},"usingPs7Init":{"runPs7Init":true,"runPs7PostInit":true,"ps7InitTclFile":"${workspaceFolder}\\hello_world\\_ide\\psinit\\ps7_init.tcl"}},"downloadElf":[{"core":"ps7_cortexa9_0","resetProcessor":true,"elfFile":"C:\\Users\\<USER>\\Documents\\Works\\ML007\\tool_ide_1\\workspace\\TA\\arm_xc7z0x0_le_hard\\make\\TA.elf","stopAtEntry":false}],"crossTriggerBreakpoints":{"isSelected":false,"breakpoints":[]}},"internalConsoleOptions":"openOnSessionStart","__configUri":"file:///c%3A/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/hello_world/_ide/launch.json"} 
2025-07-04 20:51:49 [info] : [Debug Manager] Added breakpoint:main 
2025-07-04 20:51:49 [info] : [Debug Manager] Added breakpoint:_exit 
2025-07-04 20:51:50 [info] : [Debug Manager] Launch session has started on the configuration 'hello_world_app_hw_1 [hello_world]' 
2025-07-04 20:54:18 [info] : [Debug Manager] Removed breakpoint: main 
2025-07-04 20:54:18 [info] : [Debug Manager] Removed breakpoint: _exit 
2025-07-04 20:54:18 [info] : [Debug Manager] Launch session has terminated on the configuration 'hello_world_app_hw_1 [hello_world]' 
2025-07-04 20:54:29 [info] : [Debug Manager] Successfully verified the target connection used  Local 
2025-07-04 20:54:29 [info] : [Debug Manager] Starting launch in 'debug' mode using configuration: {"type":"tcf-debug","request":"launch","name":"hello_world_app_hw_1","debugType":"baremetal-zynq","attachToRunningTargetOptions":{"targetSetupMode":"standalone","executeScript":true,"scriptPath":""},"autoAttachProcessChildren":false,"target":{"targetConnectionId":"Local","peersIniPath":"../../_ide/.peers.ini","context":"zynq"},"pathMap":[],"targetSetup":{"resetSystem":true,"programDevice":true,"resetAPU":false,"bitstreamFile":"${workspaceFolder}\\hello_world\\_ide\\bitstream\\ZynqDesign_wrapper.bit","zynqInitialization":{"isFsbl":false,"usingFSBL":{"initWithFSBL":true,"fsblFile":"${workspaceFolder}\\platform\\export\\platform\\sw\\boot\\fsbl.elf","fsblExitSymbol":"FsblHandoffJtagExit"},"usingPs7Init":{"runPs7Init":true,"runPs7PostInit":true,"ps7InitTclFile":"${workspaceFolder}\\hello_world\\_ide\\psinit\\ps7_init.tcl"}},"downloadElf":[{"core":"ps7_cortexa9_0","resetProcessor":true,"elfFile":"C:\\Users\\<USER>\\Documents\\Works\\ML007\\tool_ide_1\\workspace\\TA\\arm_xc7z0x0_le_hard\\make\\TA.elf","stopAtEntry":false}],"crossTriggerBreakpoints":{"isSelected":false,"breakpoints":[]}},"internalConsoleOptions":"openOnSessionStart","__configUri":"file:///c%3A/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/hello_world/_ide/launch.json"} 
2025-07-04 20:54:30 [info] : [Debug Manager] Added breakpoint:main 
2025-07-04 20:54:30 [info] : [Debug Manager] Added breakpoint:_exit 
2025-07-04 20:54:30 [info] : [Debug Manager] Launch session has started on the configuration 'hello_world_app_hw_1 [hello_world]' 
2025-07-04 21:01:58 [info] : [Debug Manager] Removed breakpoint: main 
2025-07-04 21:01:58 [info] : [Debug Manager] Removed breakpoint: _exit 
2025-07-04 21:01:58 [info] : [Debug Manager] Launch session has terminated on the configuration 'hello_world_app_hw_1 [hello_world]' 
2025-07-04 21:02:10 [info] : [Debug Manager] Successfully verified the target connection used  Local 
2025-07-04 21:02:10 [info] : [Debug Manager] Starting launch in 'debug' mode using configuration: {"type":"tcf-debug","request":"launch","name":"hello_world_app_hw_1","debugType":"baremetal-zynq","attachToRunningTargetOptions":{"targetSetupMode":"standalone","executeScript":true,"scriptPath":""},"autoAttachProcessChildren":false,"target":{"targetConnectionId":"Local","peersIniPath":"../../_ide/.peers.ini","context":"zynq"},"pathMap":[],"targetSetup":{"resetSystem":true,"programDevice":true,"resetAPU":false,"bitstreamFile":"${workspaceFolder}\\hello_world\\_ide\\bitstream\\ZynqDesign_wrapper.bit","zynqInitialization":{"isFsbl":false,"usingFSBL":{"initWithFSBL":true,"fsblFile":"${workspaceFolder}\\platform\\export\\platform\\sw\\boot\\fsbl.elf","fsblExitSymbol":"FsblHandoffJtagExit"},"usingPs7Init":{"runPs7Init":true,"runPs7PostInit":true,"ps7InitTclFile":"${workspaceFolder}\\hello_world\\_ide\\psinit\\ps7_init.tcl"}},"downloadElf":[{"core":"ps7_cortexa9_0","resetProcessor":true,"elfFile":"C:\\Users\\<USER>\\Documents\\Works\\ML007\\tool_ide_1\\workspace\\TA\\arm_xc7z0x0_le_hard\\make\\TA.elf","stopAtEntry":false}],"crossTriggerBreakpoints":{"isSelected":false,"breakpoints":[]}},"internalConsoleOptions":"openOnSessionStart","__configUri":"file:///c%3A/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/hello_world/_ide/launch.json"} 
2025-07-04 21:02:11 [info] : [Debug Manager] Added breakpoint:main 
2025-07-04 21:02:11 [info] : [Debug Manager] Added breakpoint:_exit 
2025-07-04 21:02:11 [info] : [Debug Manager] Launch session has started on the configuration 'hello_world_app_hw_1 [hello_world]' 
2025-07-04 21:04:53 [info] : [Debug Manager] Removed breakpoint: main 
2025-07-04 21:04:53 [info] : [Debug Manager] Removed breakpoint: _exit 
2025-07-04 21:04:53 [info] : [Debug Manager] Launch session has terminated on the configuration 'hello_world_app_hw_1 [hello_world]' 
2025-07-04 21:05:26 [info] : [Debug Manager] Successfully verified the target connection used  Local 
2025-07-04 21:05:26 [info] : [Debug Manager] Starting launch in 'debug' mode using configuration: {"type":"tcf-debug","request":"launch","name":"hello_world_app_hw_1","debugType":"baremetal-zynq","attachToRunningTargetOptions":{"targetSetupMode":"standalone","executeScript":true,"scriptPath":""},"autoAttachProcessChildren":false,"target":{"targetConnectionId":"Local","peersIniPath":"../../_ide/.peers.ini","context":"zynq"},"pathMap":[],"targetSetup":{"resetSystem":true,"programDevice":true,"resetAPU":false,"bitstreamFile":"${workspaceFolder}\\hello_world\\_ide\\bitstream\\ZynqDesign_wrapper.bit","zynqInitialization":{"isFsbl":false,"usingFSBL":{"initWithFSBL":true,"fsblFile":"${workspaceFolder}\\platform\\export\\platform\\sw\\boot\\fsbl.elf","fsblExitSymbol":"FsblHandoffJtagExit"},"usingPs7Init":{"runPs7Init":true,"runPs7PostInit":true,"ps7InitTclFile":"${workspaceFolder}\\hello_world\\_ide\\psinit\\ps7_init.tcl"}},"downloadElf":[{"core":"ps7_cortexa9_0","resetProcessor":true,"elfFile":"C:\\Users\\<USER>\\Documents\\Works\\ML007\\tool_ide_1\\workspace\\TA\\arm_xc7z0x0_le_hard\\make\\TA.elf","stopAtEntry":false}],"crossTriggerBreakpoints":{"isSelected":false,"breakpoints":[]}},"internalConsoleOptions":"openOnSessionStart","__configUri":"file:///c%3A/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/hello_world/_ide/launch.json"} 
2025-07-04 21:05:26 [info] : [Debug Manager] Added breakpoint:main 
2025-07-04 21:05:26 [info] : [Debug Manager] Added breakpoint:_exit 
2025-07-04 21:05:27 [info] : [Debug Manager] Launch session has started on the configuration 'hello_world_app_hw_1 [hello_world]' 
2025-07-04 21:16:57 [info] : [Debug Manager] Removed breakpoint: main 
2025-07-04 21:16:57 [info] : [Debug Manager] Removed breakpoint: _exit 
2025-07-04 21:16:57 [info] : [Debug Manager] Launch session has terminated on the configuration 'hello_world_app_hw_1 [hello_world]' 
2025-07-04 21:17:07 [info] : [Debug Manager] Successfully verified the target connection used  Local 
2025-07-04 21:17:07 [info] : [Debug Manager] Starting launch in 'debug' mode using configuration: {"type":"tcf-debug","request":"launch","name":"hello_world_app_hw_1","debugType":"baremetal-zynq","attachToRunningTargetOptions":{"targetSetupMode":"standalone","executeScript":true,"scriptPath":""},"autoAttachProcessChildren":false,"target":{"targetConnectionId":"Local","peersIniPath":"../../_ide/.peers.ini","context":"zynq"},"pathMap":[],"targetSetup":{"resetSystem":true,"programDevice":true,"resetAPU":false,"bitstreamFile":"${workspaceFolder}\\hello_world\\_ide\\bitstream\\ZynqDesign_wrapper.bit","zynqInitialization":{"isFsbl":false,"usingFSBL":{"initWithFSBL":true,"fsblFile":"${workspaceFolder}\\platform\\export\\platform\\sw\\boot\\fsbl.elf","fsblExitSymbol":"FsblHandoffJtagExit"},"usingPs7Init":{"runPs7Init":true,"runPs7PostInit":true,"ps7InitTclFile":"${workspaceFolder}\\hello_world\\_ide\\psinit\\ps7_init.tcl"}},"downloadElf":[{"core":"ps7_cortexa9_0","resetProcessor":true,"elfFile":"C:\\Users\\<USER>\\Documents\\Works\\ML007\\tool_ide_1\\workspace\\TA\\arm_xc7z0x0_le_hard\\make\\TA.elf","stopAtEntry":false}],"crossTriggerBreakpoints":{"isSelected":false,"breakpoints":[]}},"internalConsoleOptions":"openOnSessionStart","__configUri":"file:///c%3A/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/hello_world/_ide/launch.json"} 
2025-07-04 21:17:07 [info] : [Debug Manager] Added breakpoint:main 
2025-07-04 21:17:07 [info] : [Debug Manager] Added breakpoint:_exit 
2025-07-04 21:17:08 [info] : [Debug Manager] Launch session has started on the configuration 'hello_world_app_hw_1 [hello_world]' 
2025-07-06 14:29:01 [info] : [Debug Manager] Successfully verified the target connection used  Local 
2025-07-06 14:29:01 [info] : [Debug Manager] Starting launch in 'debug' mode using configuration: {"type":"tcf-debug","request":"launch","name":"hello_world_app_hw_1","debugType":"baremetal-zynq","attachToRunningTargetOptions":{"targetSetupMode":"standalone","executeScript":true,"scriptPath":""},"autoAttachProcessChildren":false,"target":{"targetConnectionId":"Local","peersIniPath":"../../_ide/.peers.ini","context":"zynq"},"pathMap":[],"targetSetup":{"resetSystem":true,"programDevice":true,"resetAPU":false,"bitstreamFile":"${workspaceFolder}\\hello_world\\_ide\\bitstream\\ZynqDesign_wrapper.bit","zynqInitialization":{"isFsbl":false,"usingFSBL":{"initWithFSBL":true,"fsblFile":"${workspaceFolder}\\platform\\export\\platform\\sw\\boot\\fsbl.elf","fsblExitSymbol":"FsblHandoffJtagExit"},"usingPs7Init":{"runPs7Init":true,"runPs7PostInit":true,"ps7InitTclFile":"${workspaceFolder}\\hello_world\\_ide\\psinit\\ps7_init.tcl"}},"downloadElf":[{"core":"ps7_cortexa9_0","resetProcessor":true,"elfFile":"C:\\Users\\<USER>\\Documents\\Works\\ML007\\tool_ide_1\\workspace\\TA\\arm_xc7z0x0_le_hard\\make\\TA.elf","stopAtEntry":false}],"crossTriggerBreakpoints":{"isSelected":false,"breakpoints":[]}},"internalConsoleOptions":"openOnSessionStart","__configUri":"file:///c%3A/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/hello_world/_ide/launch.json"} 
2025-07-06 14:29:01 [info] : [Debug Manager] Launch session has started on the configuration 'hello_world_app_hw_1 [hello_world]' 
2025-07-06 14:53:06 [info] : [Debug Manager] Removed breakpoint: main 
2025-07-06 14:53:06 [info] : [Debug Manager] Removed breakpoint: _exit 
2025-07-06 14:53:06 [info] : [Debug Manager] Launch session has terminated on the configuration 'hello_world_app_hw_1 [hello_world]' 
2025-07-08 19:27:56 [info] : [Debug Manager] Successfully verified the target connection used  Local 
2025-07-08 19:27:56 [info] : [Debug Manager] Starting launch in 'debug' mode using configuration: {"type":"tcf-debug","request":"launch","name":"hello_world_app_hw_1","debugType":"baremetal-zynq","attachToRunningTargetOptions":{"targetSetupMode":"standalone","executeScript":true,"scriptPath":""},"autoAttachProcessChildren":false,"target":{"targetConnectionId":"Local","peersIniPath":"../../_ide/.peers.ini","context":"zynq"},"pathMap":[],"targetSetup":{"resetSystem":true,"programDevice":true,"resetAPU":false,"bitstreamFile":"${workspaceFolder}\\hello_world\\_ide\\bitstream\\ZynqDesign_wrapper.bit","zynqInitialization":{"isFsbl":false,"usingFSBL":{"initWithFSBL":true,"fsblFile":"${workspaceFolder}\\platform\\export\\platform\\sw\\boot\\fsbl.elf","fsblExitSymbol":"FsblHandoffJtagExit"},"usingPs7Init":{"runPs7Init":true,"runPs7PostInit":true,"ps7InitTclFile":"${workspaceFolder}\\hello_world\\_ide\\psinit\\ps7_init.tcl"}},"downloadElf":[{"core":"ps7_cortexa9_0","resetProcessor":true,"elfFile":"C:\\Users\\<USER>\\Documents\\Works\\ML007\\tool_ide_1\\workspace\\TA\\arm_xc7z0x0_le_hard\\make\\TA.elf","stopAtEntry":false}],"crossTriggerBreakpoints":{"isSelected":false,"breakpoints":[]}},"internalConsoleOptions":"openOnSessionStart","__configUri":"file:///c%3A/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/hello_world/_ide/launch.json"} 
2025-07-08 19:27:56 [info] : [Debug Manager] Added breakpoint:main 
2025-07-08 19:27:56 [info] : [Debug Manager] Added breakpoint:_exit 
2025-07-08 19:27:57 [info] : [Debug Manager] Launch session has started on the configuration 'hello_world_app_hw_1 [hello_world]' 
2025-07-08 19:28:01 [info] : [Debug Manager] Removed breakpoint: main 
2025-07-08 19:28:01 [info] : [Debug Manager] Removed breakpoint: _exit 
2025-07-08 19:28:01 [info] : [Debug Manager] Launch session has terminated on the configuration 'hello_world_app_hw_1 [hello_world]' 
2025-07-08 19:33:25 [info] : [Debug Manager] Successfully verified the target connection used  Local 
2025-07-08 19:33:25 [info] : [Debug Manager] Starting launch in 'debug' mode using configuration: {"type":"tcf-debug","request":"launch","name":"hello_world_app_hw_1","debugType":"baremetal-zynq","attachToRunningTargetOptions":{"targetSetupMode":"standalone","executeScript":true,"scriptPath":""},"autoAttachProcessChildren":false,"target":{"targetConnectionId":"Local","peersIniPath":"../../_ide/.peers.ini","context":"zynq"},"pathMap":[],"targetSetup":{"resetSystem":true,"programDevice":true,"resetAPU":false,"bitstreamFile":"${workspaceFolder}\\hello_world\\_ide\\bitstream\\ZynqDesign_wrapper.bit","zynqInitialization":{"isFsbl":false,"usingFSBL":{"initWithFSBL":true,"fsblFile":"${workspaceFolder}\\platform\\export\\platform\\sw\\boot\\fsbl.elf","fsblExitSymbol":"FsblHandoffJtagExit"},"usingPs7Init":{"runPs7Init":true,"runPs7PostInit":true,"ps7InitTclFile":"${workspaceFolder}\\hello_world\\_ide\\psinit\\ps7_init.tcl"}},"downloadElf":[{"core":"ps7_cortexa9_0","resetProcessor":true,"elfFile":"C:\\Users\\<USER>\\Documents\\Works\\ML007\\tool_ide_1\\workspace\\TA\\arm_xc7z0x0_le_hard\\make\\TA.elf","stopAtEntry":false}],"crossTriggerBreakpoints":{"isSelected":false,"breakpoints":[]}},"internalConsoleOptions":"openOnSessionStart","__configUri":"file:///c%3A/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/hello_world/_ide/launch.json"} 
2025-07-08 19:33:26 [info] : [Debug Manager] Added breakpoint:main 
2025-07-08 19:33:26 [info] : [Debug Manager] Added breakpoint:_exit 
2025-07-08 19:33:26 [info] : [Debug Manager] Launch session has started on the configuration 'hello_world_app_hw_1 [hello_world]' 
2025-07-09 19:49:00 [info] : [Debug Manager] Successfully verified the target connection used  Local 
2025-07-09 19:49:00 [info] : [Debug Manager] Starting launch in 'run' mode using configuration: {"type":"tcf-debug","request":"launch","name":"hello_world_app_hw_1","debugType":"baremetal-zynq","attachToRunningTargetOptions":{"targetSetupMode":"standalone","executeScript":true,"scriptPath":""},"autoAttachProcessChildren":false,"target":{"targetConnectionId":"Local","peersIniPath":"../../_ide/.peers.ini","context":"zynq"},"pathMap":[],"targetSetup":{"resetSystem":true,"programDevice":true,"resetAPU":false,"bitstreamFile":"${workspaceFolder}\\hello_world\\_ide\\bitstream\\ZynqDesign_wrapper.bit","zynqInitialization":{"isFsbl":false,"usingFSBL":{"initWithFSBL":true,"fsblFile":"${workspaceFolder}\\platform\\export\\platform\\sw\\boot\\fsbl.elf","fsblExitSymbol":"FsblHandoffJtagExit"},"usingPs7Init":{"runPs7Init":true,"runPs7PostInit":true,"ps7InitTclFile":"${workspaceFolder}\\hello_world\\_ide\\psinit\\ps7_init.tcl"}},"downloadElf":[{"core":"ps7_cortexa9_0","resetProcessor":true,"elfFile":"C:\\Users\\<USER>\\Documents\\Works\\ML007\\tool_ide_1\\workspace\\TA\\arm_xc7z0x0_le_hard\\make\\TA.elf","stopAtEntry":false}],"crossTriggerBreakpoints":{"isSelected":false,"breakpoints":[]}},"internalConsoleOptions":"openOnSessionStart","__configUri":"file:///c%3A/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/hello_world/_ide/launch.json"} 
2025-07-09 19:49:01 [info] : [Debug Manager] Disabling breakpoints in the tool to enable run mode for 'hello_world_app_hw_1 [hello_world]'. 
2025-07-09 19:49:03 [info] : [Debug Manager] Launch session has started on the configuration 'hello_world_app_hw_1 [hello_world]' 
2025-07-09 19:51:57 [info] : [Debug Manager] Launch session has terminated on the configuration 'hello_world_app_hw_1 [hello_world]' 
2025-07-09 19:51:57 [info] : [Debug Manager] Enabling breakpoints after running the application using 'hello_world_app_hw_1 [hello_world]'. 
2025-07-09 19:52:20 [info] : [Debug Manager] Successfully verified the target connection used  Local 
2025-07-09 19:52:20 [info] : [Debug Manager] Starting launch in 'run' mode using configuration: {"type":"tcf-debug","request":"launch","name":"hello_world_app_hw_1","debugType":"baremetal-zynq","attachToRunningTargetOptions":{"targetSetupMode":"standalone","executeScript":true,"scriptPath":""},"autoAttachProcessChildren":false,"target":{"targetConnectionId":"Local","peersIniPath":"../../_ide/.peers.ini","context":"zynq"},"pathMap":[],"targetSetup":{"resetSystem":true,"programDevice":true,"resetAPU":false,"bitstreamFile":"${workspaceFolder}\\hello_world\\_ide\\bitstream\\ZynqDesign_wrapper.bit","zynqInitialization":{"isFsbl":false,"usingFSBL":{"initWithFSBL":true,"fsblFile":"${workspaceFolder}\\platform\\export\\platform\\sw\\boot\\fsbl.elf","fsblExitSymbol":"FsblHandoffJtagExit"},"usingPs7Init":{"runPs7Init":true,"runPs7PostInit":true,"ps7InitTclFile":"${workspaceFolder}\\hello_world\\_ide\\psinit\\ps7_init.tcl"}},"downloadElf":[{"core":"ps7_cortexa9_0","resetProcessor":true,"elfFile":"C:\\Users\\<USER>\\Documents\\Works\\ML007\\tool_ide_1\\workspace\\TA\\arm_xc7z0x0_le_hard\\make\\TA.elf","stopAtEntry":false}],"crossTriggerBreakpoints":{"isSelected":false,"breakpoints":[]}},"internalConsoleOptions":"openOnSessionStart","__configUri":"file:///c%3A/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/hello_world/_ide/launch.json"} 
2025-07-09 19:52:20 [info] : [Debug Manager] Disabling breakpoints in the tool to enable run mode for 'hello_world_app_hw_1 [hello_world]'. 
2025-07-09 19:52:22 [info] : [Debug Manager] Launch session has started on the configuration 'hello_world_app_hw_1 [hello_world]' 
2025-07-09 19:53:20 [info] : [Debug Manager] Launch session has terminated on the configuration 'hello_world_app_hw_1 [hello_world]' 
2025-07-09 19:53:20 [info] : [Debug Manager] Enabling breakpoints after running the application using 'hello_world_app_hw_1 [hello_world]'. 
2025-07-09 20:05:00 [info] : [Debug Manager] Successfully verified the target connection used  Local 
2025-07-09 20:05:00 [info] : [Debug Manager] Starting launch in 'run' mode using configuration: {"type":"tcf-debug","request":"launch","name":"hello_world_app_hw_1","debugType":"baremetal-zynq","attachToRunningTargetOptions":{"targetSetupMode":"standalone","executeScript":true,"scriptPath":""},"autoAttachProcessChildren":false,"target":{"targetConnectionId":"Local","peersIniPath":"../../_ide/.peers.ini","context":"zynq"},"pathMap":[],"targetSetup":{"resetSystem":true,"programDevice":true,"resetAPU":false,"bitstreamFile":"${workspaceFolder}\\hello_world\\_ide\\bitstream\\ZynqDesign_wrapper.bit","zynqInitialization":{"isFsbl":false,"usingFSBL":{"initWithFSBL":true,"fsblFile":"${workspaceFolder}\\platform\\export\\platform\\sw\\boot\\fsbl.elf","fsblExitSymbol":"FsblHandoffJtagExit"},"usingPs7Init":{"runPs7Init":true,"runPs7PostInit":true,"ps7InitTclFile":"${workspaceFolder}\\hello_world\\_ide\\psinit\\ps7_init.tcl"}},"downloadElf":[{"core":"ps7_cortexa9_0","resetProcessor":true,"elfFile":"C:\\Users\\<USER>\\Documents\\Works\\ML007\\tool_ide_1\\workspace\\TA\\arm_xc7z0x0_le_hard\\make\\TA.elf","stopAtEntry":false}],"crossTriggerBreakpoints":{"isSelected":false,"breakpoints":[]}},"internalConsoleOptions":"openOnSessionStart","__configUri":"file:///c%3A/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/hello_world/_ide/launch.json"} 
2025-07-09 20:05:00 [info] : [Debug Manager] Disabling breakpoints in the tool to enable run mode for 'hello_world_app_hw_1 [hello_world]'. 
2025-07-09 20:05:02 [info] : [Debug Manager] Launch session has started on the configuration 'hello_world_app_hw_1 [hello_world]' 
2025-07-09 20:12:23 [info] : [Debug Manager] Successfully verified the target connection used  Local 
2025-07-09 20:12:23 [info] : [Debug Manager] Starting launch in 'run' mode using configuration: {"type":"tcf-debug","request":"launch","name":"hello_world_app_hw_1","debugType":"baremetal-zynq","attachToRunningTargetOptions":{"targetSetupMode":"standalone","executeScript":true,"scriptPath":""},"autoAttachProcessChildren":false,"target":{"targetConnectionId":"Local","peersIniPath":"../../_ide/.peers.ini","context":"zynq"},"pathMap":[],"targetSetup":{"resetSystem":true,"programDevice":true,"resetAPU":false,"bitstreamFile":"${workspaceFolder}\\hello_world\\_ide\\bitstream\\ZynqDesign_wrapper.bit","zynqInitialization":{"isFsbl":false,"usingFSBL":{"initWithFSBL":true,"fsblFile":"${workspaceFolder}\\platform\\export\\platform\\sw\\boot\\fsbl.elf","fsblExitSymbol":"FsblHandoffJtagExit"},"usingPs7Init":{"runPs7Init":true,"runPs7PostInit":true,"ps7InitTclFile":"${workspaceFolder}\\hello_world\\_ide\\psinit\\ps7_init.tcl"}},"downloadElf":[{"core":"ps7_cortexa9_0","resetProcessor":true,"elfFile":"C:\\Users\\<USER>\\Documents\\Works\\ML007\\tool_ide_1\\workspace\\TA\\arm_xc7z0x0_le_hard\\make\\TA.elf","stopAtEntry":false}],"crossTriggerBreakpoints":{"isSelected":false,"breakpoints":[]}},"internalConsoleOptions":"openOnSessionStart","__configUri":"file:///c%3A/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/hello_world/_ide/launch.json"} 
2025-07-09 20:12:31 [info] : [Debug Manager] Launch session has terminated on the configuration 'hello_world_app_hw_1 [hello_world]' 
2025-07-09 20:12:31 [info] : [Debug Manager] Enabling breakpoints after running the application using 'hello_world_app_hw_1 [hello_world]'. 
2025-07-09 21:14:22 [info] : [Debug Manager] Successfully verified the target connection used  Local 
2025-07-09 21:14:22 [info] : [Debug Manager] Starting launch in 'run' mode using configuration: {"type":"tcf-debug","request":"launch","name":"hello_world_app_hw_1","debugType":"baremetal-zynq","attachToRunningTargetOptions":{"targetSetupMode":"standalone","executeScript":true,"scriptPath":""},"autoAttachProcessChildren":false,"target":{"targetConnectionId":"Local","peersIniPath":"../../_ide/.peers.ini","context":"zynq"},"pathMap":[],"targetSetup":{"resetSystem":true,"programDevice":true,"resetAPU":false,"bitstreamFile":"${workspaceFolder}\\hello_world\\_ide\\bitstream\\ZynqDesign_wrapper.bit","zynqInitialization":{"isFsbl":false,"usingFSBL":{"initWithFSBL":true,"fsblFile":"${workspaceFolder}\\platform\\export\\platform\\sw\\boot\\fsbl.elf","fsblExitSymbol":"FsblHandoffJtagExit"},"usingPs7Init":{"runPs7Init":true,"runPs7PostInit":true,"ps7InitTclFile":"${workspaceFolder}\\hello_world\\_ide\\psinit\\ps7_init.tcl"}},"downloadElf":[{"core":"ps7_cortexa9_0","resetProcessor":true,"elfFile":"C:\\Users\\<USER>\\Documents\\Works\\ML007\\tool_ide_1\\workspace\\TA\\arm_xc7z0x0_le_hard\\make\\TA.elf","stopAtEntry":false}],"crossTriggerBreakpoints":{"isSelected":false,"breakpoints":[]}},"internalConsoleOptions":"openOnSessionStart","__configUri":"file:///c%3A/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/hello_world/_ide/launch.json"} 
2025-07-09 21:14:23 [info] : [Debug Manager] Disabling breakpoints in the tool to enable run mode for 'hello_world_app_hw_1 [hello_world]'. 
2025-07-09 21:14:26 [info] : [Debug Manager] Launch session has started on the configuration 'hello_world_app_hw_1 [hello_world]' 
2025-07-09 21:15:40 [info] : [Debug Manager] Launch session has terminated on the configuration 'hello_world_app_hw_1 [hello_world]' 
2025-07-09 21:15:40 [info] : [Debug Manager] Enabling breakpoints after running the application using 'hello_world_app_hw_1 [hello_world]'. 
2025-07-09 21:15:58 [info] : [Debug Manager] Successfully verified the target connection used  Local 
2025-07-09 21:15:58 [info] : [Debug Manager] Starting launch in 'run' mode using configuration: {"type":"tcf-debug","request":"launch","name":"hello_world_app_hw_1","debugType":"baremetal-zynq","attachToRunningTargetOptions":{"targetSetupMode":"standalone","executeScript":true,"scriptPath":""},"autoAttachProcessChildren":false,"target":{"targetConnectionId":"Local","peersIniPath":"../../_ide/.peers.ini","context":"zynq"},"pathMap":[],"targetSetup":{"resetSystem":true,"programDevice":true,"resetAPU":false,"bitstreamFile":"${workspaceFolder}\\hello_world\\_ide\\bitstream\\ZynqDesign_wrapper.bit","zynqInitialization":{"isFsbl":false,"usingFSBL":{"initWithFSBL":true,"fsblFile":"${workspaceFolder}\\platform\\export\\platform\\sw\\boot\\fsbl.elf","fsblExitSymbol":"FsblHandoffJtagExit"},"usingPs7Init":{"runPs7Init":true,"runPs7PostInit":true,"ps7InitTclFile":"${workspaceFolder}\\hello_world\\_ide\\psinit\\ps7_init.tcl"}},"downloadElf":[{"core":"ps7_cortexa9_0","resetProcessor":true,"elfFile":"C:\\Users\\<USER>\\Documents\\Works\\ML007\\tool_ide_1\\workspace\\TA\\arm_xc7z0x0_le_hard\\make\\TA.elf","stopAtEntry":false}],"crossTriggerBreakpoints":{"isSelected":false,"breakpoints":[]}},"internalConsoleOptions":"openOnSessionStart","__configUri":"file:///c%3A/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/hello_world/_ide/launch.json"} 
2025-07-09 21:15:59 [info] : [Debug Manager] Disabling breakpoints in the tool to enable run mode for 'hello_world_app_hw_1 [hello_world]'. 
2025-07-09 21:16:03 [info] : [Debug Manager] Launch session has started on the configuration 'hello_world_app_hw_1 [hello_world]' 
2025-07-09 21:16:06 [info] : [Debug Manager] Launch session has terminated on the configuration 'hello_world_app_hw_1 [hello_world]' 
2025-07-09 21:16:06 [info] : [Debug Manager] Enabling breakpoints after running the application using 'hello_world_app_hw_1 [hello_world]'. 
2025-07-09 21:19:05 [info] : [Debug Manager] Successfully verified the target connection used  Local 
2025-07-09 21:19:05 [info] : [Debug Manager] Starting launch in 'run' mode using configuration: {"type":"tcf-debug","request":"launch","name":"hello_world_app_hw_1","debugType":"baremetal-zynq","attachToRunningTargetOptions":{"targetSetupMode":"standalone","executeScript":true,"scriptPath":""},"autoAttachProcessChildren":false,"target":{"targetConnectionId":"Local","peersIniPath":"../../_ide/.peers.ini","context":"zynq"},"pathMap":[],"targetSetup":{"resetSystem":true,"programDevice":true,"resetAPU":false,"bitstreamFile":"${workspaceFolder}\\hello_world\\_ide\\bitstream\\ZynqDesign_wrapper.bit","zynqInitialization":{"isFsbl":false,"usingFSBL":{"initWithFSBL":true,"fsblFile":"${workspaceFolder}\\platform\\export\\platform\\sw\\boot\\fsbl.elf","fsblExitSymbol":"FsblHandoffJtagExit"},"usingPs7Init":{"runPs7Init":true,"runPs7PostInit":true,"ps7InitTclFile":"${workspaceFolder}\\hello_world\\_ide\\psinit\\ps7_init.tcl"}},"downloadElf":[{"core":"ps7_cortexa9_0","resetProcessor":true,"elfFile":"C:\\Users\\<USER>\\Documents\\Works\\ML007\\tool_ide_1\\workspace\\TA\\arm_xc7z0x0_le_hard\\make\\TA.elf","stopAtEntry":false}],"crossTriggerBreakpoints":{"isSelected":false,"breakpoints":[]}},"internalConsoleOptions":"openOnSessionStart","__configUri":"file:///c%3A/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/hello_world/_ide/launch.json"} 
2025-07-09 21:19:06 [info] : [Debug Manager] Disabling breakpoints in the tool to enable run mode for 'hello_world_app_hw_1 [hello_world]'. 
2025-07-09 21:19:09 [info] : [Debug Manager] Launch session has started on the configuration 'hello_world_app_hw_1 [hello_world]' 
2025-07-09 21:21:33 [info] : [Debug Manager] Launch session has terminated on the configuration 'hello_world_app_hw_1 [hello_world]' 
2025-07-09 21:21:33 [info] : [Debug Manager] Enabling breakpoints after running the application using 'hello_world_app_hw_1 [hello_world]'. 
2025-07-09 21:22:28 [info] : [Debug Manager] Successfully verified the target connection used  Local 
2025-07-09 21:22:28 [info] : [Debug Manager] Starting launch in 'run' mode using configuration: {"type":"tcf-debug","request":"launch","name":"hello_world_app_hw_1","debugType":"baremetal-zynq","attachToRunningTargetOptions":{"targetSetupMode":"standalone","executeScript":true,"scriptPath":""},"autoAttachProcessChildren":false,"target":{"targetConnectionId":"Local","peersIniPath":"../../_ide/.peers.ini","context":"zynq"},"pathMap":[],"targetSetup":{"resetSystem":true,"programDevice":true,"resetAPU":false,"bitstreamFile":"${workspaceFolder}\\hello_world\\_ide\\bitstream\\ZynqDesign_wrapper.bit","zynqInitialization":{"isFsbl":false,"usingFSBL":{"initWithFSBL":true,"fsblFile":"${workspaceFolder}\\platform\\export\\platform\\sw\\boot\\fsbl.elf","fsblExitSymbol":"FsblHandoffJtagExit"},"usingPs7Init":{"runPs7Init":true,"runPs7PostInit":true,"ps7InitTclFile":"${workspaceFolder}\\hello_world\\_ide\\psinit\\ps7_init.tcl"}},"downloadElf":[{"core":"ps7_cortexa9_0","resetProcessor":true,"elfFile":"C:\\Users\\<USER>\\Documents\\Works\\ML007\\tool_ide_1\\workspace\\TA\\arm_xc7z0x0_le_hard\\make\\TA.elf","stopAtEntry":false}],"crossTriggerBreakpoints":{"isSelected":false,"breakpoints":[]}},"internalConsoleOptions":"openOnSessionStart","__configUri":"file:///c%3A/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/hello_world/_ide/launch.json"} 
2025-07-09 21:22:30 [info] : [Debug Manager] Disabling breakpoints in the tool to enable run mode for 'hello_world_app_hw_1 [hello_world]'. 
2025-07-09 21:22:32 [info] : [Debug Manager] Launch session has started on the configuration 'hello_world_app_hw_1 [hello_world]' 
2025-07-09 21:57:25 [info] : [Platform Server] Generate platform output stream finished - platform. 
2025-07-09 21:57:44 [info] : [Debug Manager] Successfully verified the target connection used  Local 
2025-07-09 21:57:44 [info] : [Debug Manager] Starting launch in 'run' mode using configuration: {"type":"tcf-debug","request":"launch","name":"hello_world_app_hw_1","debugType":"baremetal-zynq","attachToRunningTargetOptions":{"targetSetupMode":"standalone","executeScript":true,"scriptPath":""},"autoAttachProcessChildren":false,"target":{"targetConnectionId":"Local","peersIniPath":"../../_ide/.peers.ini","context":"zynq"},"pathMap":[],"targetSetup":{"resetSystem":true,"programDevice":true,"resetAPU":false,"bitstreamFile":"${workspaceFolder}\\hello_world\\_ide\\bitstream\\ZynqDesign_wrapper.bit","zynqInitialization":{"isFsbl":false,"usingFSBL":{"initWithFSBL":true,"fsblFile":"${workspaceFolder}\\platform\\export\\platform\\sw\\boot\\fsbl.elf","fsblExitSymbol":"FsblHandoffJtagExit"},"usingPs7Init":{"runPs7Init":true,"runPs7PostInit":true,"ps7InitTclFile":"${workspaceFolder}\\hello_world\\_ide\\psinit\\ps7_init.tcl"}},"downloadElf":[{"core":"ps7_cortexa9_0","resetProcessor":true,"elfFile":"C:\\Users\\<USER>\\Documents\\Works\\ML007\\tool_ide_1\\workspace\\TA\\arm_xc7z0x0_le_hard\\make\\TA.elf","stopAtEntry":false}],"crossTriggerBreakpoints":{"isSelected":false,"breakpoints":[]}},"internalConsoleOptions":"openOnSessionStart","__configUri":"file:///c%3A/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/hello_world/_ide/launch.json"} 
2025-07-09 21:57:44 [info] : [Debug Manager] Disabling breakpoints in the tool to enable run mode for 'hello_world_app_hw_1 [hello_world]'. 
2025-07-09 21:57:44 [info] : [Debug Manager] Launch session has started on the configuration 'hello_world_app_hw_1 [hello_world]' 
2025-07-09 21:57:48 [info] : [Debug Manager] Launch session has terminated on the configuration 'hello_world_app_hw_1 [hello_world]' 
2025-07-09 21:57:48 [info] : [Debug Manager] Enabling breakpoints after running the application using 'hello_world_app_hw_1 [hello_world]'. 
2025-07-09 22:09:35 [info] : [Debug Manager] Successfully verified the target connection used  Local 
2025-07-09 22:09:35 [info] : [Debug Manager] Starting launch in 'run' mode using configuration: {"type":"tcf-debug","request":"launch","name":"hello_world_app_hw_1","debugType":"baremetal-zynq","attachToRunningTargetOptions":{"targetSetupMode":"standalone","executeScript":true,"scriptPath":""},"autoAttachProcessChildren":false,"target":{"targetConnectionId":"Local","peersIniPath":"../../_ide/.peers.ini","context":"zynq"},"pathMap":[],"targetSetup":{"resetSystem":true,"programDevice":true,"resetAPU":false,"bitstreamFile":"${workspaceFolder}\\hello_world\\_ide\\bitstream\\ZynqDesign_wrapper.bit","zynqInitialization":{"isFsbl":false,"usingFSBL":{"initWithFSBL":true,"fsblFile":"${workspaceFolder}\\platform\\export\\platform\\sw\\boot\\fsbl.elf","fsblExitSymbol":"FsblHandoffJtagExit"},"usingPs7Init":{"runPs7Init":true,"runPs7PostInit":true,"ps7InitTclFile":"${workspaceFolder}\\hello_world\\_ide\\psinit\\ps7_init.tcl"}},"downloadElf":[{"core":"ps7_cortexa9_0","resetProcessor":true,"elfFile":"C:\\Users\\<USER>\\Documents\\Works\\ML007\\tool_ide_1\\workspace\\TA\\arm_xc7z0x0_le_hard\\make\\TA.elf","stopAtEntry":false}],"crossTriggerBreakpoints":{"isSelected":false,"breakpoints":[]}},"internalConsoleOptions":"openOnSessionStart","__configUri":"file:///c%3A/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/hello_world/_ide/launch.json"} 
2025-07-09 22:09:35 [info] : [Debug Manager] Disabling breakpoints in the tool to enable run mode for 'hello_world_app_hw_1 [hello_world]'. 
2025-07-09 22:09:36 [info] : [Debug Manager] Launch session has started on the configuration 'hello_world_app_hw_1 [hello_world]' 
2025-07-09 22:10:30 [info] : [Debug Manager] Successfully verified the target connection used  Local 
2025-07-09 22:10:30 [info] : [Debug Manager] Starting launch in 'run' mode using configuration: {"type":"tcf-debug","request":"launch","name":"hello_world_app_hw_1","debugType":"baremetal-zynq","attachToRunningTargetOptions":{"targetSetupMode":"standalone","executeScript":true,"scriptPath":""},"autoAttachProcessChildren":false,"target":{"targetConnectionId":"Local","peersIniPath":"../../_ide/.peers.ini","context":"zynq"},"pathMap":[],"targetSetup":{"resetSystem":true,"programDevice":true,"resetAPU":false,"bitstreamFile":"${workspaceFolder}\\hello_world\\_ide\\bitstream\\ZynqDesign_wrapper.bit","zynqInitialization":{"isFsbl":false,"usingFSBL":{"initWithFSBL":true,"fsblFile":"${workspaceFolder}\\platform\\export\\platform\\sw\\boot\\fsbl.elf","fsblExitSymbol":"FsblHandoffJtagExit"},"usingPs7Init":{"runPs7Init":true,"runPs7PostInit":true,"ps7InitTclFile":"${workspaceFolder}\\hello_world\\_ide\\psinit\\ps7_init.tcl"}},"downloadElf":[{"core":"ps7_cortexa9_0","resetProcessor":true,"elfFile":"C:\\Users\\<USER>\\Documents\\Works\\ML007\\tool_ide_1\\workspace\\TA\\arm_xc7z0x0_le_hard\\make\\TA.elf","stopAtEntry":false}],"crossTriggerBreakpoints":{"isSelected":false,"breakpoints":[]}},"internalConsoleOptions":"openOnSessionStart","__configUri":"file:///c%3A/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/hello_world/_ide/launch.json"} 
2025-07-09 22:12:23 [info] : [Debug Manager] Launch session has terminated on the configuration 'hello_world_app_hw_1 [hello_world]' 
2025-07-09 22:12:23 [info] : [Debug Manager] Enabling breakpoints after running the application using 'hello_world_app_hw_1 [hello_world]'. 
2025-07-09 22:12:36 [info] : [Debug Manager] Successfully verified the target connection used  Local 
2025-07-09 22:12:36 [info] : [Debug Manager] Starting launch in 'debug' mode using configuration: {"type":"tcf-debug","request":"launch","name":"hello_world_app_hw_1","debugType":"baremetal-zynq","attachToRunningTargetOptions":{"targetSetupMode":"standalone","executeScript":true,"scriptPath":""},"autoAttachProcessChildren":false,"target":{"targetConnectionId":"Local","peersIniPath":"../../_ide/.peers.ini","context":"zynq"},"pathMap":[],"targetSetup":{"resetSystem":true,"programDevice":true,"resetAPU":false,"bitstreamFile":"${workspaceFolder}\\hello_world\\_ide\\bitstream\\ZynqDesign_wrapper.bit","zynqInitialization":{"isFsbl":false,"usingFSBL":{"initWithFSBL":true,"fsblFile":"${workspaceFolder}\\platform\\export\\platform\\sw\\boot\\fsbl.elf","fsblExitSymbol":"FsblHandoffJtagExit"},"usingPs7Init":{"runPs7Init":true,"runPs7PostInit":true,"ps7InitTclFile":"${workspaceFolder}\\hello_world\\_ide\\psinit\\ps7_init.tcl"}},"downloadElf":[{"core":"ps7_cortexa9_0","resetProcessor":true,"elfFile":"C:\\Users\\<USER>\\Documents\\Works\\ML007\\tool_ide_1\\workspace\\TA\\arm_xc7z0x0_le_hard\\make\\TA.elf","stopAtEntry":false}],"crossTriggerBreakpoints":{"isSelected":false,"breakpoints":[]}},"internalConsoleOptions":"openOnSessionStart","__configUri":"file:///c%3A/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/hello_world/_ide/launch.json"} 
2025-07-09 22:12:36 [info] : [Debug Manager] Launch session has started on the configuration 'hello_world_app_hw_1 [hello_world]' 
2025-07-09 22:13:57 [info] : [Debug Manager] Launch session has terminated on the configuration 'hello_world_app_hw_1 [hello_world]' 
2025-07-09 22:14:06 [info] : [Debug Manager] Successfully verified the target connection used  Local 
2025-07-09 22:14:06 [info] : [Debug Manager] Starting launch in 'debug' mode using configuration: {"type":"tcf-debug","request":"launch","name":"hello_world_app_hw_1","debugType":"baremetal-zynq","attachToRunningTargetOptions":{"targetSetupMode":"standalone","executeScript":true,"scriptPath":""},"autoAttachProcessChildren":false,"target":{"targetConnectionId":"Local","peersIniPath":"../../_ide/.peers.ini","context":"zynq"},"pathMap":[],"targetSetup":{"resetSystem":true,"programDevice":true,"resetAPU":false,"bitstreamFile":"${workspaceFolder}\\hello_world\\_ide\\bitstream\\ZynqDesign_wrapper.bit","zynqInitialization":{"isFsbl":false,"usingFSBL":{"initWithFSBL":true,"fsblFile":"${workspaceFolder}\\platform\\export\\platform\\sw\\boot\\fsbl.elf","fsblExitSymbol":"FsblHandoffJtagExit"},"usingPs7Init":{"runPs7Init":true,"runPs7PostInit":true,"ps7InitTclFile":"${workspaceFolder}\\hello_world\\_ide\\psinit\\ps7_init.tcl"}},"downloadElf":[{"core":"ps7_cortexa9_0","resetProcessor":true,"elfFile":"C:\\Users\\<USER>\\Documents\\Works\\ML007\\tool_ide_1\\workspace\\TA\\arm_xc7z0x0_le_hard\\make\\TA.elf","stopAtEntry":false}],"crossTriggerBreakpoints":{"isSelected":false,"breakpoints":[]}},"internalConsoleOptions":"openOnSessionStart","__configUri":"file:///c%3A/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/hello_world/_ide/launch.json"} 
2025-07-09 22:14:06 [info] : [Debug Manager] Added breakpoint:main 
2025-07-09 22:14:06 [info] : [Debug Manager] Added breakpoint:_exit 
2025-07-09 22:14:07 [info] : [Debug Manager] Launch session has started on the configuration 'hello_world_app_hw_1 [hello_world]' 
2025-07-09 22:16:55 [info] : [Debug Manager] Removed breakpoint: main 
2025-07-09 22:16:55 [info] : [Debug Manager] Removed breakpoint: _exit 
2025-07-09 22:16:55 [info] : [Debug Manager] Launch session has terminated on the configuration 'hello_world_app_hw_1 [hello_world]' 
2025-07-09 22:17:14 [info] : [Debug Manager] Successfully verified the target connection used  Local 
2025-07-09 22:17:14 [info] : [Debug Manager] Starting launch in 'debug' mode using configuration: {"type":"tcf-debug","request":"launch","name":"hello_world_app_hw_1","debugType":"baremetal-zynq","attachToRunningTargetOptions":{"targetSetupMode":"standalone","executeScript":true,"scriptPath":""},"autoAttachProcessChildren":false,"target":{"targetConnectionId":"Local","peersIniPath":"../../_ide/.peers.ini","context":"zynq"},"pathMap":[],"targetSetup":{"resetSystem":true,"programDevice":true,"resetAPU":false,"bitstreamFile":"${workspaceFolder}\\hello_world\\_ide\\bitstream\\ZynqDesign_wrapper.bit","zynqInitialization":{"isFsbl":false,"usingFSBL":{"initWithFSBL":true,"fsblFile":"${workspaceFolder}\\platform\\export\\platform\\sw\\boot\\fsbl.elf","fsblExitSymbol":"FsblHandoffJtagExit"},"usingPs7Init":{"runPs7Init":true,"runPs7PostInit":true,"ps7InitTclFile":"${workspaceFolder}\\hello_world\\_ide\\psinit\\ps7_init.tcl"}},"downloadElf":[{"core":"ps7_cortexa9_0","resetProcessor":true,"elfFile":"C:\\Users\\<USER>\\Documents\\Works\\ML007\\tool_ide_1\\workspace\\TA\\arm_xc7z0x0_le_hard\\make\\TA.elf","stopAtEntry":false}],"crossTriggerBreakpoints":{"isSelected":false,"breakpoints":[]}},"internalConsoleOptions":"openOnSessionStart","__configUri":"file:///c%3A/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/hello_world/_ide/launch.json"} 
2025-07-09 22:17:14 [info] : [Debug Manager] Added breakpoint:main 
2025-07-09 22:17:14 [info] : [Debug Manager] Added breakpoint:_exit 
2025-07-09 22:17:15 [info] : [Debug Manager] Launch session has started on the configuration 'hello_world_app_hw_1 [hello_world]' 
2025-07-09 22:18:15 [info] : [Debug Manager] Removed breakpoint: main 
2025-07-09 22:18:15 [info] : [Debug Manager] Removed breakpoint: _exit 
2025-07-09 22:18:15 [info] : [Debug Manager] Launch session has terminated on the configuration 'hello_world_app_hw_1 [hello_world]' 
2025-07-09 22:19:16 [info] : [Debug Manager] Successfully verified the target connection used  Local 
2025-07-09 22:19:16 [info] : [Debug Manager] Starting launch in 'debug' mode using configuration: {"type":"tcf-debug","request":"launch","name":"hello_world_app_hw_1","debugType":"baremetal-zynq","attachToRunningTargetOptions":{"targetSetupMode":"standalone","executeScript":true,"scriptPath":""},"autoAttachProcessChildren":false,"target":{"targetConnectionId":"Local","peersIniPath":"../../_ide/.peers.ini","context":"zynq"},"pathMap":[],"targetSetup":{"resetSystem":true,"programDevice":true,"resetAPU":false,"bitstreamFile":"${workspaceFolder}\\hello_world\\_ide\\bitstream\\ZynqDesign_wrapper.bit","zynqInitialization":{"isFsbl":false,"usingFSBL":{"initWithFSBL":true,"fsblFile":"${workspaceFolder}\\platform\\export\\platform\\sw\\boot\\fsbl.elf","fsblExitSymbol":"FsblHandoffJtagExit"},"usingPs7Init":{"runPs7Init":true,"runPs7PostInit":true,"ps7InitTclFile":"${workspaceFolder}\\hello_world\\_ide\\psinit\\ps7_init.tcl"}},"downloadElf":[{"core":"ps7_cortexa9_0","resetProcessor":true,"elfFile":"C:\\Users\\<USER>\\Documents\\Works\\ML007\\tool_ide_1\\workspace\\TA\\arm_xc7z0x0_le_hard\\make\\TA.elf","stopAtEntry":false}],"crossTriggerBreakpoints":{"isSelected":false,"breakpoints":[]}},"internalConsoleOptions":"openOnSessionStart","__configUri":"file:///c%3A/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/hello_world/_ide/launch.json"} 
2025-07-09 22:19:16 [info] : [Debug Manager] Added breakpoint:main 
2025-07-09 22:19:16 [info] : [Debug Manager] Added breakpoint:_exit 
2025-07-09 22:19:17 [info] : [Debug Manager] Launch session has started on the configuration 'hello_world_app_hw_1 [hello_world]' 
2025-07-09 22:44:55 [info] : [Debug Manager] Removed breakpoint: main 
2025-07-09 22:44:55 [info] : [Debug Manager] Removed breakpoint: _exit 
2025-07-09 22:44:55 [info] : [Debug Manager] Launch session has terminated on the configuration 'hello_world_app_hw_1 [hello_world]' 
2025-07-09 22:45:25 [info] : [Debug Manager] Successfully verified the target connection used  Local 
2025-07-09 22:45:25 [info] : [Debug Manager] Starting launch in 'debug' mode using configuration: {"type":"tcf-debug","request":"launch","name":"hello_world_app_hw_1","debugType":"baremetal-zynq","attachToRunningTargetOptions":{"targetSetupMode":"standalone","executeScript":true,"scriptPath":""},"autoAttachProcessChildren":false,"target":{"targetConnectionId":"Local","peersIniPath":"../../_ide/.peers.ini","context":"zynq"},"pathMap":[],"targetSetup":{"resetSystem":true,"programDevice":true,"resetAPU":false,"bitstreamFile":"${workspaceFolder}\\hello_world\\_ide\\bitstream\\ZynqDesign_wrapper.bit","zynqInitialization":{"isFsbl":false,"usingFSBL":{"initWithFSBL":true,"fsblFile":"${workspaceFolder}\\platform\\export\\platform\\sw\\boot\\fsbl.elf","fsblExitSymbol":"FsblHandoffJtagExit"},"usingPs7Init":{"runPs7Init":true,"runPs7PostInit":true,"ps7InitTclFile":"${workspaceFolder}\\hello_world\\_ide\\psinit\\ps7_init.tcl"}},"downloadElf":[{"core":"ps7_cortexa9_0","resetProcessor":true,"elfFile":"C:\\Users\\<USER>\\Documents\\Works\\ML007\\tool_ide_1\\workspace\\TA\\arm_xc7z0x0_le_hard\\make\\TA.elf","stopAtEntry":false}],"crossTriggerBreakpoints":{"isSelected":false,"breakpoints":[]}},"internalConsoleOptions":"openOnSessionStart","__configUri":"file:///c%3A/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/hello_world/_ide/launch.json"} 
2025-07-09 22:45:25 [info] : [Debug Manager] Added breakpoint:main 
2025-07-09 22:45:25 [info] : [Debug Manager] Added breakpoint:_exit 
2025-07-09 22:45:26 [info] : [Debug Manager] Launch session has started on the configuration 'hello_world_app_hw_1 [hello_world]' 
2025-07-09 22:45:38 [info] : [Debug Manager] Removed breakpoint: main 
2025-07-09 22:45:38 [info] : [Debug Manager] Removed breakpoint: _exit 
2025-07-09 22:45:38 [info] : [Debug Manager] Launch session has terminated on the configuration 'hello_world_app_hw_1 [hello_world]' 
2025-07-09 22:52:37 [info] : [Debug Manager] Successfully verified the target connection used  Local 
2025-07-09 22:52:37 [info] : [Debug Manager] Starting launch in 'debug' mode using configuration: {"type":"tcf-debug","request":"launch","name":"hello_world_app_hw_1","debugType":"baremetal-zynq","attachToRunningTargetOptions":{"targetSetupMode":"standalone","executeScript":true,"scriptPath":""},"autoAttachProcessChildren":false,"target":{"targetConnectionId":"Local","peersIniPath":"../../_ide/.peers.ini","context":"zynq"},"pathMap":[],"targetSetup":{"resetSystem":true,"programDevice":true,"resetAPU":false,"bitstreamFile":"${workspaceFolder}\\hello_world\\_ide\\bitstream\\ZynqDesign_wrapper.bit","zynqInitialization":{"isFsbl":false,"usingFSBL":{"initWithFSBL":true,"fsblFile":"${workspaceFolder}\\platform\\export\\platform\\sw\\boot\\fsbl.elf","fsblExitSymbol":"FsblHandoffJtagExit"},"usingPs7Init":{"runPs7Init":true,"runPs7PostInit":true,"ps7InitTclFile":"${workspaceFolder}\\hello_world\\_ide\\psinit\\ps7_init.tcl"}},"downloadElf":[{"core":"ps7_cortexa9_0","resetProcessor":true,"elfFile":"C:\\Users\\<USER>\\Documents\\Works\\ML007\\tool_ide_1\\workspace\\TA\\arm_xc7z0x0_le_hard\\make\\TA.elf","stopAtEntry":false}],"crossTriggerBreakpoints":{"isSelected":false,"breakpoints":[]}},"internalConsoleOptions":"openOnSessionStart","__configUri":"file:///c%3A/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/hello_world/_ide/launch.json"} 
2025-07-09 22:52:37 [info] : [Debug Manager] Added breakpoint:main 
2025-07-09 22:52:37 [info] : [Debug Manager] Added breakpoint:_exit 
2025-07-09 22:52:38 [info] : [Debug Manager] Launch session has started on the configuration 'hello_world_app_hw_1 [hello_world]' 
2025-07-09 22:54:43 [info] : [Debug Manager] Launch session has terminated on the configuration 'hello_world_app_hw_1 [hello_world]' 
2025-07-09 22:54:52 [info] : [Debug Manager] Successfully verified the target connection used  Local 
2025-07-09 22:54:52 [info] : [Debug Manager] Starting launch in 'debug' mode using configuration: {"type":"tcf-debug","request":"launch","name":"hello_world_app_hw_1","debugType":"baremetal-zynq","attachToRunningTargetOptions":{"targetSetupMode":"standalone","executeScript":true,"scriptPath":""},"autoAttachProcessChildren":false,"target":{"targetConnectionId":"Local","peersIniPath":"../../_ide/.peers.ini","context":"zynq"},"pathMap":[],"targetSetup":{"resetSystem":true,"programDevice":true,"resetAPU":false,"bitstreamFile":"${workspaceFolder}\\hello_world\\_ide\\bitstream\\ZynqDesign_wrapper.bit","zynqInitialization":{"isFsbl":false,"usingFSBL":{"initWithFSBL":true,"fsblFile":"${workspaceFolder}\\platform\\export\\platform\\sw\\boot\\fsbl.elf","fsblExitSymbol":"FsblHandoffJtagExit"},"usingPs7Init":{"runPs7Init":true,"runPs7PostInit":true,"ps7InitTclFile":"${workspaceFolder}\\hello_world\\_ide\\psinit\\ps7_init.tcl"}},"downloadElf":[{"core":"ps7_cortexa9_0","resetProcessor":true,"elfFile":"C:\\Users\\<USER>\\Documents\\Works\\ML007\\tool_ide_1\\workspace\\TA\\arm_xc7z0x0_le_hard\\make\\TA.elf","stopAtEntry":false}],"crossTriggerBreakpoints":{"isSelected":false,"breakpoints":[]}},"internalConsoleOptions":"openOnSessionStart","__configUri":"file:///c%3A/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/hello_world/_ide/launch.json"} 
2025-07-09 22:54:53 [info] : [Debug Manager] Added breakpoint:main 
2025-07-09 22:54:53 [info] : [Debug Manager] Added breakpoint:_exit 
2025-07-09 22:54:53 [info] : [Debug Manager] Launch session has started on the configuration 'hello_world_app_hw_1 [hello_world]' 
2025-07-09 22:55:07 [info] : [Debug Manager] Removed breakpoint: main 
2025-07-09 22:55:07 [info] : [Debug Manager] Removed breakpoint: _exit 
2025-07-09 22:55:07 [info] : [Debug Manager] Launch session has terminated on the configuration 'hello_world_app_hw_1 [hello_world]' 
2025-07-09 22:55:54 [info] : [Debug Manager] Successfully verified the target connection used  Local 
2025-07-09 22:55:54 [info] : [Debug Manager] Starting launch in 'debug' mode using configuration: {"type":"tcf-debug","request":"launch","name":"hello_world_app_hw_1","debugType":"baremetal-zynq","attachToRunningTargetOptions":{"targetSetupMode":"standalone","executeScript":true,"scriptPath":""},"autoAttachProcessChildren":false,"target":{"targetConnectionId":"Local","peersIniPath":"../../_ide/.peers.ini","context":"zynq"},"pathMap":[],"targetSetup":{"resetSystem":true,"programDevice":true,"resetAPU":false,"bitstreamFile":"${workspaceFolder}\\hello_world\\_ide\\bitstream\\ZynqDesign_wrapper.bit","zynqInitialization":{"isFsbl":false,"usingFSBL":{"initWithFSBL":true,"fsblFile":"${workspaceFolder}\\platform\\export\\platform\\sw\\boot\\fsbl.elf","fsblExitSymbol":"FsblHandoffJtagExit"},"usingPs7Init":{"runPs7Init":true,"runPs7PostInit":true,"ps7InitTclFile":"${workspaceFolder}\\hello_world\\_ide\\psinit\\ps7_init.tcl"}},"downloadElf":[{"core":"ps7_cortexa9_0","resetProcessor":true,"elfFile":"C:\\Users\\<USER>\\Documents\\Works\\ML007\\tool_ide_1\\workspace\\TA\\arm_xc7z0x0_le_hard\\make\\TA.elf","stopAtEntry":false}],"crossTriggerBreakpoints":{"isSelected":false,"breakpoints":[]}},"internalConsoleOptions":"openOnSessionStart","__configUri":"file:///c%3A/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/hello_world/_ide/launch.json"} 
2025-07-09 22:55:54 [info] : [Debug Manager] Added breakpoint:main 
2025-07-09 22:55:54 [info] : [Debug Manager] Added breakpoint:_exit 
2025-07-09 22:55:54 [info] : [Debug Manager] Launch session has started on the configuration 'hello_world_app_hw_1 [hello_world]' 
2025-07-09 23:03:11 [info] : [Debug Manager] Removed breakpoint: main 
2025-07-09 23:03:11 [info] : [Debug Manager] Removed breakpoint: _exit 
2025-07-09 23:03:11 [info] : [Debug Manager] Launch session has terminated on the configuration 'hello_world_app_hw_1 [hello_world]' 
2025-07-09 23:03:20 [info] : [Debug Manager] Successfully verified the target connection used  Local 
2025-07-09 23:03:20 [info] : [Debug Manager] Starting launch in 'debug' mode using configuration: {"type":"tcf-debug","request":"launch","name":"hello_world_app_hw_1","debugType":"baremetal-zynq","attachToRunningTargetOptions":{"targetSetupMode":"standalone","executeScript":true,"scriptPath":""},"autoAttachProcessChildren":false,"target":{"targetConnectionId":"Local","peersIniPath":"../../_ide/.peers.ini","context":"zynq"},"pathMap":[],"targetSetup":{"resetSystem":true,"programDevice":true,"resetAPU":false,"bitstreamFile":"${workspaceFolder}\\hello_world\\_ide\\bitstream\\ZynqDesign_wrapper.bit","zynqInitialization":{"isFsbl":false,"usingFSBL":{"initWithFSBL":true,"fsblFile":"${workspaceFolder}\\platform\\export\\platform\\sw\\boot\\fsbl.elf","fsblExitSymbol":"FsblHandoffJtagExit"},"usingPs7Init":{"runPs7Init":true,"runPs7PostInit":true,"ps7InitTclFile":"${workspaceFolder}\\hello_world\\_ide\\psinit\\ps7_init.tcl"}},"downloadElf":[{"core":"ps7_cortexa9_0","resetProcessor":true,"elfFile":"C:\\Users\\<USER>\\Documents\\Works\\ML007\\tool_ide_1\\workspace\\TA\\arm_xc7z0x0_le_hard\\make\\TA.elf","stopAtEntry":false}],"crossTriggerBreakpoints":{"isSelected":false,"breakpoints":[]}},"internalConsoleOptions":"openOnSessionStart","__configUri":"file:///c%3A/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/hello_world/_ide/launch.json"} 
2025-07-09 23:03:21 [info] : [Debug Manager] Added breakpoint:main 
2025-07-09 23:03:21 [info] : [Debug Manager] Added breakpoint:_exit 
2025-07-09 23:03:21 [info] : [Debug Manager] Launch session has started on the configuration 'hello_world_app_hw_1 [hello_world]' 
2025-07-09 23:14:05 [info] : [Debug Manager] Removed breakpoint: main 
2025-07-09 23:14:05 [info] : [Debug Manager] Removed breakpoint: _exit 
2025-07-09 23:14:05 [info] : [Debug Manager] Launch session has terminated on the configuration 'hello_world_app_hw_1 [hello_world]' 
2025-07-09 23:14:16 [info] : [Debug Manager] Successfully verified the target connection used  Local 
2025-07-09 23:14:16 [info] : [Debug Manager] Starting launch in 'debug' mode using configuration: {"type":"tcf-debug","request":"launch","name":"hello_world_app_hw_1","debugType":"baremetal-zynq","attachToRunningTargetOptions":{"targetSetupMode":"standalone","executeScript":true,"scriptPath":""},"autoAttachProcessChildren":false,"target":{"targetConnectionId":"Local","peersIniPath":"../../_ide/.peers.ini","context":"zynq"},"pathMap":[],"targetSetup":{"resetSystem":true,"programDevice":true,"resetAPU":false,"bitstreamFile":"${workspaceFolder}\\hello_world\\_ide\\bitstream\\ZynqDesign_wrapper.bit","zynqInitialization":{"isFsbl":false,"usingFSBL":{"initWithFSBL":true,"fsblFile":"${workspaceFolder}\\platform\\export\\platform\\sw\\boot\\fsbl.elf","fsblExitSymbol":"FsblHandoffJtagExit"},"usingPs7Init":{"runPs7Init":true,"runPs7PostInit":true,"ps7InitTclFile":"${workspaceFolder}\\hello_world\\_ide\\psinit\\ps7_init.tcl"}},"downloadElf":[{"core":"ps7_cortexa9_0","resetProcessor":true,"elfFile":"C:\\Users\\<USER>\\Documents\\Works\\ML007\\tool_ide_1\\workspace\\TA\\arm_xc7z0x0_le_hard\\make\\TA.elf","stopAtEntry":false}],"crossTriggerBreakpoints":{"isSelected":false,"breakpoints":[]}},"internalConsoleOptions":"openOnSessionStart","__configUri":"file:///c%3A/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/hello_world/_ide/launch.json"} 
2025-07-09 23:14:17 [info] : [Debug Manager] Added breakpoint:main 
2025-07-09 23:14:17 [info] : [Debug Manager] Added breakpoint:_exit 
2025-07-09 23:14:17 [info] : [Debug Manager] Launch session has started on the configuration 'hello_world_app_hw_1 [hello_world]' 
2025-07-09 23:15:44 [info] : [Debug Manager] Removed breakpoint: main 
2025-07-09 23:15:44 [info] : [Debug Manager] Removed breakpoint: _exit 
2025-07-09 23:15:44 [info] : [Debug Manager] Launch session has terminated on the configuration 'hello_world_app_hw_1 [hello_world]' 
2025-07-09 23:15:54 [info] : [Debug Manager] Successfully verified the target connection used  Local 
2025-07-09 23:15:54 [info] : [Debug Manager] Starting launch in 'debug' mode using configuration: {"type":"tcf-debug","request":"launch","name":"hello_world_app_hw_1","debugType":"baremetal-zynq","attachToRunningTargetOptions":{"targetSetupMode":"standalone","executeScript":true,"scriptPath":""},"autoAttachProcessChildren":false,"target":{"targetConnectionId":"Local","peersIniPath":"../../_ide/.peers.ini","context":"zynq"},"pathMap":[],"targetSetup":{"resetSystem":true,"programDevice":true,"resetAPU":false,"bitstreamFile":"${workspaceFolder}\\hello_world\\_ide\\bitstream\\ZynqDesign_wrapper.bit","zynqInitialization":{"isFsbl":false,"usingFSBL":{"initWithFSBL":true,"fsblFile":"${workspaceFolder}\\platform\\export\\platform\\sw\\boot\\fsbl.elf","fsblExitSymbol":"FsblHandoffJtagExit"},"usingPs7Init":{"runPs7Init":true,"runPs7PostInit":true,"ps7InitTclFile":"${workspaceFolder}\\hello_world\\_ide\\psinit\\ps7_init.tcl"}},"downloadElf":[{"core":"ps7_cortexa9_0","resetProcessor":true,"elfFile":"C:\\Users\\<USER>\\Documents\\Works\\ML007\\tool_ide_1\\workspace\\TA\\arm_xc7z0x0_le_hard\\make\\TA.elf","stopAtEntry":false}],"crossTriggerBreakpoints":{"isSelected":false,"breakpoints":[]}},"internalConsoleOptions":"openOnSessionStart","__configUri":"file:///c%3A/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/hello_world/_ide/launch.json"} 
2025-07-09 23:15:54 [info] : [Debug Manager] Added breakpoint:main 
2025-07-09 23:15:54 [info] : [Debug Manager] Added breakpoint:_exit 
2025-07-09 23:15:54 [info] : [Debug Manager] Launch session has started on the configuration 'hello_world_app_hw_1 [hello_world]' 
