@echo off
:: ##############################################################
:: # Name:     wrxmlcfg.bat                                     #
:: # Purpose:  wraps the headless XML configuration for windows #
:: # OS:       Windows                                          #
:: ##############################################################
:: # use sh.exe and launch the more powerful shell script       #
SET NAME=%~n0%
SET DIR=%~dp0%
SET UNIXDIR=%DIR:\=/%
:: # Set the path to ensure the shell script finds all tools    #
set PATH=%DIR%;%PATH%
:: # Finally, call the appropriate shell script                 #
call sh.exe %UNIXDIR%%NAME%.sh %*
