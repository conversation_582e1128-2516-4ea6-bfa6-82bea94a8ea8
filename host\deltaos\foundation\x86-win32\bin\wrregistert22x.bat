@echo off
:: #################################################################
:: # Name:     wrregistert55x.bat                                  #
:: # Purpose:  Register an existing Tornado 5.5 installation to    #
:: #           the Wind River Workbench                            #
:: # OS:       Windows                                             #
:: #################################################################
:: # use sh.exe and launch the more powerful shell script          #
:: # Redirect the output of the which call into the variable ME    #
SET NAME=%~n0%
SET DIR=%~dp0%
SET UNIXDIR=%DIR:\=/%
:: # Set the path to ensure the shell script finds all tools       #
set PATH=%DIR%;%DIR%..\..\..\utilities-1.0\x86-win32\bin;%PATH%
:: # Ensure we have VxWorks 5.5 support installed                  #
:: # Finally, call the appropriate shell script                    #
call sh.exe %UNIXDIR%/%NAME%.sh %*
