@echo off
:: ################################################################
:: # Name:     wrfmpp.bat                                         #
:: # Purpose:  Wrap the Wind River FMPP launch for windows        #
:: # OS:       Windows                                            #
:: ################################################################
:: # use sh.exe and launch the more powerful shell script         #
:: # Redirect the output of the which call into the variable ME   #
SET NAME=%~n0%
SET DIR=%~dp0%
SET UNIXDIR=%DIR:\=/%
:: # Set the path to ensure the shell script finds all tools      #
set PATH=%DIR%;%PATH%
:: # Finally, call the appropriate shell script                   #
call sh.exe %UNIXDIR%/%NAME%.sh %*
