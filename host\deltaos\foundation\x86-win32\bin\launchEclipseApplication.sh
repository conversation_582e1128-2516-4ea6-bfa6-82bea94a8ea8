#!/bin/sh
################################################################
# Name:     launchEclipseApplication.sh                        #
# Purpose:  Launch the Eclipse Application                     #
#           given in argument 0                                #
# OS:       All						       #
################################################################
# Function:	Name:		pParseArgs		       #
#		Purpose:	Parse the arguments to this    #
#		 script and separate them for either eclipse-  #
#                args or vmargs.  The Virtual Machine can be   #
#                given arguments, but only, like the wrwb      #
#                startup exe, as last arguments indicated by   #
#                the "-vmargs" string. Everything after this   #
#		 is treated as vmarg, everything before that   #
#                as eclipsearg!                                #
#		Arguments:	The list of args passed to the #
#		 script.                                       #
################################################################
pParseArgs(){
	VMARGS=`echo $* | grep "\-vmargs" | sed -e 's/.*\-vmargs//g'`
	if [ "$VMARGS" = "" ]; then
		ECLIPSEARGS="$*"
	else
		ECLIPSEARGS=`echo $* | grep "\-vmargs" | sed -e 's/\-vmargs.*//g'`
	fi
	# Try to avoid a workspace being generated for a simple help statement!
	if [ "$ECLIPSEARGS" = "--help" -o "$ECLIPSEARGS" = "-h" ]; then
		ECLIPSEARGS="$ECLIPSEARGS -data @none"
		echo $ECLIPSEARGS
	fi
	overrides_xmx=""
	overrides_xms=""
	overrides_maxperm=""
	overrides_verify=""
	for arg in $VMARGS; do
		if [ "$overrides_xmx" = "" ]; then
			overrides_xmx=`echo $arg | grep "\-Xmx"`
		fi
		if [ "$overrides_xms" = "" ]; then
			overrides_xms=`echo $arg | grep "\-Xms"`
		fi
		if [ "$overrides_maxperm" = "" ]; then
			overrides_maxperm=`echo $arg | grep "\-XX:MaxPermSize"`
		fi
		if [ "$overrides_verify" = "" ]; then
			overrides_verify=`echo $arg | grep "\-Xverify"`
		fi
	done
	if [ "$overrides_xmx" = "" ]; then
		VMARGS="$VMARGS -Xmx384m"
	fi
	if [ "$overrides_xms" = "" ]; then
		VMARGS="$VMARGS -Xms40m"
	fi
	if [ "$overrides_maxperm" = "" ]; then
		VMARGS="$VMARGS -XX:MaxPermSize=128m"
	fi
	if [ "$overrides_verify" = "" ]; then
		VMARGS="$VMARGS -Xverify:none"
	fi
	uname=`uname`
	case $uname in
			Linux)
					WRENV="wrenv.linux"
					PLATFORMOPTIONS="-os linux -arch x86 -ws gtk"
					;;
			SunOS | Solaris)
					WRENV="wrenv.solaris"
					PLATFORMOPTIONS="-os solaris -arch sparc -ws gtk"
					;;
			windowsNT | windows32 | *)
					WRENV="wrenv.exe"
					PLATFORMOPTIONS="-os win32 -arch x86 -ws win32"
					;;
	esac
}
################################################################
# Function:	Name:		pFindWorkbenchExe              #
#	 	Purpose:	find the startup executable    #
#		Arguments:	None                           #
################################################################
pFindExe(){
	JRE_HOME="$ROOT/bin/jre/1.6.0_21/x86-win32"
	WRWB_PATH="$ROOT/bin/eclipse"
	# In cases where there are more launcher plug-ins available use the sort (-reverse) command to find the highest version
	launcher=`ls $WRWB_PATH/plugins | grep org.eclipse.equinox.launcher_ | sort -r | head -1`
	LAUNCH="$JRE_HOME/bin/java -cp $WRWB_PATH/plugins/$launcher -Dosgi.checkConfiguration=false $VMARGS org.eclipse.equinox.launcher.WRWBMain"
}
#########
# Do it #
#########
pParseArgs $*
pFindExe
if [ "$USER_DIR" != "" ]
then
	cd "$USER_DIR" && $LAUNCH -nosplash $PLATFORMOPTIONS -application $ECLIPSEARGS
else
	cd "$ROOT" && $LAUNCH -nosplash $PLATFORMOPTIONS -application $ECLIPSEARGS
fi

#exit_status=`echo $?`
exit_status=`echo 0`
# Exit 13 is a failure in the startup
if [ "$exit_status" = "13" ]; then
	echo "The application failed to start."
	echo "Please check the logfiles for more info:"
	echo "<user-home>/.$WORKBENCH_NAME/configuration/<time-since-1970-in-seconds>.log"
	echo "<workspace-dir>/.metadata/.log or"
fi
exit $exit_status
