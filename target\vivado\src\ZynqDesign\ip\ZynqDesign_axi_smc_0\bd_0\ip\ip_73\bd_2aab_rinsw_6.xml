<?xml version="1.0" encoding="UTF-8"?>
<spirit:component xmlns:xilinx="http://www.xilinx.com" xmlns:spirit="http://www.spiritconsortium.org/XMLSchema/SPIRIT/1685-2009" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <spirit:vendor>xilinx.com</spirit:vendor>
  <spirit:library>customized_ip</spirit:library>
  <spirit:name>bd_2aab_rinsw_6</spirit:name>
  <spirit:version>1.0</spirit:version>
  <spirit:busInterfaces>
    <spirit:busInterface>
      <spirit:name>aclk</spirit:name>
      <spirit:busType spirit:vendor="xilinx.com" spirit:library="signal" spirit:name="clock" spirit:version="1.0"/>
      <spirit:abstractionType spirit:vendor="xilinx.com" spirit:library="signal" spirit:name="clock_rtl" spirit:version="1.0"/>
      <spirit:slave/>
      <spirit:portMaps>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>CLK</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>aclk</spirit:name>
          </spirit:physicalPort>
        </spirit:portMap>
      </spirit:portMaps>
      <spirit:parameters>
        <spirit:parameter>
          <spirit:name>FREQ_HZ</spirit:name>
          <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="BUSIFPARAM_VALUE.ACLK.FREQ_HZ">50000000</spirit:value>
          <spirit:vendorExtensions>
            <xilinx:parameterInfo>
              <xilinx:parameterUsage>none</xilinx:parameterUsage>
            </xilinx:parameterInfo>
          </spirit:vendorExtensions>
        </spirit:parameter>
        <spirit:parameter>
          <spirit:name>FREQ_TOLERANCE_HZ</spirit:name>
          <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="BUSIFPARAM_VALUE.ACLK.FREQ_TOLERANCE_HZ">0</spirit:value>
          <spirit:vendorExtensions>
            <xilinx:parameterInfo>
              <xilinx:parameterUsage>none</xilinx:parameterUsage>
            </xilinx:parameterInfo>
          </spirit:vendorExtensions>
        </spirit:parameter>
        <spirit:parameter>
          <spirit:name>PHASE</spirit:name>
          <spirit:value spirit:format="float" spirit:resolve="generated" spirit:id="BUSIFPARAM_VALUE.ACLK.PHASE">0.0</spirit:value>
          <spirit:vendorExtensions>
            <xilinx:parameterInfo>
              <xilinx:parameterUsage>none</xilinx:parameterUsage>
            </xilinx:parameterInfo>
          </spirit:vendorExtensions>
        </spirit:parameter>
        <spirit:parameter>
          <spirit:name>CLK_DOMAIN</spirit:name>
          <spirit:value spirit:resolve="generated" spirit:id="BUSIFPARAM_VALUE.ACLK.CLK_DOMAIN">ZynqDesign_processing_system7_0_0_FCLK_CLK0</spirit:value>
          <spirit:vendorExtensions>
            <xilinx:parameterInfo>
              <xilinx:parameterUsage>none</xilinx:parameterUsage>
            </xilinx:parameterInfo>
          </spirit:vendorExtensions>
        </spirit:parameter>
        <spirit:parameter>
          <spirit:name>ASSOCIATED_BUSIF</spirit:name>
          <spirit:value spirit:resolve="generated" spirit:id="BUSIFPARAM_VALUE.ACLK.ASSOCIATED_BUSIF">M00_SC:M01_SC:M02_SC:M03_SC:M04_SC:M05_SC:M06_SC:M07_SC:M08_SC:M09_SC:M10_SC:M11_SC:M12_SC:M13_SC:M14_SC:M15_SC:S00_SC:S01_SC:S02_SC:S03_SC:S04_SC:S05_SC:S06_SC:S07_SC:S08_SC:S09_SC:S10_SC:S11_SC:S12_SC:S13_SC:S14_SC:S15_SC</spirit:value>
          <spirit:vendorExtensions>
            <xilinx:parameterInfo>
              <xilinx:parameterUsage>none</xilinx:parameterUsage>
            </xilinx:parameterInfo>
          </spirit:vendorExtensions>
        </spirit:parameter>
        <spirit:parameter>
          <spirit:name>ASSOCIATED_PORT</spirit:name>
          <spirit:value spirit:resolve="generated" spirit:id="BUSIFPARAM_VALUE.ACLK.ASSOCIATED_PORT"/>
          <spirit:vendorExtensions>
            <xilinx:parameterInfo>
              <xilinx:parameterUsage>none</xilinx:parameterUsage>
            </xilinx:parameterInfo>
          </spirit:vendorExtensions>
        </spirit:parameter>
        <spirit:parameter>
          <spirit:name>ASSOCIATED_RESET</spirit:name>
          <spirit:value spirit:resolve="generated" spirit:id="BUSIFPARAM_VALUE.ACLK.ASSOCIATED_RESET"/>
          <spirit:vendorExtensions>
            <xilinx:parameterInfo>
              <xilinx:parameterUsage>none</xilinx:parameterUsage>
            </xilinx:parameterInfo>
          </spirit:vendorExtensions>
        </spirit:parameter>
        <spirit:parameter>
          <spirit:name>INSERT_VIP</spirit:name>
          <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="BUSIFPARAM_VALUE.ACLK.INSERT_VIP">0</spirit:value>
          <spirit:vendorExtensions>
            <xilinx:parameterInfo>
              <xilinx:parameterUsage>simulation.rtl</xilinx:parameterUsage>
            </xilinx:parameterInfo>
          </spirit:vendorExtensions>
        </spirit:parameter>
      </spirit:parameters>
    </spirit:busInterface>
    <spirit:busInterface>
      <spirit:name>aclken</spirit:name>
      <spirit:busType spirit:vendor="xilinx.com" spirit:library="signal" spirit:name="clockenable" spirit:version="1.0"/>
      <spirit:abstractionType spirit:vendor="xilinx.com" spirit:library="signal" spirit:name="clockenable_rtl" spirit:version="1.0"/>
      <spirit:slave/>
      <spirit:portMaps>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>CE</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>aclken</spirit:name>
          </spirit:physicalPort>
        </spirit:portMap>
      </spirit:portMaps>
      <spirit:parameters>
        <spirit:parameter>
          <spirit:name>POLARITY</spirit:name>
          <spirit:value spirit:resolve="generated" spirit:id="BUSIFPARAM_VALUE.ACLKEN.POLARITY">ACTIVE_LOW</spirit:value>
          <spirit:vendorExtensions>
            <xilinx:parameterInfo>
              <xilinx:parameterUsage>none</xilinx:parameterUsage>
            </xilinx:parameterInfo>
          </spirit:vendorExtensions>
        </spirit:parameter>
      </spirit:parameters>
    </spirit:busInterface>
    <spirit:busInterface>
      <spirit:name>S00_SC</spirit:name>
      <spirit:displayName>S00_SC</spirit:displayName>
      <spirit:busType spirit:vendor="xilinx.com" spirit:library="interface" spirit:name="sc" spirit:version="1.0"/>
      <spirit:abstractionType spirit:vendor="xilinx.com" spirit:library="interface" spirit:name="sc_rtl" spirit:version="1.0"/>
      <spirit:slave/>
      <spirit:portMaps>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>SEND</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>s_sc_send</spirit:name>
            <spirit:vector>
              <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="((1 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_MI&apos;))) - 1)">0</spirit:left>
              <spirit:right spirit:format="long" spirit:resolve="dependent" spirit:dependency="(((0 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_MI&apos;))) - 1) + 1)">0</spirit:right>
            </spirit:vector>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>REQ</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>s_sc_req</spirit:name>
            <spirit:vector>
              <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="((1 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_MI&apos;))) - 1)">0</spirit:left>
              <spirit:right spirit:format="long" spirit:resolve="dependent" spirit:dependency="(((0 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_MI&apos;))) - 1) + 1)">0</spirit:right>
            </spirit:vector>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>INFO</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>s_sc_info</spirit:name>
            <spirit:vector>
              <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="(((1 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_MI&apos;))) * 1) - 1)">0</spirit:left>
              <spirit:right spirit:format="long" spirit:resolve="dependent" spirit:dependency="((((0 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_MI&apos;))) * 1) - 1) + 1)">0</spirit:right>
            </spirit:vector>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>PAYLD</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>s_sc_payld</spirit:name>
            <spirit:vector>
              <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="((1 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_PAYLD_WIDTH&apos;))) - 1)">54</spirit:left>
              <spirit:right spirit:format="long" spirit:resolve="dependent" spirit:dependency="(((0 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_PAYLD_WIDTH&apos;))) - 1) + 1)">0</spirit:right>
            </spirit:vector>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>RECV</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>s_sc_recv</spirit:name>
            <spirit:vector>
              <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="((1 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_MI&apos;))) - 1)">0</spirit:left>
              <spirit:right spirit:format="long" spirit:resolve="dependent" spirit:dependency="(((0 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_MI&apos;))) - 1) + 1)">0</spirit:right>
            </spirit:vector>
          </spirit:physicalPort>
        </spirit:portMap>
      </spirit:portMaps>
      <spirit:parameters>
        <spirit:parameter>
          <spirit:name>BRIDGES</spirit:name>
          <spirit:value spirit:id="BUSIFPARAM_VALUE.S00_SC.BRIDGES">M00_SC:M01_SC:M02_SC:M03_SC:M04_SC:M05_SC:M06_SC:M07_SC:M08_SC:M09_SC:M10_SC:M11_SC:M12_SC:M13_SC:M14_SC:M15_SC</spirit:value>
        </spirit:parameter>
      </spirit:parameters>
      <spirit:vendorExtensions>
        <xilinx:busInterfaceInfo>
          <xilinx:enablement>
            <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="BUSIF_ENABLEMENT.S00_SC" xilinx:dependency="(spirit:decode(id(&apos;PARAM_VALUE.NUM_SI&apos;)) > 0)">true</xilinx:isEnabled>
          </xilinx:enablement>
        </xilinx:busInterfaceInfo>
      </spirit:vendorExtensions>
    </spirit:busInterface>
    <spirit:busInterface>
      <spirit:name>M00_SC</spirit:name>
      <spirit:displayName>M00_SC</spirit:displayName>
      <spirit:busType spirit:vendor="xilinx.com" spirit:library="interface" spirit:name="sc" spirit:version="1.0"/>
      <spirit:abstractionType spirit:vendor="xilinx.com" spirit:library="interface" spirit:name="sc_rtl" spirit:version="1.0"/>
      <spirit:master/>
      <spirit:portMaps>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>RECV</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>m_sc_recv</spirit:name>
            <spirit:vector>
              <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="((1 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_SI&apos;))) - 1)">1</spirit:left>
              <spirit:right spirit:format="long" spirit:resolve="dependent" spirit:dependency="(((0 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_SI&apos;))) - 1) + 1)">0</spirit:right>
            </spirit:vector>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>SEND</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>m_sc_send</spirit:name>
            <spirit:vector>
              <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="((1 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_SI&apos;))) - 1)">1</spirit:left>
              <spirit:right spirit:format="long" spirit:resolve="dependent" spirit:dependency="(((0 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_SI&apos;))) - 1) + 1)">0</spirit:right>
            </spirit:vector>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>REQ</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>m_sc_req</spirit:name>
            <spirit:vector>
              <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="((1 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_SI&apos;))) - 1)">1</spirit:left>
              <spirit:right spirit:format="long" spirit:resolve="dependent" spirit:dependency="(((0 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_SI&apos;))) - 1) + 1)">0</spirit:right>
            </spirit:vector>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>INFO</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>m_sc_info</spirit:name>
            <spirit:vector>
              <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="(((1 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_SI&apos;))) * 1) - 1)">1</spirit:left>
              <spirit:right spirit:format="long" spirit:resolve="dependent" spirit:dependency="((((0 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_SI&apos;))) * 1) - 1) + 1)">0</spirit:right>
            </spirit:vector>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>PAYLD</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>m_sc_payld</spirit:name>
            <spirit:vector>
              <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="((1 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_PAYLD_WIDTH&apos;))) - 1)">54</spirit:left>
              <spirit:right spirit:format="long" spirit:resolve="dependent" spirit:dependency="(((0 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_PAYLD_WIDTH&apos;))) - 1) + 1)">0</spirit:right>
            </spirit:vector>
          </spirit:physicalPort>
        </spirit:portMap>
      </spirit:portMaps>
      <spirit:vendorExtensions>
        <xilinx:busInterfaceInfo>
          <xilinx:enablement>
            <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="BUSIF_ENABLEMENT.M00_SC" xilinx:dependency="(spirit:decode(id(&apos;PARAM_VALUE.NUM_MI&apos;)) > 0)">true</xilinx:isEnabled>
          </xilinx:enablement>
        </xilinx:busInterfaceInfo>
      </spirit:vendorExtensions>
    </spirit:busInterface>
    <spirit:busInterface>
      <spirit:name>S01_SC</spirit:name>
      <spirit:displayName>S01_SC</spirit:displayName>
      <spirit:busType spirit:vendor="xilinx.com" spirit:library="interface" spirit:name="sc" spirit:version="1.0"/>
      <spirit:abstractionType spirit:vendor="xilinx.com" spirit:library="interface" spirit:name="sc_rtl" spirit:version="1.0"/>
      <spirit:slave/>
      <spirit:portMaps>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>SEND</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>s_sc_send</spirit:name>
            <spirit:vector>
              <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="((2 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_MI&apos;))) - 1)">1</spirit:left>
              <spirit:right spirit:format="long" spirit:resolve="dependent" spirit:dependency="(((1 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_MI&apos;))) - 1) + 1)">1</spirit:right>
            </spirit:vector>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>REQ</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>s_sc_req</spirit:name>
            <spirit:vector>
              <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="((2 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_MI&apos;))) - 1)">1</spirit:left>
              <spirit:right spirit:format="long" spirit:resolve="dependent" spirit:dependency="(((1 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_MI&apos;))) - 1) + 1)">1</spirit:right>
            </spirit:vector>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>INFO</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>s_sc_info</spirit:name>
            <spirit:vector>
              <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="(((2 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_MI&apos;))) * 1) - 1)">1</spirit:left>
              <spirit:right spirit:format="long" spirit:resolve="dependent" spirit:dependency="((((1 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_MI&apos;))) * 1) - 1) + 1)">1</spirit:right>
            </spirit:vector>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>PAYLD</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>s_sc_payld</spirit:name>
            <spirit:vector>
              <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="((2 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_PAYLD_WIDTH&apos;))) - 1)">109</spirit:left>
              <spirit:right spirit:format="long" spirit:resolve="dependent" spirit:dependency="(((1 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_PAYLD_WIDTH&apos;))) - 1) + 1)">55</spirit:right>
            </spirit:vector>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>RECV</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>s_sc_recv</spirit:name>
            <spirit:vector>
              <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="((2 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_MI&apos;))) - 1)">1</spirit:left>
              <spirit:right spirit:format="long" spirit:resolve="dependent" spirit:dependency="(((1 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_MI&apos;))) - 1) + 1)">1</spirit:right>
            </spirit:vector>
          </spirit:physicalPort>
        </spirit:portMap>
      </spirit:portMaps>
      <spirit:parameters>
        <spirit:parameter>
          <spirit:name>BRIDGES</spirit:name>
          <spirit:value spirit:id="BUSIFPARAM_VALUE.S01_SC.BRIDGES">M00_SC:M01_SC:M02_SC:M03_SC:M04_SC:M05_SC:M06_SC:M07_SC:M08_SC:M09_SC:M10_SC:M11_SC:M12_SC:M13_SC:M14_SC:M15_SC</spirit:value>
        </spirit:parameter>
      </spirit:parameters>
      <spirit:vendorExtensions>
        <xilinx:busInterfaceInfo>
          <xilinx:enablement>
            <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="BUSIF_ENABLEMENT.S01_SC" xilinx:dependency="(spirit:decode(id(&apos;PARAM_VALUE.NUM_SI&apos;)) > 1)">true</xilinx:isEnabled>
          </xilinx:enablement>
        </xilinx:busInterfaceInfo>
      </spirit:vendorExtensions>
    </spirit:busInterface>
    <spirit:busInterface>
      <spirit:name>M01_SC</spirit:name>
      <spirit:displayName>M01_SC</spirit:displayName>
      <spirit:busType spirit:vendor="xilinx.com" spirit:library="interface" spirit:name="sc" spirit:version="1.0"/>
      <spirit:abstractionType spirit:vendor="xilinx.com" spirit:library="interface" spirit:name="sc_rtl" spirit:version="1.0"/>
      <spirit:master/>
      <spirit:portMaps>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>RECV</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>m_sc_recv</spirit:name>
            <spirit:vector>
              <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="((2 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_SI&apos;))) - 1)">3</spirit:left>
              <spirit:right spirit:format="long" spirit:resolve="dependent" spirit:dependency="(((1 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_SI&apos;))) - 1) + 1)">2</spirit:right>
            </spirit:vector>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>SEND</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>m_sc_send</spirit:name>
            <spirit:vector>
              <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="((2 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_SI&apos;))) - 1)">3</spirit:left>
              <spirit:right spirit:format="long" spirit:resolve="dependent" spirit:dependency="(((1 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_SI&apos;))) - 1) + 1)">2</spirit:right>
            </spirit:vector>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>REQ</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>m_sc_req</spirit:name>
            <spirit:vector>
              <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="((2 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_SI&apos;))) - 1)">3</spirit:left>
              <spirit:right spirit:format="long" spirit:resolve="dependent" spirit:dependency="(((1 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_SI&apos;))) - 1) + 1)">2</spirit:right>
            </spirit:vector>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>INFO</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>m_sc_info</spirit:name>
            <spirit:vector>
              <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="(((2 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_SI&apos;))) * 1) - 1)">3</spirit:left>
              <spirit:right spirit:format="long" spirit:resolve="dependent" spirit:dependency="((((1 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_SI&apos;))) * 1) - 1) + 1)">2</spirit:right>
            </spirit:vector>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>PAYLD</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>m_sc_payld</spirit:name>
            <spirit:vector>
              <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="((2 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_PAYLD_WIDTH&apos;))) - 1)">109</spirit:left>
              <spirit:right spirit:format="long" spirit:resolve="dependent" spirit:dependency="(((1 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_PAYLD_WIDTH&apos;))) - 1) + 1)">55</spirit:right>
            </spirit:vector>
          </spirit:physicalPort>
        </spirit:portMap>
      </spirit:portMaps>
      <spirit:vendorExtensions>
        <xilinx:busInterfaceInfo>
          <xilinx:enablement>
            <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="BUSIF_ENABLEMENT.M01_SC" xilinx:dependency="(spirit:decode(id(&apos;PARAM_VALUE.NUM_MI&apos;)) > 1)">false</xilinx:isEnabled>
          </xilinx:enablement>
        </xilinx:busInterfaceInfo>
      </spirit:vendorExtensions>
    </spirit:busInterface>
    <spirit:busInterface>
      <spirit:name>S02_SC</spirit:name>
      <spirit:displayName>S02_SC</spirit:displayName>
      <spirit:busType spirit:vendor="xilinx.com" spirit:library="interface" spirit:name="sc" spirit:version="1.0"/>
      <spirit:abstractionType spirit:vendor="xilinx.com" spirit:library="interface" spirit:name="sc_rtl" spirit:version="1.0"/>
      <spirit:slave/>
      <spirit:portMaps>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>SEND</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>s_sc_send</spirit:name>
            <spirit:vector>
              <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="((3 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_MI&apos;))) - 1)">2</spirit:left>
              <spirit:right spirit:format="long" spirit:resolve="dependent" spirit:dependency="(((2 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_MI&apos;))) - 1) + 1)">2</spirit:right>
            </spirit:vector>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>REQ</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>s_sc_req</spirit:name>
            <spirit:vector>
              <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="((3 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_MI&apos;))) - 1)">2</spirit:left>
              <spirit:right spirit:format="long" spirit:resolve="dependent" spirit:dependency="(((2 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_MI&apos;))) - 1) + 1)">2</spirit:right>
            </spirit:vector>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>INFO</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>s_sc_info</spirit:name>
            <spirit:vector>
              <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="(((3 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_MI&apos;))) * 1) - 1)">2</spirit:left>
              <spirit:right spirit:format="long" spirit:resolve="dependent" spirit:dependency="((((2 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_MI&apos;))) * 1) - 1) + 1)">2</spirit:right>
            </spirit:vector>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>PAYLD</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>s_sc_payld</spirit:name>
            <spirit:vector>
              <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="((3 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_PAYLD_WIDTH&apos;))) - 1)">164</spirit:left>
              <spirit:right spirit:format="long" spirit:resolve="dependent" spirit:dependency="(((2 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_PAYLD_WIDTH&apos;))) - 1) + 1)">110</spirit:right>
            </spirit:vector>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>RECV</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>s_sc_recv</spirit:name>
            <spirit:vector>
              <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="((3 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_MI&apos;))) - 1)">2</spirit:left>
              <spirit:right spirit:format="long" spirit:resolve="dependent" spirit:dependency="(((2 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_MI&apos;))) - 1) + 1)">2</spirit:right>
            </spirit:vector>
          </spirit:physicalPort>
        </spirit:portMap>
      </spirit:portMaps>
      <spirit:parameters>
        <spirit:parameter>
          <spirit:name>BRIDGES</spirit:name>
          <spirit:value spirit:id="BUSIFPARAM_VALUE.S02_SC.BRIDGES">M00_SC:M01_SC:M02_SC:M03_SC:M04_SC:M05_SC:M06_SC:M07_SC:M08_SC:M09_SC:M10_SC:M11_SC:M12_SC:M13_SC:M14_SC:M15_SC</spirit:value>
        </spirit:parameter>
      </spirit:parameters>
      <spirit:vendorExtensions>
        <xilinx:busInterfaceInfo>
          <xilinx:enablement>
            <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="BUSIF_ENABLEMENT.S02_SC" xilinx:dependency="(spirit:decode(id(&apos;PARAM_VALUE.NUM_SI&apos;)) > 2)">false</xilinx:isEnabled>
          </xilinx:enablement>
        </xilinx:busInterfaceInfo>
      </spirit:vendorExtensions>
    </spirit:busInterface>
    <spirit:busInterface>
      <spirit:name>M02_SC</spirit:name>
      <spirit:displayName>M02_SC</spirit:displayName>
      <spirit:busType spirit:vendor="xilinx.com" spirit:library="interface" spirit:name="sc" spirit:version="1.0"/>
      <spirit:abstractionType spirit:vendor="xilinx.com" spirit:library="interface" spirit:name="sc_rtl" spirit:version="1.0"/>
      <spirit:master/>
      <spirit:portMaps>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>RECV</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>m_sc_recv</spirit:name>
            <spirit:vector>
              <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="((3 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_SI&apos;))) - 1)">5</spirit:left>
              <spirit:right spirit:format="long" spirit:resolve="dependent" spirit:dependency="(((2 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_SI&apos;))) - 1) + 1)">4</spirit:right>
            </spirit:vector>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>SEND</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>m_sc_send</spirit:name>
            <spirit:vector>
              <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="((3 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_SI&apos;))) - 1)">5</spirit:left>
              <spirit:right spirit:format="long" spirit:resolve="dependent" spirit:dependency="(((2 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_SI&apos;))) - 1) + 1)">4</spirit:right>
            </spirit:vector>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>REQ</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>m_sc_req</spirit:name>
            <spirit:vector>
              <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="((3 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_SI&apos;))) - 1)">5</spirit:left>
              <spirit:right spirit:format="long" spirit:resolve="dependent" spirit:dependency="(((2 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_SI&apos;))) - 1) + 1)">4</spirit:right>
            </spirit:vector>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>INFO</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>m_sc_info</spirit:name>
            <spirit:vector>
              <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="(((3 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_SI&apos;))) * 1) - 1)">5</spirit:left>
              <spirit:right spirit:format="long" spirit:resolve="dependent" spirit:dependency="((((2 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_SI&apos;))) * 1) - 1) + 1)">4</spirit:right>
            </spirit:vector>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>PAYLD</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>m_sc_payld</spirit:name>
            <spirit:vector>
              <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="((3 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_PAYLD_WIDTH&apos;))) - 1)">164</spirit:left>
              <spirit:right spirit:format="long" spirit:resolve="dependent" spirit:dependency="(((2 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_PAYLD_WIDTH&apos;))) - 1) + 1)">110</spirit:right>
            </spirit:vector>
          </spirit:physicalPort>
        </spirit:portMap>
      </spirit:portMaps>
      <spirit:vendorExtensions>
        <xilinx:busInterfaceInfo>
          <xilinx:enablement>
            <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="BUSIF_ENABLEMENT.M02_SC" xilinx:dependency="(spirit:decode(id(&apos;PARAM_VALUE.NUM_MI&apos;)) > 2)">false</xilinx:isEnabled>
          </xilinx:enablement>
        </xilinx:busInterfaceInfo>
      </spirit:vendorExtensions>
    </spirit:busInterface>
    <spirit:busInterface>
      <spirit:name>S03_SC</spirit:name>
      <spirit:displayName>S03_SC</spirit:displayName>
      <spirit:busType spirit:vendor="xilinx.com" spirit:library="interface" spirit:name="sc" spirit:version="1.0"/>
      <spirit:abstractionType spirit:vendor="xilinx.com" spirit:library="interface" spirit:name="sc_rtl" spirit:version="1.0"/>
      <spirit:slave/>
      <spirit:portMaps>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>SEND</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>s_sc_send</spirit:name>
            <spirit:vector>
              <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="((4 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_MI&apos;))) - 1)">3</spirit:left>
              <spirit:right spirit:format="long" spirit:resolve="dependent" spirit:dependency="(((3 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_MI&apos;))) - 1) + 1)">3</spirit:right>
            </spirit:vector>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>REQ</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>s_sc_req</spirit:name>
            <spirit:vector>
              <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="((4 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_MI&apos;))) - 1)">3</spirit:left>
              <spirit:right spirit:format="long" spirit:resolve="dependent" spirit:dependency="(((3 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_MI&apos;))) - 1) + 1)">3</spirit:right>
            </spirit:vector>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>INFO</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>s_sc_info</spirit:name>
            <spirit:vector>
              <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="(((4 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_MI&apos;))) * 1) - 1)">3</spirit:left>
              <spirit:right spirit:format="long" spirit:resolve="dependent" spirit:dependency="((((3 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_MI&apos;))) * 1) - 1) + 1)">3</spirit:right>
            </spirit:vector>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>PAYLD</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>s_sc_payld</spirit:name>
            <spirit:vector>
              <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="((4 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_PAYLD_WIDTH&apos;))) - 1)">219</spirit:left>
              <spirit:right spirit:format="long" spirit:resolve="dependent" spirit:dependency="(((3 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_PAYLD_WIDTH&apos;))) - 1) + 1)">165</spirit:right>
            </spirit:vector>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>RECV</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>s_sc_recv</spirit:name>
            <spirit:vector>
              <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="((4 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_MI&apos;))) - 1)">3</spirit:left>
              <spirit:right spirit:format="long" spirit:resolve="dependent" spirit:dependency="(((3 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_MI&apos;))) - 1) + 1)">3</spirit:right>
            </spirit:vector>
          </spirit:physicalPort>
        </spirit:portMap>
      </spirit:portMaps>
      <spirit:parameters>
        <spirit:parameter>
          <spirit:name>BRIDGES</spirit:name>
          <spirit:value spirit:id="BUSIFPARAM_VALUE.S03_SC.BRIDGES">M00_SC:M01_SC:M02_SC:M03_SC:M04_SC:M05_SC:M06_SC:M07_SC:M08_SC:M09_SC:M10_SC:M11_SC:M12_SC:M13_SC:M14_SC:M15_SC</spirit:value>
        </spirit:parameter>
      </spirit:parameters>
      <spirit:vendorExtensions>
        <xilinx:busInterfaceInfo>
          <xilinx:enablement>
            <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="BUSIF_ENABLEMENT.S03_SC" xilinx:dependency="(spirit:decode(id(&apos;PARAM_VALUE.NUM_SI&apos;)) > 3)">false</xilinx:isEnabled>
          </xilinx:enablement>
        </xilinx:busInterfaceInfo>
      </spirit:vendorExtensions>
    </spirit:busInterface>
    <spirit:busInterface>
      <spirit:name>M03_SC</spirit:name>
      <spirit:displayName>M03_SC</spirit:displayName>
      <spirit:busType spirit:vendor="xilinx.com" spirit:library="interface" spirit:name="sc" spirit:version="1.0"/>
      <spirit:abstractionType spirit:vendor="xilinx.com" spirit:library="interface" spirit:name="sc_rtl" spirit:version="1.0"/>
      <spirit:master/>
      <spirit:portMaps>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>RECV</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>m_sc_recv</spirit:name>
            <spirit:vector>
              <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="((4 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_SI&apos;))) - 1)">7</spirit:left>
              <spirit:right spirit:format="long" spirit:resolve="dependent" spirit:dependency="(((3 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_SI&apos;))) - 1) + 1)">6</spirit:right>
            </spirit:vector>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>SEND</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>m_sc_send</spirit:name>
            <spirit:vector>
              <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="((4 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_SI&apos;))) - 1)">7</spirit:left>
              <spirit:right spirit:format="long" spirit:resolve="dependent" spirit:dependency="(((3 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_SI&apos;))) - 1) + 1)">6</spirit:right>
            </spirit:vector>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>REQ</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>m_sc_req</spirit:name>
            <spirit:vector>
              <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="((4 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_SI&apos;))) - 1)">7</spirit:left>
              <spirit:right spirit:format="long" spirit:resolve="dependent" spirit:dependency="(((3 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_SI&apos;))) - 1) + 1)">6</spirit:right>
            </spirit:vector>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>INFO</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>m_sc_info</spirit:name>
            <spirit:vector>
              <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="(((4 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_SI&apos;))) * 1) - 1)">7</spirit:left>
              <spirit:right spirit:format="long" spirit:resolve="dependent" spirit:dependency="((((3 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_SI&apos;))) * 1) - 1) + 1)">6</spirit:right>
            </spirit:vector>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>PAYLD</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>m_sc_payld</spirit:name>
            <spirit:vector>
              <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="((4 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_PAYLD_WIDTH&apos;))) - 1)">219</spirit:left>
              <spirit:right spirit:format="long" spirit:resolve="dependent" spirit:dependency="(((3 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_PAYLD_WIDTH&apos;))) - 1) + 1)">165</spirit:right>
            </spirit:vector>
          </spirit:physicalPort>
        </spirit:portMap>
      </spirit:portMaps>
      <spirit:vendorExtensions>
        <xilinx:busInterfaceInfo>
          <xilinx:enablement>
            <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="BUSIF_ENABLEMENT.M03_SC" xilinx:dependency="(spirit:decode(id(&apos;PARAM_VALUE.NUM_MI&apos;)) > 3)">false</xilinx:isEnabled>
          </xilinx:enablement>
        </xilinx:busInterfaceInfo>
      </spirit:vendorExtensions>
    </spirit:busInterface>
    <spirit:busInterface>
      <spirit:name>S04_SC</spirit:name>
      <spirit:displayName>S04_SC</spirit:displayName>
      <spirit:busType spirit:vendor="xilinx.com" spirit:library="interface" spirit:name="sc" spirit:version="1.0"/>
      <spirit:abstractionType spirit:vendor="xilinx.com" spirit:library="interface" spirit:name="sc_rtl" spirit:version="1.0"/>
      <spirit:slave/>
      <spirit:portMaps>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>SEND</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>s_sc_send</spirit:name>
            <spirit:vector>
              <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="((5 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_MI&apos;))) - 1)">4</spirit:left>
              <spirit:right spirit:format="long" spirit:resolve="dependent" spirit:dependency="(((4 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_MI&apos;))) - 1) + 1)">4</spirit:right>
            </spirit:vector>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>REQ</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>s_sc_req</spirit:name>
            <spirit:vector>
              <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="((5 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_MI&apos;))) - 1)">4</spirit:left>
              <spirit:right spirit:format="long" spirit:resolve="dependent" spirit:dependency="(((4 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_MI&apos;))) - 1) + 1)">4</spirit:right>
            </spirit:vector>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>INFO</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>s_sc_info</spirit:name>
            <spirit:vector>
              <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="(((5 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_MI&apos;))) * 1) - 1)">4</spirit:left>
              <spirit:right spirit:format="long" spirit:resolve="dependent" spirit:dependency="((((4 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_MI&apos;))) * 1) - 1) + 1)">4</spirit:right>
            </spirit:vector>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>PAYLD</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>s_sc_payld</spirit:name>
            <spirit:vector>
              <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="((5 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_PAYLD_WIDTH&apos;))) - 1)">274</spirit:left>
              <spirit:right spirit:format="long" spirit:resolve="dependent" spirit:dependency="(((4 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_PAYLD_WIDTH&apos;))) - 1) + 1)">220</spirit:right>
            </spirit:vector>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>RECV</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>s_sc_recv</spirit:name>
            <spirit:vector>
              <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="((5 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_MI&apos;))) - 1)">4</spirit:left>
              <spirit:right spirit:format="long" spirit:resolve="dependent" spirit:dependency="(((4 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_MI&apos;))) - 1) + 1)">4</spirit:right>
            </spirit:vector>
          </spirit:physicalPort>
        </spirit:portMap>
      </spirit:portMaps>
      <spirit:parameters>
        <spirit:parameter>
          <spirit:name>BRIDGES</spirit:name>
          <spirit:value spirit:id="BUSIFPARAM_VALUE.S04_SC.BRIDGES">M00_SC:M01_SC:M02_SC:M03_SC:M04_SC:M05_SC:M06_SC:M07_SC:M08_SC:M09_SC:M10_SC:M11_SC:M12_SC:M13_SC:M14_SC:M15_SC</spirit:value>
        </spirit:parameter>
      </spirit:parameters>
      <spirit:vendorExtensions>
        <xilinx:busInterfaceInfo>
          <xilinx:enablement>
            <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="BUSIF_ENABLEMENT.S04_SC" xilinx:dependency="(spirit:decode(id(&apos;PARAM_VALUE.NUM_SI&apos;)) > 4)">false</xilinx:isEnabled>
          </xilinx:enablement>
        </xilinx:busInterfaceInfo>
      </spirit:vendorExtensions>
    </spirit:busInterface>
    <spirit:busInterface>
      <spirit:name>M04_SC</spirit:name>
      <spirit:displayName>M04_SC</spirit:displayName>
      <spirit:busType spirit:vendor="xilinx.com" spirit:library="interface" spirit:name="sc" spirit:version="1.0"/>
      <spirit:abstractionType spirit:vendor="xilinx.com" spirit:library="interface" spirit:name="sc_rtl" spirit:version="1.0"/>
      <spirit:master/>
      <spirit:portMaps>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>RECV</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>m_sc_recv</spirit:name>
            <spirit:vector>
              <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="((5 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_SI&apos;))) - 1)">9</spirit:left>
              <spirit:right spirit:format="long" spirit:resolve="dependent" spirit:dependency="(((4 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_SI&apos;))) - 1) + 1)">8</spirit:right>
            </spirit:vector>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>SEND</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>m_sc_send</spirit:name>
            <spirit:vector>
              <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="((5 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_SI&apos;))) - 1)">9</spirit:left>
              <spirit:right spirit:format="long" spirit:resolve="dependent" spirit:dependency="(((4 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_SI&apos;))) - 1) + 1)">8</spirit:right>
            </spirit:vector>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>REQ</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>m_sc_req</spirit:name>
            <spirit:vector>
              <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="((5 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_SI&apos;))) - 1)">9</spirit:left>
              <spirit:right spirit:format="long" spirit:resolve="dependent" spirit:dependency="(((4 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_SI&apos;))) - 1) + 1)">8</spirit:right>
            </spirit:vector>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>INFO</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>m_sc_info</spirit:name>
            <spirit:vector>
              <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="(((5 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_SI&apos;))) * 1) - 1)">9</spirit:left>
              <spirit:right spirit:format="long" spirit:resolve="dependent" spirit:dependency="((((4 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_SI&apos;))) * 1) - 1) + 1)">8</spirit:right>
            </spirit:vector>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>PAYLD</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>m_sc_payld</spirit:name>
            <spirit:vector>
              <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="((5 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_PAYLD_WIDTH&apos;))) - 1)">274</spirit:left>
              <spirit:right spirit:format="long" spirit:resolve="dependent" spirit:dependency="(((4 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_PAYLD_WIDTH&apos;))) - 1) + 1)">220</spirit:right>
            </spirit:vector>
          </spirit:physicalPort>
        </spirit:portMap>
      </spirit:portMaps>
      <spirit:vendorExtensions>
        <xilinx:busInterfaceInfo>
          <xilinx:enablement>
            <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="BUSIF_ENABLEMENT.M04_SC" xilinx:dependency="(spirit:decode(id(&apos;PARAM_VALUE.NUM_MI&apos;)) > 4)">false</xilinx:isEnabled>
          </xilinx:enablement>
        </xilinx:busInterfaceInfo>
      </spirit:vendorExtensions>
    </spirit:busInterface>
    <spirit:busInterface>
      <spirit:name>S05_SC</spirit:name>
      <spirit:displayName>S05_SC</spirit:displayName>
      <spirit:busType spirit:vendor="xilinx.com" spirit:library="interface" spirit:name="sc" spirit:version="1.0"/>
      <spirit:abstractionType spirit:vendor="xilinx.com" spirit:library="interface" spirit:name="sc_rtl" spirit:version="1.0"/>
      <spirit:slave/>
      <spirit:portMaps>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>SEND</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>s_sc_send</spirit:name>
            <spirit:vector>
              <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="((6 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_MI&apos;))) - 1)">5</spirit:left>
              <spirit:right spirit:format="long" spirit:resolve="dependent" spirit:dependency="(((5 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_MI&apos;))) - 1) + 1)">5</spirit:right>
            </spirit:vector>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>REQ</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>s_sc_req</spirit:name>
            <spirit:vector>
              <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="((6 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_MI&apos;))) - 1)">5</spirit:left>
              <spirit:right spirit:format="long" spirit:resolve="dependent" spirit:dependency="(((5 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_MI&apos;))) - 1) + 1)">5</spirit:right>
            </spirit:vector>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>INFO</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>s_sc_info</spirit:name>
            <spirit:vector>
              <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="(((6 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_MI&apos;))) * 1) - 1)">5</spirit:left>
              <spirit:right spirit:format="long" spirit:resolve="dependent" spirit:dependency="((((5 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_MI&apos;))) * 1) - 1) + 1)">5</spirit:right>
            </spirit:vector>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>PAYLD</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>s_sc_payld</spirit:name>
            <spirit:vector>
              <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="((6 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_PAYLD_WIDTH&apos;))) - 1)">329</spirit:left>
              <spirit:right spirit:format="long" spirit:resolve="dependent" spirit:dependency="(((5 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_PAYLD_WIDTH&apos;))) - 1) + 1)">275</spirit:right>
            </spirit:vector>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>RECV</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>s_sc_recv</spirit:name>
            <spirit:vector>
              <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="((6 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_MI&apos;))) - 1)">5</spirit:left>
              <spirit:right spirit:format="long" spirit:resolve="dependent" spirit:dependency="(((5 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_MI&apos;))) - 1) + 1)">5</spirit:right>
            </spirit:vector>
          </spirit:physicalPort>
        </spirit:portMap>
      </spirit:portMaps>
      <spirit:parameters>
        <spirit:parameter>
          <spirit:name>BRIDGES</spirit:name>
          <spirit:value spirit:id="BUSIFPARAM_VALUE.S05_SC.BRIDGES">M00_SC:M01_SC:M02_SC:M03_SC:M04_SC:M05_SC:M06_SC:M07_SC:M08_SC:M09_SC:M10_SC:M11_SC:M12_SC:M13_SC:M14_SC:M15_SC</spirit:value>
        </spirit:parameter>
      </spirit:parameters>
      <spirit:vendorExtensions>
        <xilinx:busInterfaceInfo>
          <xilinx:enablement>
            <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="BUSIF_ENABLEMENT.S05_SC" xilinx:dependency="(spirit:decode(id(&apos;PARAM_VALUE.NUM_SI&apos;)) > 5)">false</xilinx:isEnabled>
          </xilinx:enablement>
        </xilinx:busInterfaceInfo>
      </spirit:vendorExtensions>
    </spirit:busInterface>
    <spirit:busInterface>
      <spirit:name>M05_SC</spirit:name>
      <spirit:displayName>M05_SC</spirit:displayName>
      <spirit:busType spirit:vendor="xilinx.com" spirit:library="interface" spirit:name="sc" spirit:version="1.0"/>
      <spirit:abstractionType spirit:vendor="xilinx.com" spirit:library="interface" spirit:name="sc_rtl" spirit:version="1.0"/>
      <spirit:master/>
      <spirit:portMaps>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>RECV</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>m_sc_recv</spirit:name>
            <spirit:vector>
              <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="((6 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_SI&apos;))) - 1)">11</spirit:left>
              <spirit:right spirit:format="long" spirit:resolve="dependent" spirit:dependency="(((5 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_SI&apos;))) - 1) + 1)">10</spirit:right>
            </spirit:vector>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>SEND</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>m_sc_send</spirit:name>
            <spirit:vector>
              <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="((6 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_SI&apos;))) - 1)">11</spirit:left>
              <spirit:right spirit:format="long" spirit:resolve="dependent" spirit:dependency="(((5 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_SI&apos;))) - 1) + 1)">10</spirit:right>
            </spirit:vector>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>REQ</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>m_sc_req</spirit:name>
            <spirit:vector>
              <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="((6 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_SI&apos;))) - 1)">11</spirit:left>
              <spirit:right spirit:format="long" spirit:resolve="dependent" spirit:dependency="(((5 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_SI&apos;))) - 1) + 1)">10</spirit:right>
            </spirit:vector>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>INFO</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>m_sc_info</spirit:name>
            <spirit:vector>
              <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="(((6 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_SI&apos;))) * 1) - 1)">11</spirit:left>
              <spirit:right spirit:format="long" spirit:resolve="dependent" spirit:dependency="((((5 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_SI&apos;))) * 1) - 1) + 1)">10</spirit:right>
            </spirit:vector>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>PAYLD</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>m_sc_payld</spirit:name>
            <spirit:vector>
              <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="((6 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_PAYLD_WIDTH&apos;))) - 1)">329</spirit:left>
              <spirit:right spirit:format="long" spirit:resolve="dependent" spirit:dependency="(((5 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_PAYLD_WIDTH&apos;))) - 1) + 1)">275</spirit:right>
            </spirit:vector>
          </spirit:physicalPort>
        </spirit:portMap>
      </spirit:portMaps>
      <spirit:vendorExtensions>
        <xilinx:busInterfaceInfo>
          <xilinx:enablement>
            <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="BUSIF_ENABLEMENT.M05_SC" xilinx:dependency="(spirit:decode(id(&apos;PARAM_VALUE.NUM_MI&apos;)) > 5)">false</xilinx:isEnabled>
          </xilinx:enablement>
        </xilinx:busInterfaceInfo>
      </spirit:vendorExtensions>
    </spirit:busInterface>
    <spirit:busInterface>
      <spirit:name>S06_SC</spirit:name>
      <spirit:displayName>S06_SC</spirit:displayName>
      <spirit:busType spirit:vendor="xilinx.com" spirit:library="interface" spirit:name="sc" spirit:version="1.0"/>
      <spirit:abstractionType spirit:vendor="xilinx.com" spirit:library="interface" spirit:name="sc_rtl" spirit:version="1.0"/>
      <spirit:slave/>
      <spirit:portMaps>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>SEND</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>s_sc_send</spirit:name>
            <spirit:vector>
              <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="((7 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_MI&apos;))) - 1)">6</spirit:left>
              <spirit:right spirit:format="long" spirit:resolve="dependent" spirit:dependency="(((6 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_MI&apos;))) - 1) + 1)">6</spirit:right>
            </spirit:vector>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>REQ</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>s_sc_req</spirit:name>
            <spirit:vector>
              <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="((7 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_MI&apos;))) - 1)">6</spirit:left>
              <spirit:right spirit:format="long" spirit:resolve="dependent" spirit:dependency="(((6 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_MI&apos;))) - 1) + 1)">6</spirit:right>
            </spirit:vector>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>INFO</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>s_sc_info</spirit:name>
            <spirit:vector>
              <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="(((7 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_MI&apos;))) * 1) - 1)">6</spirit:left>
              <spirit:right spirit:format="long" spirit:resolve="dependent" spirit:dependency="((((6 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_MI&apos;))) * 1) - 1) + 1)">6</spirit:right>
            </spirit:vector>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>PAYLD</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>s_sc_payld</spirit:name>
            <spirit:vector>
              <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="((7 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_PAYLD_WIDTH&apos;))) - 1)">384</spirit:left>
              <spirit:right spirit:format="long" spirit:resolve="dependent" spirit:dependency="(((6 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_PAYLD_WIDTH&apos;))) - 1) + 1)">330</spirit:right>
            </spirit:vector>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>RECV</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>s_sc_recv</spirit:name>
            <spirit:vector>
              <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="((7 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_MI&apos;))) - 1)">6</spirit:left>
              <spirit:right spirit:format="long" spirit:resolve="dependent" spirit:dependency="(((6 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_MI&apos;))) - 1) + 1)">6</spirit:right>
            </spirit:vector>
          </spirit:physicalPort>
        </spirit:portMap>
      </spirit:portMaps>
      <spirit:parameters>
        <spirit:parameter>
          <spirit:name>BRIDGES</spirit:name>
          <spirit:value spirit:id="BUSIFPARAM_VALUE.S06_SC.BRIDGES">M00_SC:M01_SC:M02_SC:M03_SC:M04_SC:M05_SC:M06_SC:M07_SC:M08_SC:M09_SC:M10_SC:M11_SC:M12_SC:M13_SC:M14_SC:M15_SC</spirit:value>
        </spirit:parameter>
      </spirit:parameters>
      <spirit:vendorExtensions>
        <xilinx:busInterfaceInfo>
          <xilinx:enablement>
            <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="BUSIF_ENABLEMENT.S06_SC" xilinx:dependency="(spirit:decode(id(&apos;PARAM_VALUE.NUM_SI&apos;)) > 6)">false</xilinx:isEnabled>
          </xilinx:enablement>
        </xilinx:busInterfaceInfo>
      </spirit:vendorExtensions>
    </spirit:busInterface>
    <spirit:busInterface>
      <spirit:name>M06_SC</spirit:name>
      <spirit:displayName>M06_SC</spirit:displayName>
      <spirit:busType spirit:vendor="xilinx.com" spirit:library="interface" spirit:name="sc" spirit:version="1.0"/>
      <spirit:abstractionType spirit:vendor="xilinx.com" spirit:library="interface" spirit:name="sc_rtl" spirit:version="1.0"/>
      <spirit:master/>
      <spirit:portMaps>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>RECV</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>m_sc_recv</spirit:name>
            <spirit:vector>
              <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="((7 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_SI&apos;))) - 1)">13</spirit:left>
              <spirit:right spirit:format="long" spirit:resolve="dependent" spirit:dependency="(((6 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_SI&apos;))) - 1) + 1)">12</spirit:right>
            </spirit:vector>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>SEND</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>m_sc_send</spirit:name>
            <spirit:vector>
              <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="((7 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_SI&apos;))) - 1)">13</spirit:left>
              <spirit:right spirit:format="long" spirit:resolve="dependent" spirit:dependency="(((6 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_SI&apos;))) - 1) + 1)">12</spirit:right>
            </spirit:vector>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>REQ</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>m_sc_req</spirit:name>
            <spirit:vector>
              <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="((7 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_SI&apos;))) - 1)">13</spirit:left>
              <spirit:right spirit:format="long" spirit:resolve="dependent" spirit:dependency="(((6 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_SI&apos;))) - 1) + 1)">12</spirit:right>
            </spirit:vector>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>INFO</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>m_sc_info</spirit:name>
            <spirit:vector>
              <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="(((7 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_SI&apos;))) * 1) - 1)">13</spirit:left>
              <spirit:right spirit:format="long" spirit:resolve="dependent" spirit:dependency="((((6 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_SI&apos;))) * 1) - 1) + 1)">12</spirit:right>
            </spirit:vector>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>PAYLD</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>m_sc_payld</spirit:name>
            <spirit:vector>
              <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="((7 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_PAYLD_WIDTH&apos;))) - 1)">384</spirit:left>
              <spirit:right spirit:format="long" spirit:resolve="dependent" spirit:dependency="(((6 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_PAYLD_WIDTH&apos;))) - 1) + 1)">330</spirit:right>
            </spirit:vector>
          </spirit:physicalPort>
        </spirit:portMap>
      </spirit:portMaps>
      <spirit:vendorExtensions>
        <xilinx:busInterfaceInfo>
          <xilinx:enablement>
            <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="BUSIF_ENABLEMENT.M06_SC" xilinx:dependency="(spirit:decode(id(&apos;PARAM_VALUE.NUM_MI&apos;)) > 6)">false</xilinx:isEnabled>
          </xilinx:enablement>
        </xilinx:busInterfaceInfo>
      </spirit:vendorExtensions>
    </spirit:busInterface>
    <spirit:busInterface>
      <spirit:name>S07_SC</spirit:name>
      <spirit:displayName>S07_SC</spirit:displayName>
      <spirit:busType spirit:vendor="xilinx.com" spirit:library="interface" spirit:name="sc" spirit:version="1.0"/>
      <spirit:abstractionType spirit:vendor="xilinx.com" spirit:library="interface" spirit:name="sc_rtl" spirit:version="1.0"/>
      <spirit:slave/>
      <spirit:portMaps>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>SEND</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>s_sc_send</spirit:name>
            <spirit:vector>
              <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="((8 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_MI&apos;))) - 1)">7</spirit:left>
              <spirit:right spirit:format="long" spirit:resolve="dependent" spirit:dependency="(((7 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_MI&apos;))) - 1) + 1)">7</spirit:right>
            </spirit:vector>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>REQ</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>s_sc_req</spirit:name>
            <spirit:vector>
              <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="((8 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_MI&apos;))) - 1)">7</spirit:left>
              <spirit:right spirit:format="long" spirit:resolve="dependent" spirit:dependency="(((7 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_MI&apos;))) - 1) + 1)">7</spirit:right>
            </spirit:vector>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>INFO</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>s_sc_info</spirit:name>
            <spirit:vector>
              <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="(((8 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_MI&apos;))) * 1) - 1)">7</spirit:left>
              <spirit:right spirit:format="long" spirit:resolve="dependent" spirit:dependency="((((7 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_MI&apos;))) * 1) - 1) + 1)">7</spirit:right>
            </spirit:vector>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>PAYLD</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>s_sc_payld</spirit:name>
            <spirit:vector>
              <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="((8 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_PAYLD_WIDTH&apos;))) - 1)">439</spirit:left>
              <spirit:right spirit:format="long" spirit:resolve="dependent" spirit:dependency="(((7 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_PAYLD_WIDTH&apos;))) - 1) + 1)">385</spirit:right>
            </spirit:vector>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>RECV</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>s_sc_recv</spirit:name>
            <spirit:vector>
              <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="((8 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_MI&apos;))) - 1)">7</spirit:left>
              <spirit:right spirit:format="long" spirit:resolve="dependent" spirit:dependency="(((7 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_MI&apos;))) - 1) + 1)">7</spirit:right>
            </spirit:vector>
          </spirit:physicalPort>
        </spirit:portMap>
      </spirit:portMaps>
      <spirit:parameters>
        <spirit:parameter>
          <spirit:name>BRIDGES</spirit:name>
          <spirit:value spirit:id="BUSIFPARAM_VALUE.S07_SC.BRIDGES">M00_SC:M01_SC:M02_SC:M03_SC:M04_SC:M05_SC:M06_SC:M07_SC:M08_SC:M09_SC:M10_SC:M11_SC:M12_SC:M13_SC:M14_SC:M15_SC</spirit:value>
        </spirit:parameter>
      </spirit:parameters>
      <spirit:vendorExtensions>
        <xilinx:busInterfaceInfo>
          <xilinx:enablement>
            <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="BUSIF_ENABLEMENT.S07_SC" xilinx:dependency="(spirit:decode(id(&apos;PARAM_VALUE.NUM_SI&apos;)) > 7)">false</xilinx:isEnabled>
          </xilinx:enablement>
        </xilinx:busInterfaceInfo>
      </spirit:vendorExtensions>
    </spirit:busInterface>
    <spirit:busInterface>
      <spirit:name>M07_SC</spirit:name>
      <spirit:displayName>M07_SC</spirit:displayName>
      <spirit:busType spirit:vendor="xilinx.com" spirit:library="interface" spirit:name="sc" spirit:version="1.0"/>
      <spirit:abstractionType spirit:vendor="xilinx.com" spirit:library="interface" spirit:name="sc_rtl" spirit:version="1.0"/>
      <spirit:master/>
      <spirit:portMaps>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>RECV</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>m_sc_recv</spirit:name>
            <spirit:vector>
              <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="((8 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_SI&apos;))) - 1)">15</spirit:left>
              <spirit:right spirit:format="long" spirit:resolve="dependent" spirit:dependency="(((7 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_SI&apos;))) - 1) + 1)">14</spirit:right>
            </spirit:vector>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>SEND</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>m_sc_send</spirit:name>
            <spirit:vector>
              <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="((8 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_SI&apos;))) - 1)">15</spirit:left>
              <spirit:right spirit:format="long" spirit:resolve="dependent" spirit:dependency="(((7 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_SI&apos;))) - 1) + 1)">14</spirit:right>
            </spirit:vector>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>REQ</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>m_sc_req</spirit:name>
            <spirit:vector>
              <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="((8 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_SI&apos;))) - 1)">15</spirit:left>
              <spirit:right spirit:format="long" spirit:resolve="dependent" spirit:dependency="(((7 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_SI&apos;))) - 1) + 1)">14</spirit:right>
            </spirit:vector>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>INFO</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>m_sc_info</spirit:name>
            <spirit:vector>
              <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="(((8 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_SI&apos;))) * 1) - 1)">15</spirit:left>
              <spirit:right spirit:format="long" spirit:resolve="dependent" spirit:dependency="((((7 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_SI&apos;))) * 1) - 1) + 1)">14</spirit:right>
            </spirit:vector>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>PAYLD</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>m_sc_payld</spirit:name>
            <spirit:vector>
              <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="((8 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_PAYLD_WIDTH&apos;))) - 1)">439</spirit:left>
              <spirit:right spirit:format="long" spirit:resolve="dependent" spirit:dependency="(((7 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_PAYLD_WIDTH&apos;))) - 1) + 1)">385</spirit:right>
            </spirit:vector>
          </spirit:physicalPort>
        </spirit:portMap>
      </spirit:portMaps>
      <spirit:vendorExtensions>
        <xilinx:busInterfaceInfo>
          <xilinx:enablement>
            <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="BUSIF_ENABLEMENT.M07_SC" xilinx:dependency="(spirit:decode(id(&apos;PARAM_VALUE.NUM_MI&apos;)) > 7)">false</xilinx:isEnabled>
          </xilinx:enablement>
        </xilinx:busInterfaceInfo>
      </spirit:vendorExtensions>
    </spirit:busInterface>
    <spirit:busInterface>
      <spirit:name>S08_SC</spirit:name>
      <spirit:displayName>S08_SC</spirit:displayName>
      <spirit:busType spirit:vendor="xilinx.com" spirit:library="interface" spirit:name="sc" spirit:version="1.0"/>
      <spirit:abstractionType spirit:vendor="xilinx.com" spirit:library="interface" spirit:name="sc_rtl" spirit:version="1.0"/>
      <spirit:slave/>
      <spirit:portMaps>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>SEND</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>s_sc_send</spirit:name>
            <spirit:vector>
              <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="((9 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_MI&apos;))) - 1)">8</spirit:left>
              <spirit:right spirit:format="long" spirit:resolve="dependent" spirit:dependency="(((8 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_MI&apos;))) - 1) + 1)">8</spirit:right>
            </spirit:vector>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>REQ</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>s_sc_req</spirit:name>
            <spirit:vector>
              <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="((9 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_MI&apos;))) - 1)">8</spirit:left>
              <spirit:right spirit:format="long" spirit:resolve="dependent" spirit:dependency="(((8 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_MI&apos;))) - 1) + 1)">8</spirit:right>
            </spirit:vector>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>INFO</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>s_sc_info</spirit:name>
            <spirit:vector>
              <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="(((9 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_MI&apos;))) * 1) - 1)">8</spirit:left>
              <spirit:right spirit:format="long" spirit:resolve="dependent" spirit:dependency="((((8 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_MI&apos;))) * 1) - 1) + 1)">8</spirit:right>
            </spirit:vector>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>PAYLD</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>s_sc_payld</spirit:name>
            <spirit:vector>
              <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="((9 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_PAYLD_WIDTH&apos;))) - 1)">494</spirit:left>
              <spirit:right spirit:format="long" spirit:resolve="dependent" spirit:dependency="(((8 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_PAYLD_WIDTH&apos;))) - 1) + 1)">440</spirit:right>
            </spirit:vector>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>RECV</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>s_sc_recv</spirit:name>
            <spirit:vector>
              <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="((9 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_MI&apos;))) - 1)">8</spirit:left>
              <spirit:right spirit:format="long" spirit:resolve="dependent" spirit:dependency="(((8 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_MI&apos;))) - 1) + 1)">8</spirit:right>
            </spirit:vector>
          </spirit:physicalPort>
        </spirit:portMap>
      </spirit:portMaps>
      <spirit:parameters>
        <spirit:parameter>
          <spirit:name>BRIDGES</spirit:name>
          <spirit:value spirit:id="BUSIFPARAM_VALUE.S08_SC.BRIDGES">M00_SC:M01_SC:M02_SC:M03_SC:M04_SC:M05_SC:M06_SC:M07_SC:M08_SC:M09_SC:M10_SC:M11_SC:M12_SC:M13_SC:M14_SC:M15_SC</spirit:value>
        </spirit:parameter>
      </spirit:parameters>
      <spirit:vendorExtensions>
        <xilinx:busInterfaceInfo>
          <xilinx:enablement>
            <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="BUSIF_ENABLEMENT.S08_SC" xilinx:dependency="(spirit:decode(id(&apos;PARAM_VALUE.NUM_SI&apos;)) > 8)">false</xilinx:isEnabled>
          </xilinx:enablement>
        </xilinx:busInterfaceInfo>
      </spirit:vendorExtensions>
    </spirit:busInterface>
    <spirit:busInterface>
      <spirit:name>M08_SC</spirit:name>
      <spirit:displayName>M08_SC</spirit:displayName>
      <spirit:busType spirit:vendor="xilinx.com" spirit:library="interface" spirit:name="sc" spirit:version="1.0"/>
      <spirit:abstractionType spirit:vendor="xilinx.com" spirit:library="interface" spirit:name="sc_rtl" spirit:version="1.0"/>
      <spirit:master/>
      <spirit:portMaps>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>RECV</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>m_sc_recv</spirit:name>
            <spirit:vector>
              <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="((9 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_SI&apos;))) - 1)">17</spirit:left>
              <spirit:right spirit:format="long" spirit:resolve="dependent" spirit:dependency="(((8 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_SI&apos;))) - 1) + 1)">16</spirit:right>
            </spirit:vector>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>SEND</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>m_sc_send</spirit:name>
            <spirit:vector>
              <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="((9 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_SI&apos;))) - 1)">17</spirit:left>
              <spirit:right spirit:format="long" spirit:resolve="dependent" spirit:dependency="(((8 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_SI&apos;))) - 1) + 1)">16</spirit:right>
            </spirit:vector>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>REQ</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>m_sc_req</spirit:name>
            <spirit:vector>
              <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="((9 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_SI&apos;))) - 1)">17</spirit:left>
              <spirit:right spirit:format="long" spirit:resolve="dependent" spirit:dependency="(((8 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_SI&apos;))) - 1) + 1)">16</spirit:right>
            </spirit:vector>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>INFO</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>m_sc_info</spirit:name>
            <spirit:vector>
              <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="(((9 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_SI&apos;))) * 1) - 1)">17</spirit:left>
              <spirit:right spirit:format="long" spirit:resolve="dependent" spirit:dependency="((((8 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_SI&apos;))) * 1) - 1) + 1)">16</spirit:right>
            </spirit:vector>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>PAYLD</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>m_sc_payld</spirit:name>
            <spirit:vector>
              <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="((9 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_PAYLD_WIDTH&apos;))) - 1)">494</spirit:left>
              <spirit:right spirit:format="long" spirit:resolve="dependent" spirit:dependency="(((8 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_PAYLD_WIDTH&apos;))) - 1) + 1)">440</spirit:right>
            </spirit:vector>
          </spirit:physicalPort>
        </spirit:portMap>
      </spirit:portMaps>
      <spirit:vendorExtensions>
        <xilinx:busInterfaceInfo>
          <xilinx:enablement>
            <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="BUSIF_ENABLEMENT.M08_SC" xilinx:dependency="(spirit:decode(id(&apos;PARAM_VALUE.NUM_MI&apos;)) > 8)">false</xilinx:isEnabled>
          </xilinx:enablement>
        </xilinx:busInterfaceInfo>
      </spirit:vendorExtensions>
    </spirit:busInterface>
    <spirit:busInterface>
      <spirit:name>S09_SC</spirit:name>
      <spirit:displayName>S09_SC</spirit:displayName>
      <spirit:busType spirit:vendor="xilinx.com" spirit:library="interface" spirit:name="sc" spirit:version="1.0"/>
      <spirit:abstractionType spirit:vendor="xilinx.com" spirit:library="interface" spirit:name="sc_rtl" spirit:version="1.0"/>
      <spirit:slave/>
      <spirit:portMaps>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>SEND</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>s_sc_send</spirit:name>
            <spirit:vector>
              <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="((10 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_MI&apos;))) - 1)">9</spirit:left>
              <spirit:right spirit:format="long" spirit:resolve="dependent" spirit:dependency="(((9 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_MI&apos;))) - 1) + 1)">9</spirit:right>
            </spirit:vector>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>REQ</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>s_sc_req</spirit:name>
            <spirit:vector>
              <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="((10 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_MI&apos;))) - 1)">9</spirit:left>
              <spirit:right spirit:format="long" spirit:resolve="dependent" spirit:dependency="(((9 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_MI&apos;))) - 1) + 1)">9</spirit:right>
            </spirit:vector>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>INFO</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>s_sc_info</spirit:name>
            <spirit:vector>
              <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="(((10 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_MI&apos;))) * 1) - 1)">9</spirit:left>
              <spirit:right spirit:format="long" spirit:resolve="dependent" spirit:dependency="((((9 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_MI&apos;))) * 1) - 1) + 1)">9</spirit:right>
            </spirit:vector>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>PAYLD</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>s_sc_payld</spirit:name>
            <spirit:vector>
              <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="((10 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_PAYLD_WIDTH&apos;))) - 1)">549</spirit:left>
              <spirit:right spirit:format="long" spirit:resolve="dependent" spirit:dependency="(((9 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_PAYLD_WIDTH&apos;))) - 1) + 1)">495</spirit:right>
            </spirit:vector>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>RECV</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>s_sc_recv</spirit:name>
            <spirit:vector>
              <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="((10 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_MI&apos;))) - 1)">9</spirit:left>
              <spirit:right spirit:format="long" spirit:resolve="dependent" spirit:dependency="(((9 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_MI&apos;))) - 1) + 1)">9</spirit:right>
            </spirit:vector>
          </spirit:physicalPort>
        </spirit:portMap>
      </spirit:portMaps>
      <spirit:parameters>
        <spirit:parameter>
          <spirit:name>BRIDGES</spirit:name>
          <spirit:value spirit:id="BUSIFPARAM_VALUE.S09_SC.BRIDGES">M00_SC:M01_SC:M02_SC:M03_SC:M04_SC:M05_SC:M06_SC:M07_SC:M08_SC:M09_SC:M10_SC:M11_SC:M12_SC:M13_SC:M14_SC:M15_SC</spirit:value>
        </spirit:parameter>
      </spirit:parameters>
      <spirit:vendorExtensions>
        <xilinx:busInterfaceInfo>
          <xilinx:enablement>
            <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="BUSIF_ENABLEMENT.S09_SC" xilinx:dependency="(spirit:decode(id(&apos;PARAM_VALUE.NUM_SI&apos;)) > 9)">false</xilinx:isEnabled>
          </xilinx:enablement>
        </xilinx:busInterfaceInfo>
      </spirit:vendorExtensions>
    </spirit:busInterface>
    <spirit:busInterface>
      <spirit:name>M09_SC</spirit:name>
      <spirit:displayName>M09_SC</spirit:displayName>
      <spirit:busType spirit:vendor="xilinx.com" spirit:library="interface" spirit:name="sc" spirit:version="1.0"/>
      <spirit:abstractionType spirit:vendor="xilinx.com" spirit:library="interface" spirit:name="sc_rtl" spirit:version="1.0"/>
      <spirit:master/>
      <spirit:portMaps>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>RECV</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>m_sc_recv</spirit:name>
            <spirit:vector>
              <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="((10 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_SI&apos;))) - 1)">19</spirit:left>
              <spirit:right spirit:format="long" spirit:resolve="dependent" spirit:dependency="(((9 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_SI&apos;))) - 1) + 1)">18</spirit:right>
            </spirit:vector>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>SEND</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>m_sc_send</spirit:name>
            <spirit:vector>
              <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="((10 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_SI&apos;))) - 1)">19</spirit:left>
              <spirit:right spirit:format="long" spirit:resolve="dependent" spirit:dependency="(((9 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_SI&apos;))) - 1) + 1)">18</spirit:right>
            </spirit:vector>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>REQ</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>m_sc_req</spirit:name>
            <spirit:vector>
              <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="((10 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_SI&apos;))) - 1)">19</spirit:left>
              <spirit:right spirit:format="long" spirit:resolve="dependent" spirit:dependency="(((9 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_SI&apos;))) - 1) + 1)">18</spirit:right>
            </spirit:vector>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>INFO</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>m_sc_info</spirit:name>
            <spirit:vector>
              <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="(((10 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_SI&apos;))) * 1) - 1)">19</spirit:left>
              <spirit:right spirit:format="long" spirit:resolve="dependent" spirit:dependency="((((9 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_SI&apos;))) * 1) - 1) + 1)">18</spirit:right>
            </spirit:vector>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>PAYLD</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>m_sc_payld</spirit:name>
            <spirit:vector>
              <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="((10 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_PAYLD_WIDTH&apos;))) - 1)">549</spirit:left>
              <spirit:right spirit:format="long" spirit:resolve="dependent" spirit:dependency="(((9 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_PAYLD_WIDTH&apos;))) - 1) + 1)">495</spirit:right>
            </spirit:vector>
          </spirit:physicalPort>
        </spirit:portMap>
      </spirit:portMaps>
      <spirit:vendorExtensions>
        <xilinx:busInterfaceInfo>
          <xilinx:enablement>
            <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="BUSIF_ENABLEMENT.M09_SC" xilinx:dependency="(spirit:decode(id(&apos;PARAM_VALUE.NUM_MI&apos;)) > 9)">false</xilinx:isEnabled>
          </xilinx:enablement>
        </xilinx:busInterfaceInfo>
      </spirit:vendorExtensions>
    </spirit:busInterface>
    <spirit:busInterface>
      <spirit:name>S10_SC</spirit:name>
      <spirit:displayName>S10_SC</spirit:displayName>
      <spirit:busType spirit:vendor="xilinx.com" spirit:library="interface" spirit:name="sc" spirit:version="1.0"/>
      <spirit:abstractionType spirit:vendor="xilinx.com" spirit:library="interface" spirit:name="sc_rtl" spirit:version="1.0"/>
      <spirit:slave/>
      <spirit:portMaps>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>SEND</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>s_sc_send</spirit:name>
            <spirit:vector>
              <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="((11 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_MI&apos;))) - 1)">10</spirit:left>
              <spirit:right spirit:format="long" spirit:resolve="dependent" spirit:dependency="(((10 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_MI&apos;))) - 1) + 1)">10</spirit:right>
            </spirit:vector>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>REQ</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>s_sc_req</spirit:name>
            <spirit:vector>
              <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="((11 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_MI&apos;))) - 1)">10</spirit:left>
              <spirit:right spirit:format="long" spirit:resolve="dependent" spirit:dependency="(((10 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_MI&apos;))) - 1) + 1)">10</spirit:right>
            </spirit:vector>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>INFO</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>s_sc_info</spirit:name>
            <spirit:vector>
              <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="(((11 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_MI&apos;))) * 1) - 1)">10</spirit:left>
              <spirit:right spirit:format="long" spirit:resolve="dependent" spirit:dependency="((((10 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_MI&apos;))) * 1) - 1) + 1)">10</spirit:right>
            </spirit:vector>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>PAYLD</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>s_sc_payld</spirit:name>
            <spirit:vector>
              <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="((11 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_PAYLD_WIDTH&apos;))) - 1)">604</spirit:left>
              <spirit:right spirit:format="long" spirit:resolve="dependent" spirit:dependency="(((10 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_PAYLD_WIDTH&apos;))) - 1) + 1)">550</spirit:right>
            </spirit:vector>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>RECV</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>s_sc_recv</spirit:name>
            <spirit:vector>
              <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="((11 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_MI&apos;))) - 1)">10</spirit:left>
              <spirit:right spirit:format="long" spirit:resolve="dependent" spirit:dependency="(((10 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_MI&apos;))) - 1) + 1)">10</spirit:right>
            </spirit:vector>
          </spirit:physicalPort>
        </spirit:portMap>
      </spirit:portMaps>
      <spirit:parameters>
        <spirit:parameter>
          <spirit:name>BRIDGES</spirit:name>
          <spirit:value spirit:id="BUSIFPARAM_VALUE.S10_SC.BRIDGES">M00_SC:M01_SC:M02_SC:M03_SC:M04_SC:M05_SC:M06_SC:M07_SC:M08_SC:M09_SC:M10_SC:M11_SC:M12_SC:M13_SC:M14_SC:M15_SC</spirit:value>
        </spirit:parameter>
      </spirit:parameters>
      <spirit:vendorExtensions>
        <xilinx:busInterfaceInfo>
          <xilinx:enablement>
            <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="BUSIF_ENABLEMENT.S10_SC" xilinx:dependency="(spirit:decode(id(&apos;PARAM_VALUE.NUM_SI&apos;)) > 10)">false</xilinx:isEnabled>
          </xilinx:enablement>
        </xilinx:busInterfaceInfo>
      </spirit:vendorExtensions>
    </spirit:busInterface>
    <spirit:busInterface>
      <spirit:name>M10_SC</spirit:name>
      <spirit:displayName>M10_SC</spirit:displayName>
      <spirit:busType spirit:vendor="xilinx.com" spirit:library="interface" spirit:name="sc" spirit:version="1.0"/>
      <spirit:abstractionType spirit:vendor="xilinx.com" spirit:library="interface" spirit:name="sc_rtl" spirit:version="1.0"/>
      <spirit:master/>
      <spirit:portMaps>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>RECV</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>m_sc_recv</spirit:name>
            <spirit:vector>
              <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="((11 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_SI&apos;))) - 1)">21</spirit:left>
              <spirit:right spirit:format="long" spirit:resolve="dependent" spirit:dependency="(((10 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_SI&apos;))) - 1) + 1)">20</spirit:right>
            </spirit:vector>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>SEND</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>m_sc_send</spirit:name>
            <spirit:vector>
              <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="((11 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_SI&apos;))) - 1)">21</spirit:left>
              <spirit:right spirit:format="long" spirit:resolve="dependent" spirit:dependency="(((10 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_SI&apos;))) - 1) + 1)">20</spirit:right>
            </spirit:vector>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>REQ</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>m_sc_req</spirit:name>
            <spirit:vector>
              <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="((11 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_SI&apos;))) - 1)">21</spirit:left>
              <spirit:right spirit:format="long" spirit:resolve="dependent" spirit:dependency="(((10 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_SI&apos;))) - 1) + 1)">20</spirit:right>
            </spirit:vector>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>INFO</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>m_sc_info</spirit:name>
            <spirit:vector>
              <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="(((11 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_SI&apos;))) * 1) - 1)">21</spirit:left>
              <spirit:right spirit:format="long" spirit:resolve="dependent" spirit:dependency="((((10 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_SI&apos;))) * 1) - 1) + 1)">20</spirit:right>
            </spirit:vector>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>PAYLD</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>m_sc_payld</spirit:name>
            <spirit:vector>
              <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="((11 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_PAYLD_WIDTH&apos;))) - 1)">604</spirit:left>
              <spirit:right spirit:format="long" spirit:resolve="dependent" spirit:dependency="(((10 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_PAYLD_WIDTH&apos;))) - 1) + 1)">550</spirit:right>
            </spirit:vector>
          </spirit:physicalPort>
        </spirit:portMap>
      </spirit:portMaps>
      <spirit:vendorExtensions>
        <xilinx:busInterfaceInfo>
          <xilinx:enablement>
            <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="BUSIF_ENABLEMENT.M10_SC" xilinx:dependency="(spirit:decode(id(&apos;PARAM_VALUE.NUM_MI&apos;)) > 10)">false</xilinx:isEnabled>
          </xilinx:enablement>
        </xilinx:busInterfaceInfo>
      </spirit:vendorExtensions>
    </spirit:busInterface>
    <spirit:busInterface>
      <spirit:name>S11_SC</spirit:name>
      <spirit:displayName>S11_SC</spirit:displayName>
      <spirit:busType spirit:vendor="xilinx.com" spirit:library="interface" spirit:name="sc" spirit:version="1.0"/>
      <spirit:abstractionType spirit:vendor="xilinx.com" spirit:library="interface" spirit:name="sc_rtl" spirit:version="1.0"/>
      <spirit:slave/>
      <spirit:portMaps>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>SEND</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>s_sc_send</spirit:name>
            <spirit:vector>
              <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="((12 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_MI&apos;))) - 1)">11</spirit:left>
              <spirit:right spirit:format="long" spirit:resolve="dependent" spirit:dependency="(((11 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_MI&apos;))) - 1) + 1)">11</spirit:right>
            </spirit:vector>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>REQ</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>s_sc_req</spirit:name>
            <spirit:vector>
              <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="((12 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_MI&apos;))) - 1)">11</spirit:left>
              <spirit:right spirit:format="long" spirit:resolve="dependent" spirit:dependency="(((11 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_MI&apos;))) - 1) + 1)">11</spirit:right>
            </spirit:vector>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>INFO</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>s_sc_info</spirit:name>
            <spirit:vector>
              <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="(((12 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_MI&apos;))) * 1) - 1)">11</spirit:left>
              <spirit:right spirit:format="long" spirit:resolve="dependent" spirit:dependency="((((11 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_MI&apos;))) * 1) - 1) + 1)">11</spirit:right>
            </spirit:vector>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>PAYLD</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>s_sc_payld</spirit:name>
            <spirit:vector>
              <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="((12 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_PAYLD_WIDTH&apos;))) - 1)">659</spirit:left>
              <spirit:right spirit:format="long" spirit:resolve="dependent" spirit:dependency="(((11 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_PAYLD_WIDTH&apos;))) - 1) + 1)">605</spirit:right>
            </spirit:vector>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>RECV</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>s_sc_recv</spirit:name>
            <spirit:vector>
              <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="((12 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_MI&apos;))) - 1)">11</spirit:left>
              <spirit:right spirit:format="long" spirit:resolve="dependent" spirit:dependency="(((11 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_MI&apos;))) - 1) + 1)">11</spirit:right>
            </spirit:vector>
          </spirit:physicalPort>
        </spirit:portMap>
      </spirit:portMaps>
      <spirit:parameters>
        <spirit:parameter>
          <spirit:name>BRIDGES</spirit:name>
          <spirit:value spirit:id="BUSIFPARAM_VALUE.S11_SC.BRIDGES">M00_SC:M01_SC:M02_SC:M03_SC:M04_SC:M05_SC:M06_SC:M07_SC:M08_SC:M09_SC:M10_SC:M11_SC:M12_SC:M13_SC:M14_SC:M15_SC</spirit:value>
        </spirit:parameter>
      </spirit:parameters>
      <spirit:vendorExtensions>
        <xilinx:busInterfaceInfo>
          <xilinx:enablement>
            <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="BUSIF_ENABLEMENT.S11_SC" xilinx:dependency="(spirit:decode(id(&apos;PARAM_VALUE.NUM_SI&apos;)) > 11)">false</xilinx:isEnabled>
          </xilinx:enablement>
        </xilinx:busInterfaceInfo>
      </spirit:vendorExtensions>
    </spirit:busInterface>
    <spirit:busInterface>
      <spirit:name>M11_SC</spirit:name>
      <spirit:displayName>M11_SC</spirit:displayName>
      <spirit:busType spirit:vendor="xilinx.com" spirit:library="interface" spirit:name="sc" spirit:version="1.0"/>
      <spirit:abstractionType spirit:vendor="xilinx.com" spirit:library="interface" spirit:name="sc_rtl" spirit:version="1.0"/>
      <spirit:master/>
      <spirit:portMaps>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>RECV</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>m_sc_recv</spirit:name>
            <spirit:vector>
              <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="((12 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_SI&apos;))) - 1)">23</spirit:left>
              <spirit:right spirit:format="long" spirit:resolve="dependent" spirit:dependency="(((11 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_SI&apos;))) - 1) + 1)">22</spirit:right>
            </spirit:vector>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>SEND</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>m_sc_send</spirit:name>
            <spirit:vector>
              <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="((12 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_SI&apos;))) - 1)">23</spirit:left>
              <spirit:right spirit:format="long" spirit:resolve="dependent" spirit:dependency="(((11 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_SI&apos;))) - 1) + 1)">22</spirit:right>
            </spirit:vector>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>REQ</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>m_sc_req</spirit:name>
            <spirit:vector>
              <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="((12 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_SI&apos;))) - 1)">23</spirit:left>
              <spirit:right spirit:format="long" spirit:resolve="dependent" spirit:dependency="(((11 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_SI&apos;))) - 1) + 1)">22</spirit:right>
            </spirit:vector>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>INFO</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>m_sc_info</spirit:name>
            <spirit:vector>
              <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="(((12 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_SI&apos;))) * 1) - 1)">23</spirit:left>
              <spirit:right spirit:format="long" spirit:resolve="dependent" spirit:dependency="((((11 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_SI&apos;))) * 1) - 1) + 1)">22</spirit:right>
            </spirit:vector>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>PAYLD</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>m_sc_payld</spirit:name>
            <spirit:vector>
              <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="((12 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_PAYLD_WIDTH&apos;))) - 1)">659</spirit:left>
              <spirit:right spirit:format="long" spirit:resolve="dependent" spirit:dependency="(((11 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_PAYLD_WIDTH&apos;))) - 1) + 1)">605</spirit:right>
            </spirit:vector>
          </spirit:physicalPort>
        </spirit:portMap>
      </spirit:portMaps>
      <spirit:vendorExtensions>
        <xilinx:busInterfaceInfo>
          <xilinx:enablement>
            <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="BUSIF_ENABLEMENT.M11_SC" xilinx:dependency="(spirit:decode(id(&apos;PARAM_VALUE.NUM_MI&apos;)) > 11)">false</xilinx:isEnabled>
          </xilinx:enablement>
        </xilinx:busInterfaceInfo>
      </spirit:vendorExtensions>
    </spirit:busInterface>
    <spirit:busInterface>
      <spirit:name>S12_SC</spirit:name>
      <spirit:displayName>S12_SC</spirit:displayName>
      <spirit:busType spirit:vendor="xilinx.com" spirit:library="interface" spirit:name="sc" spirit:version="1.0"/>
      <spirit:abstractionType spirit:vendor="xilinx.com" spirit:library="interface" spirit:name="sc_rtl" spirit:version="1.0"/>
      <spirit:slave/>
      <spirit:portMaps>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>SEND</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>s_sc_send</spirit:name>
            <spirit:vector>
              <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="((13 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_MI&apos;))) - 1)">12</spirit:left>
              <spirit:right spirit:format="long" spirit:resolve="dependent" spirit:dependency="(((12 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_MI&apos;))) - 1) + 1)">12</spirit:right>
            </spirit:vector>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>REQ</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>s_sc_req</spirit:name>
            <spirit:vector>
              <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="((13 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_MI&apos;))) - 1)">12</spirit:left>
              <spirit:right spirit:format="long" spirit:resolve="dependent" spirit:dependency="(((12 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_MI&apos;))) - 1) + 1)">12</spirit:right>
            </spirit:vector>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>INFO</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>s_sc_info</spirit:name>
            <spirit:vector>
              <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="(((13 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_MI&apos;))) * 1) - 1)">12</spirit:left>
              <spirit:right spirit:format="long" spirit:resolve="dependent" spirit:dependency="((((12 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_MI&apos;))) * 1) - 1) + 1)">12</spirit:right>
            </spirit:vector>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>PAYLD</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>s_sc_payld</spirit:name>
            <spirit:vector>
              <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="((13 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_PAYLD_WIDTH&apos;))) - 1)">714</spirit:left>
              <spirit:right spirit:format="long" spirit:resolve="dependent" spirit:dependency="(((12 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_PAYLD_WIDTH&apos;))) - 1) + 1)">660</spirit:right>
            </spirit:vector>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>RECV</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>s_sc_recv</spirit:name>
            <spirit:vector>
              <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="((13 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_MI&apos;))) - 1)">12</spirit:left>
              <spirit:right spirit:format="long" spirit:resolve="dependent" spirit:dependency="(((12 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_MI&apos;))) - 1) + 1)">12</spirit:right>
            </spirit:vector>
          </spirit:physicalPort>
        </spirit:portMap>
      </spirit:portMaps>
      <spirit:parameters>
        <spirit:parameter>
          <spirit:name>BRIDGES</spirit:name>
          <spirit:value spirit:id="BUSIFPARAM_VALUE.S12_SC.BRIDGES">M00_SC:M01_SC:M02_SC:M03_SC:M04_SC:M05_SC:M06_SC:M07_SC:M08_SC:M09_SC:M10_SC:M11_SC:M12_SC:M13_SC:M14_SC:M15_SC</spirit:value>
        </spirit:parameter>
      </spirit:parameters>
      <spirit:vendorExtensions>
        <xilinx:busInterfaceInfo>
          <xilinx:enablement>
            <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="BUSIF_ENABLEMENT.S12_SC" xilinx:dependency="(spirit:decode(id(&apos;PARAM_VALUE.NUM_SI&apos;)) > 12)">false</xilinx:isEnabled>
          </xilinx:enablement>
        </xilinx:busInterfaceInfo>
      </spirit:vendorExtensions>
    </spirit:busInterface>
    <spirit:busInterface>
      <spirit:name>M12_SC</spirit:name>
      <spirit:displayName>M12_SC</spirit:displayName>
      <spirit:busType spirit:vendor="xilinx.com" spirit:library="interface" spirit:name="sc" spirit:version="1.0"/>
      <spirit:abstractionType spirit:vendor="xilinx.com" spirit:library="interface" spirit:name="sc_rtl" spirit:version="1.0"/>
      <spirit:master/>
      <spirit:portMaps>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>RECV</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>m_sc_recv</spirit:name>
            <spirit:vector>
              <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="((13 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_SI&apos;))) - 1)">25</spirit:left>
              <spirit:right spirit:format="long" spirit:resolve="dependent" spirit:dependency="(((12 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_SI&apos;))) - 1) + 1)">24</spirit:right>
            </spirit:vector>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>SEND</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>m_sc_send</spirit:name>
            <spirit:vector>
              <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="((13 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_SI&apos;))) - 1)">25</spirit:left>
              <spirit:right spirit:format="long" spirit:resolve="dependent" spirit:dependency="(((12 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_SI&apos;))) - 1) + 1)">24</spirit:right>
            </spirit:vector>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>REQ</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>m_sc_req</spirit:name>
            <spirit:vector>
              <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="((13 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_SI&apos;))) - 1)">25</spirit:left>
              <spirit:right spirit:format="long" spirit:resolve="dependent" spirit:dependency="(((12 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_SI&apos;))) - 1) + 1)">24</spirit:right>
            </spirit:vector>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>INFO</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>m_sc_info</spirit:name>
            <spirit:vector>
              <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="(((13 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_SI&apos;))) * 1) - 1)">25</spirit:left>
              <spirit:right spirit:format="long" spirit:resolve="dependent" spirit:dependency="((((12 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_SI&apos;))) * 1) - 1) + 1)">24</spirit:right>
            </spirit:vector>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>PAYLD</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>m_sc_payld</spirit:name>
            <spirit:vector>
              <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="((13 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_PAYLD_WIDTH&apos;))) - 1)">714</spirit:left>
              <spirit:right spirit:format="long" spirit:resolve="dependent" spirit:dependency="(((12 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_PAYLD_WIDTH&apos;))) - 1) + 1)">660</spirit:right>
            </spirit:vector>
          </spirit:physicalPort>
        </spirit:portMap>
      </spirit:portMaps>
      <spirit:vendorExtensions>
        <xilinx:busInterfaceInfo>
          <xilinx:enablement>
            <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="BUSIF_ENABLEMENT.M12_SC" xilinx:dependency="(spirit:decode(id(&apos;PARAM_VALUE.NUM_MI&apos;)) > 12)">false</xilinx:isEnabled>
          </xilinx:enablement>
        </xilinx:busInterfaceInfo>
      </spirit:vendorExtensions>
    </spirit:busInterface>
    <spirit:busInterface>
      <spirit:name>S13_SC</spirit:name>
      <spirit:displayName>S13_SC</spirit:displayName>
      <spirit:busType spirit:vendor="xilinx.com" spirit:library="interface" spirit:name="sc" spirit:version="1.0"/>
      <spirit:abstractionType spirit:vendor="xilinx.com" spirit:library="interface" spirit:name="sc_rtl" spirit:version="1.0"/>
      <spirit:slave/>
      <spirit:portMaps>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>SEND</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>s_sc_send</spirit:name>
            <spirit:vector>
              <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="((14 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_MI&apos;))) - 1)">13</spirit:left>
              <spirit:right spirit:format="long" spirit:resolve="dependent" spirit:dependency="(((13 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_MI&apos;))) - 1) + 1)">13</spirit:right>
            </spirit:vector>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>REQ</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>s_sc_req</spirit:name>
            <spirit:vector>
              <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="((14 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_MI&apos;))) - 1)">13</spirit:left>
              <spirit:right spirit:format="long" spirit:resolve="dependent" spirit:dependency="(((13 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_MI&apos;))) - 1) + 1)">13</spirit:right>
            </spirit:vector>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>INFO</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>s_sc_info</spirit:name>
            <spirit:vector>
              <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="(((14 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_MI&apos;))) * 1) - 1)">13</spirit:left>
              <spirit:right spirit:format="long" spirit:resolve="dependent" spirit:dependency="((((13 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_MI&apos;))) * 1) - 1) + 1)">13</spirit:right>
            </spirit:vector>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>PAYLD</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>s_sc_payld</spirit:name>
            <spirit:vector>
              <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="((14 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_PAYLD_WIDTH&apos;))) - 1)">769</spirit:left>
              <spirit:right spirit:format="long" spirit:resolve="dependent" spirit:dependency="(((13 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_PAYLD_WIDTH&apos;))) - 1) + 1)">715</spirit:right>
            </spirit:vector>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>RECV</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>s_sc_recv</spirit:name>
            <spirit:vector>
              <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="((14 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_MI&apos;))) - 1)">13</spirit:left>
              <spirit:right spirit:format="long" spirit:resolve="dependent" spirit:dependency="(((13 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_MI&apos;))) - 1) + 1)">13</spirit:right>
            </spirit:vector>
          </spirit:physicalPort>
        </spirit:portMap>
      </spirit:portMaps>
      <spirit:parameters>
        <spirit:parameter>
          <spirit:name>BRIDGES</spirit:name>
          <spirit:value spirit:id="BUSIFPARAM_VALUE.S13_SC.BRIDGES">M00_SC:M01_SC:M02_SC:M03_SC:M04_SC:M05_SC:M06_SC:M07_SC:M08_SC:M09_SC:M10_SC:M11_SC:M12_SC:M13_SC:M14_SC:M15_SC</spirit:value>
        </spirit:parameter>
      </spirit:parameters>
      <spirit:vendorExtensions>
        <xilinx:busInterfaceInfo>
          <xilinx:enablement>
            <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="BUSIF_ENABLEMENT.S13_SC" xilinx:dependency="(spirit:decode(id(&apos;PARAM_VALUE.NUM_SI&apos;)) > 13)">false</xilinx:isEnabled>
          </xilinx:enablement>
        </xilinx:busInterfaceInfo>
      </spirit:vendorExtensions>
    </spirit:busInterface>
    <spirit:busInterface>
      <spirit:name>M13_SC</spirit:name>
      <spirit:displayName>M13_SC</spirit:displayName>
      <spirit:busType spirit:vendor="xilinx.com" spirit:library="interface" spirit:name="sc" spirit:version="1.0"/>
      <spirit:abstractionType spirit:vendor="xilinx.com" spirit:library="interface" spirit:name="sc_rtl" spirit:version="1.0"/>
      <spirit:master/>
      <spirit:portMaps>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>RECV</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>m_sc_recv</spirit:name>
            <spirit:vector>
              <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="((14 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_SI&apos;))) - 1)">27</spirit:left>
              <spirit:right spirit:format="long" spirit:resolve="dependent" spirit:dependency="(((13 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_SI&apos;))) - 1) + 1)">26</spirit:right>
            </spirit:vector>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>SEND</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>m_sc_send</spirit:name>
            <spirit:vector>
              <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="((14 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_SI&apos;))) - 1)">27</spirit:left>
              <spirit:right spirit:format="long" spirit:resolve="dependent" spirit:dependency="(((13 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_SI&apos;))) - 1) + 1)">26</spirit:right>
            </spirit:vector>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>REQ</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>m_sc_req</spirit:name>
            <spirit:vector>
              <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="((14 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_SI&apos;))) - 1)">27</spirit:left>
              <spirit:right spirit:format="long" spirit:resolve="dependent" spirit:dependency="(((13 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_SI&apos;))) - 1) + 1)">26</spirit:right>
            </spirit:vector>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>INFO</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>m_sc_info</spirit:name>
            <spirit:vector>
              <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="(((14 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_SI&apos;))) * 1) - 1)">27</spirit:left>
              <spirit:right spirit:format="long" spirit:resolve="dependent" spirit:dependency="((((13 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_SI&apos;))) * 1) - 1) + 1)">26</spirit:right>
            </spirit:vector>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>PAYLD</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>m_sc_payld</spirit:name>
            <spirit:vector>
              <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="((14 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_PAYLD_WIDTH&apos;))) - 1)">769</spirit:left>
              <spirit:right spirit:format="long" spirit:resolve="dependent" spirit:dependency="(((13 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_PAYLD_WIDTH&apos;))) - 1) + 1)">715</spirit:right>
            </spirit:vector>
          </spirit:physicalPort>
        </spirit:portMap>
      </spirit:portMaps>
      <spirit:vendorExtensions>
        <xilinx:busInterfaceInfo>
          <xilinx:enablement>
            <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="BUSIF_ENABLEMENT.M13_SC" xilinx:dependency="(spirit:decode(id(&apos;PARAM_VALUE.NUM_MI&apos;)) > 13)">false</xilinx:isEnabled>
          </xilinx:enablement>
        </xilinx:busInterfaceInfo>
      </spirit:vendorExtensions>
    </spirit:busInterface>
    <spirit:busInterface>
      <spirit:name>S14_SC</spirit:name>
      <spirit:displayName>S14_SC</spirit:displayName>
      <spirit:busType spirit:vendor="xilinx.com" spirit:library="interface" spirit:name="sc" spirit:version="1.0"/>
      <spirit:abstractionType spirit:vendor="xilinx.com" spirit:library="interface" spirit:name="sc_rtl" spirit:version="1.0"/>
      <spirit:slave/>
      <spirit:portMaps>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>SEND</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>s_sc_send</spirit:name>
            <spirit:vector>
              <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="((15 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_MI&apos;))) - 1)">14</spirit:left>
              <spirit:right spirit:format="long" spirit:resolve="dependent" spirit:dependency="(((14 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_MI&apos;))) - 1) + 1)">14</spirit:right>
            </spirit:vector>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>REQ</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>s_sc_req</spirit:name>
            <spirit:vector>
              <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="((15 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_MI&apos;))) - 1)">14</spirit:left>
              <spirit:right spirit:format="long" spirit:resolve="dependent" spirit:dependency="(((14 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_MI&apos;))) - 1) + 1)">14</spirit:right>
            </spirit:vector>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>INFO</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>s_sc_info</spirit:name>
            <spirit:vector>
              <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="(((15 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_MI&apos;))) * 1) - 1)">14</spirit:left>
              <spirit:right spirit:format="long" spirit:resolve="dependent" spirit:dependency="((((14 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_MI&apos;))) * 1) - 1) + 1)">14</spirit:right>
            </spirit:vector>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>PAYLD</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>s_sc_payld</spirit:name>
            <spirit:vector>
              <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="((15 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_PAYLD_WIDTH&apos;))) - 1)">824</spirit:left>
              <spirit:right spirit:format="long" spirit:resolve="dependent" spirit:dependency="(((14 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_PAYLD_WIDTH&apos;))) - 1) + 1)">770</spirit:right>
            </spirit:vector>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>RECV</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>s_sc_recv</spirit:name>
            <spirit:vector>
              <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="((15 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_MI&apos;))) - 1)">14</spirit:left>
              <spirit:right spirit:format="long" spirit:resolve="dependent" spirit:dependency="(((14 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_MI&apos;))) - 1) + 1)">14</spirit:right>
            </spirit:vector>
          </spirit:physicalPort>
        </spirit:portMap>
      </spirit:portMaps>
      <spirit:parameters>
        <spirit:parameter>
          <spirit:name>BRIDGES</spirit:name>
          <spirit:value spirit:id="BUSIFPARAM_VALUE.S14_SC.BRIDGES">M00_SC:M01_SC:M02_SC:M03_SC:M04_SC:M05_SC:M06_SC:M07_SC:M08_SC:M09_SC:M10_SC:M11_SC:M12_SC:M13_SC:M14_SC:M15_SC</spirit:value>
        </spirit:parameter>
      </spirit:parameters>
      <spirit:vendorExtensions>
        <xilinx:busInterfaceInfo>
          <xilinx:enablement>
            <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="BUSIF_ENABLEMENT.S14_SC" xilinx:dependency="(spirit:decode(id(&apos;PARAM_VALUE.NUM_SI&apos;)) > 14)">false</xilinx:isEnabled>
          </xilinx:enablement>
        </xilinx:busInterfaceInfo>
      </spirit:vendorExtensions>
    </spirit:busInterface>
    <spirit:busInterface>
      <spirit:name>M14_SC</spirit:name>
      <spirit:displayName>M14_SC</spirit:displayName>
      <spirit:busType spirit:vendor="xilinx.com" spirit:library="interface" spirit:name="sc" spirit:version="1.0"/>
      <spirit:abstractionType spirit:vendor="xilinx.com" spirit:library="interface" spirit:name="sc_rtl" spirit:version="1.0"/>
      <spirit:master/>
      <spirit:portMaps>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>RECV</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>m_sc_recv</spirit:name>
            <spirit:vector>
              <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="((15 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_SI&apos;))) - 1)">29</spirit:left>
              <spirit:right spirit:format="long" spirit:resolve="dependent" spirit:dependency="(((14 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_SI&apos;))) - 1) + 1)">28</spirit:right>
            </spirit:vector>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>SEND</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>m_sc_send</spirit:name>
            <spirit:vector>
              <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="((15 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_SI&apos;))) - 1)">29</spirit:left>
              <spirit:right spirit:format="long" spirit:resolve="dependent" spirit:dependency="(((14 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_SI&apos;))) - 1) + 1)">28</spirit:right>
            </spirit:vector>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>REQ</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>m_sc_req</spirit:name>
            <spirit:vector>
              <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="((15 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_SI&apos;))) - 1)">29</spirit:left>
              <spirit:right spirit:format="long" spirit:resolve="dependent" spirit:dependency="(((14 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_SI&apos;))) - 1) + 1)">28</spirit:right>
            </spirit:vector>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>INFO</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>m_sc_info</spirit:name>
            <spirit:vector>
              <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="(((15 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_SI&apos;))) * 1) - 1)">29</spirit:left>
              <spirit:right spirit:format="long" spirit:resolve="dependent" spirit:dependency="((((14 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_SI&apos;))) * 1) - 1) + 1)">28</spirit:right>
            </spirit:vector>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>PAYLD</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>m_sc_payld</spirit:name>
            <spirit:vector>
              <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="((15 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_PAYLD_WIDTH&apos;))) - 1)">824</spirit:left>
              <spirit:right spirit:format="long" spirit:resolve="dependent" spirit:dependency="(((14 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_PAYLD_WIDTH&apos;))) - 1) + 1)">770</spirit:right>
            </spirit:vector>
          </spirit:physicalPort>
        </spirit:portMap>
      </spirit:portMaps>
      <spirit:vendorExtensions>
        <xilinx:busInterfaceInfo>
          <xilinx:enablement>
            <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="BUSIF_ENABLEMENT.M14_SC" xilinx:dependency="(spirit:decode(id(&apos;PARAM_VALUE.NUM_MI&apos;)) > 14)">false</xilinx:isEnabled>
          </xilinx:enablement>
        </xilinx:busInterfaceInfo>
      </spirit:vendorExtensions>
    </spirit:busInterface>
    <spirit:busInterface>
      <spirit:name>S15_SC</spirit:name>
      <spirit:displayName>S15_SC</spirit:displayName>
      <spirit:busType spirit:vendor="xilinx.com" spirit:library="interface" spirit:name="sc" spirit:version="1.0"/>
      <spirit:abstractionType spirit:vendor="xilinx.com" spirit:library="interface" spirit:name="sc_rtl" spirit:version="1.0"/>
      <spirit:slave/>
      <spirit:portMaps>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>SEND</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>s_sc_send</spirit:name>
            <spirit:vector>
              <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="((16 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_MI&apos;))) - 1)">15</spirit:left>
              <spirit:right spirit:format="long" spirit:resolve="dependent" spirit:dependency="(((15 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_MI&apos;))) - 1) + 1)">15</spirit:right>
            </spirit:vector>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>REQ</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>s_sc_req</spirit:name>
            <spirit:vector>
              <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="((16 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_MI&apos;))) - 1)">15</spirit:left>
              <spirit:right spirit:format="long" spirit:resolve="dependent" spirit:dependency="(((15 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_MI&apos;))) - 1) + 1)">15</spirit:right>
            </spirit:vector>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>INFO</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>s_sc_info</spirit:name>
            <spirit:vector>
              <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="(((16 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_MI&apos;))) * 1) - 1)">15</spirit:left>
              <spirit:right spirit:format="long" spirit:resolve="dependent" spirit:dependency="((((15 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_MI&apos;))) * 1) - 1) + 1)">15</spirit:right>
            </spirit:vector>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>PAYLD</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>s_sc_payld</spirit:name>
            <spirit:vector>
              <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="((16 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_PAYLD_WIDTH&apos;))) - 1)">879</spirit:left>
              <spirit:right spirit:format="long" spirit:resolve="dependent" spirit:dependency="(((15 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_PAYLD_WIDTH&apos;))) - 1) + 1)">825</spirit:right>
            </spirit:vector>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>RECV</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>s_sc_recv</spirit:name>
            <spirit:vector>
              <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="((16 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_MI&apos;))) - 1)">15</spirit:left>
              <spirit:right spirit:format="long" spirit:resolve="dependent" spirit:dependency="(((15 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_MI&apos;))) - 1) + 1)">15</spirit:right>
            </spirit:vector>
          </spirit:physicalPort>
        </spirit:portMap>
      </spirit:portMaps>
      <spirit:parameters>
        <spirit:parameter>
          <spirit:name>BRIDGES</spirit:name>
          <spirit:value spirit:id="BUSIFPARAM_VALUE.S15_SC.BRIDGES">M00_SC:M01_SC:M02_SC:M03_SC:M04_SC:M05_SC:M06_SC:M07_SC:M08_SC:M09_SC:M10_SC:M11_SC:M12_SC:M13_SC:M14_SC:M15_SC</spirit:value>
        </spirit:parameter>
      </spirit:parameters>
      <spirit:vendorExtensions>
        <xilinx:busInterfaceInfo>
          <xilinx:enablement>
            <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="BUSIF_ENABLEMENT.S15_SC" xilinx:dependency="(spirit:decode(id(&apos;PARAM_VALUE.NUM_SI&apos;)) > 15)">false</xilinx:isEnabled>
          </xilinx:enablement>
        </xilinx:busInterfaceInfo>
      </spirit:vendorExtensions>
    </spirit:busInterface>
    <spirit:busInterface>
      <spirit:name>M15_SC</spirit:name>
      <spirit:displayName>M15_SC</spirit:displayName>
      <spirit:busType spirit:vendor="xilinx.com" spirit:library="interface" spirit:name="sc" spirit:version="1.0"/>
      <spirit:abstractionType spirit:vendor="xilinx.com" spirit:library="interface" spirit:name="sc_rtl" spirit:version="1.0"/>
      <spirit:master/>
      <spirit:portMaps>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>RECV</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>m_sc_recv</spirit:name>
            <spirit:vector>
              <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="((16 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_SI&apos;))) - 1)">31</spirit:left>
              <spirit:right spirit:format="long" spirit:resolve="dependent" spirit:dependency="(((15 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_SI&apos;))) - 1) + 1)">30</spirit:right>
            </spirit:vector>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>SEND</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>m_sc_send</spirit:name>
            <spirit:vector>
              <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="((16 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_SI&apos;))) - 1)">31</spirit:left>
              <spirit:right spirit:format="long" spirit:resolve="dependent" spirit:dependency="(((15 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_SI&apos;))) - 1) + 1)">30</spirit:right>
            </spirit:vector>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>REQ</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>m_sc_req</spirit:name>
            <spirit:vector>
              <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="((16 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_SI&apos;))) - 1)">31</spirit:left>
              <spirit:right spirit:format="long" spirit:resolve="dependent" spirit:dependency="(((15 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_SI&apos;))) - 1) + 1)">30</spirit:right>
            </spirit:vector>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>INFO</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>m_sc_info</spirit:name>
            <spirit:vector>
              <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="(((16 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_SI&apos;))) * 1) - 1)">31</spirit:left>
              <spirit:right spirit:format="long" spirit:resolve="dependent" spirit:dependency="((((15 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_SI&apos;))) * 1) - 1) + 1)">30</spirit:right>
            </spirit:vector>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>PAYLD</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>m_sc_payld</spirit:name>
            <spirit:vector>
              <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="((16 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_PAYLD_WIDTH&apos;))) - 1)">879</spirit:left>
              <spirit:right spirit:format="long" spirit:resolve="dependent" spirit:dependency="(((15 * spirit:decode(id(&apos;MODELPARAM_VALUE.C_PAYLD_WIDTH&apos;))) - 1) + 1)">825</spirit:right>
            </spirit:vector>
          </spirit:physicalPort>
        </spirit:portMap>
      </spirit:portMaps>
      <spirit:vendorExtensions>
        <xilinx:busInterfaceInfo>
          <xilinx:enablement>
            <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="BUSIF_ENABLEMENT.M15_SC" xilinx:dependency="(spirit:decode(id(&apos;PARAM_VALUE.NUM_MI&apos;)) > 15)">false</xilinx:isEnabled>
          </xilinx:enablement>
        </xilinx:busInterfaceInfo>
      </spirit:vendorExtensions>
    </spirit:busInterface>
  </spirit:busInterfaces>
  <spirit:model>
    <spirit:views>
      <spirit:view>
        <spirit:name>xilinx_synthesisconstraints</spirit:name>
        <spirit:displayName>Synthesis Constraints</spirit:displayName>
        <spirit:envIdentifier>:vivado.xilinx.com:synthesis.constraints</spirit:envIdentifier>
        <spirit:parameters>
          <spirit:parameter>
            <spirit:name>outputProductCRC</spirit:name>
            <spirit:value>9:16db66c1</spirit:value>
          </spirit:parameter>
        </spirit:parameters>
      </spirit:view>
      <spirit:view>
        <spirit:name>xilinx_verilogbehavioralsimulation</spirit:name>
        <spirit:displayName>Verilog Simulation</spirit:displayName>
        <spirit:envIdentifier>verilogSource:vivado.xilinx.com:simulation</spirit:envIdentifier>
        <spirit:language>verilog</spirit:language>
        <spirit:modelName>sc_switchboard_v1_0_8_top</spirit:modelName>
        <spirit:fileSetRef>
          <spirit:localName>xilinx_verilogbehavioralsimulation_xilinx_com_ip_sc_util_1_0__ref_view_fileset</spirit:localName>
        </spirit:fileSetRef>
        <spirit:fileSetRef>
          <spirit:localName>xilinx_verilogbehavioralsimulation_view_fileset</spirit:localName>
        </spirit:fileSetRef>
        <spirit:parameters>
          <spirit:parameter>
            <spirit:name>GENtimestamp</spirit:name>
            <spirit:value>Sun Jun 01 14:00:06 UTC 2025</spirit:value>
          </spirit:parameter>
          <spirit:parameter>
            <spirit:name>outputProductCRC</spirit:name>
            <spirit:value>9:6fa7e43e</spirit:value>
          </spirit:parameter>
        </spirit:parameters>
      </spirit:view>
      <spirit:view>
        <spirit:name>xilinx_verilogsimulationwrapper</spirit:name>
        <spirit:displayName>Verilog Simulation Wrapper</spirit:displayName>
        <spirit:envIdentifier>verilogSource:vivado.xilinx.com:simulation.wrapper</spirit:envIdentifier>
        <spirit:language>verilog</spirit:language>
        <spirit:modelName>bd_2aab_rinsw_6</spirit:modelName>
        <spirit:fileSetRef>
          <spirit:localName>xilinx_verilogsimulationwrapper_view_fileset</spirit:localName>
        </spirit:fileSetRef>
        <spirit:parameters>
          <spirit:parameter>
            <spirit:name>GENtimestamp</spirit:name>
            <spirit:value>Wed Jul 02 11:55:26 UTC 2025</spirit:value>
          </spirit:parameter>
          <spirit:parameter>
            <spirit:name>outputProductCRC</spirit:name>
            <spirit:value>9:6fa7e43e</spirit:value>
          </spirit:parameter>
        </spirit:parameters>
      </spirit:view>
      <spirit:view>
        <spirit:name>xilinx_verilogsynthesis</spirit:name>
        <spirit:displayName>Verilog Synthesis</spirit:displayName>
        <spirit:envIdentifier>verilogSource:vivado.xilinx.com:synthesis</spirit:envIdentifier>
        <spirit:language>verilog</spirit:language>
        <spirit:modelName>sc_switchboard_v1_0_8_top</spirit:modelName>
        <spirit:fileSetRef>
          <spirit:localName>xilinx_verilogsynthesis_xilinx_com_ip_sc_util_1_0__ref_view_fileset</spirit:localName>
        </spirit:fileSetRef>
        <spirit:fileSetRef>
          <spirit:localName>xilinx_verilogsynthesis_view_fileset</spirit:localName>
        </spirit:fileSetRef>
        <spirit:parameters>
          <spirit:parameter>
            <spirit:name>GENtimestamp</spirit:name>
            <spirit:value>Sun Jun 01 14:00:06 UTC 2025</spirit:value>
          </spirit:parameter>
          <spirit:parameter>
            <spirit:name>outputProductCRC</spirit:name>
            <spirit:value>9:16db66c1</spirit:value>
          </spirit:parameter>
        </spirit:parameters>
      </spirit:view>
      <spirit:view>
        <spirit:name>xilinx_verilogsynthesiswrapper</spirit:name>
        <spirit:displayName>Verilog Synthesis Wrapper</spirit:displayName>
        <spirit:envIdentifier>verilogSource:vivado.xilinx.com:synthesis.wrapper</spirit:envIdentifier>
        <spirit:language>verilog</spirit:language>
        <spirit:modelName>bd_2aab_rinsw_6</spirit:modelName>
        <spirit:fileSetRef>
          <spirit:localName>xilinx_verilogsynthesiswrapper_view_fileset</spirit:localName>
        </spirit:fileSetRef>
        <spirit:parameters>
          <spirit:parameter>
            <spirit:name>GENtimestamp</spirit:name>
            <spirit:value>Wed Jul 02 11:55:26 UTC 2025</spirit:value>
          </spirit:parameter>
          <spirit:parameter>
            <spirit:name>outputProductCRC</spirit:name>
            <spirit:value>9:16db66c1</spirit:value>
          </spirit:parameter>
        </spirit:parameters>
      </spirit:view>
    </spirit:views>
    <spirit:ports>
      <spirit:port>
        <spirit:name>aclk</spirit:name>
        <spirit:wire>
          <spirit:direction>in</spirit:direction>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>wire</spirit:typeName>
              <spirit:viewNameRef>xilinx_verilogsynthesis</spirit:viewNameRef>
              <spirit:viewNameRef>xilinx_verilogbehavioralsimulation</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
        </spirit:wire>
      </spirit:port>
      <spirit:port>
        <spirit:name>aclken</spirit:name>
        <spirit:wire>
          <spirit:direction>in</spirit:direction>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>wire</spirit:typeName>
              <spirit:viewNameRef>xilinx_verilogsynthesis</spirit:viewNameRef>
              <spirit:viewNameRef>xilinx_verilogbehavioralsimulation</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
          <spirit:driver>
            <spirit:defaultValue spirit:format="bitString" spirit:bitStringLength="1">1</spirit:defaultValue>
          </spirit:driver>
        </spirit:wire>
      </spirit:port>
      <spirit:port>
        <spirit:name>connectivity</spirit:name>
        <spirit:wire>
          <spirit:direction>in</spirit:direction>
          <spirit:vector>
            <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="((spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_SI&apos;)) * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_MI&apos;))) - 1)">1</spirit:left>
            <spirit:right spirit:format="long">0</spirit:right>
          </spirit:vector>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>wire</spirit:typeName>
              <spirit:viewNameRef>xilinx_verilogsynthesis</spirit:viewNameRef>
              <spirit:viewNameRef>xilinx_verilogbehavioralsimulation</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
          <spirit:driver>
            <spirit:defaultValue spirit:format="bitString" spirit:resolve="dependent" spirit:dependency="(spirit:decode(id(&apos;MODELPARAM_VALUE.C_CONNECTIVITY&apos;)))" spirit:bitStringLength="2">0x3</spirit:defaultValue>
          </spirit:driver>
        </spirit:wire>
        <spirit:vendorExtensions>
          <xilinx:portInfo>
            <xilinx:enablement>
              <xilinx:isEnabled xilinx:id="PORT_ENABLEMENT.connectivity">false</xilinx:isEnabled>
            </xilinx:enablement>
          </xilinx:portInfo>
        </spirit:vendorExtensions>
      </spirit:port>
      <spirit:port>
        <spirit:name>s_sc_send</spirit:name>
        <spirit:wire>
          <spirit:direction>in</spirit:direction>
          <spirit:vector>
            <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="((spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_SI&apos;)) * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_MI&apos;))) - 1)">1</spirit:left>
            <spirit:right spirit:format="long">0</spirit:right>
          </spirit:vector>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>wire</spirit:typeName>
              <spirit:viewNameRef>xilinx_verilogsynthesis</spirit:viewNameRef>
              <spirit:viewNameRef>xilinx_verilogbehavioralsimulation</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
        </spirit:wire>
      </spirit:port>
      <spirit:port>
        <spirit:name>s_sc_req</spirit:name>
        <spirit:wire>
          <spirit:direction>in</spirit:direction>
          <spirit:vector>
            <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="((spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_SI&apos;)) * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_MI&apos;))) - 1)">1</spirit:left>
            <spirit:right spirit:format="long">0</spirit:right>
          </spirit:vector>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>wire</spirit:typeName>
              <spirit:viewNameRef>xilinx_verilogsynthesis</spirit:viewNameRef>
              <spirit:viewNameRef>xilinx_verilogbehavioralsimulation</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
        </spirit:wire>
      </spirit:port>
      <spirit:port>
        <spirit:name>s_sc_info</spirit:name>
        <spirit:wire>
          <spirit:direction>in</spirit:direction>
          <spirit:vector>
            <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="(((spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_SI&apos;)) * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_MI&apos;))) * 1) - 1)">1</spirit:left>
            <spirit:right spirit:format="long">0</spirit:right>
          </spirit:vector>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>wire</spirit:typeName>
              <spirit:viewNameRef>xilinx_verilogsynthesis</spirit:viewNameRef>
              <spirit:viewNameRef>xilinx_verilogbehavioralsimulation</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
        </spirit:wire>
      </spirit:port>
      <spirit:port>
        <spirit:name>s_sc_payld</spirit:name>
        <spirit:wire>
          <spirit:direction>in</spirit:direction>
          <spirit:vector>
            <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="((spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_SI&apos;)) * spirit:decode(id(&apos;MODELPARAM_VALUE.C_PAYLD_WIDTH&apos;))) - 1)">109</spirit:left>
            <spirit:right spirit:format="long">0</spirit:right>
          </spirit:vector>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>wire</spirit:typeName>
              <spirit:viewNameRef>xilinx_verilogsynthesis</spirit:viewNameRef>
              <spirit:viewNameRef>xilinx_verilogbehavioralsimulation</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
        </spirit:wire>
      </spirit:port>
      <spirit:port>
        <spirit:name>s_sc_recv</spirit:name>
        <spirit:wire>
          <spirit:direction>out</spirit:direction>
          <spirit:vector>
            <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="((spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_SI&apos;)) * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_MI&apos;))) - 1)">1</spirit:left>
            <spirit:right spirit:format="long">0</spirit:right>
          </spirit:vector>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>wire</spirit:typeName>
              <spirit:viewNameRef>xilinx_verilogsynthesis</spirit:viewNameRef>
              <spirit:viewNameRef>xilinx_verilogbehavioralsimulation</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
        </spirit:wire>
      </spirit:port>
      <spirit:port>
        <spirit:name>m_sc_recv</spirit:name>
        <spirit:wire>
          <spirit:direction>in</spirit:direction>
          <spirit:vector>
            <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="((spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_MI&apos;)) * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_SI&apos;))) - 1)">1</spirit:left>
            <spirit:right spirit:format="long">0</spirit:right>
          </spirit:vector>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>wire</spirit:typeName>
              <spirit:viewNameRef>xilinx_verilogsynthesis</spirit:viewNameRef>
              <spirit:viewNameRef>xilinx_verilogbehavioralsimulation</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
        </spirit:wire>
      </spirit:port>
      <spirit:port>
        <spirit:name>m_sc_send</spirit:name>
        <spirit:wire>
          <spirit:direction>out</spirit:direction>
          <spirit:vector>
            <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="((spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_MI&apos;)) * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_SI&apos;))) - 1)">1</spirit:left>
            <spirit:right spirit:format="long">0</spirit:right>
          </spirit:vector>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>wire</spirit:typeName>
              <spirit:viewNameRef>xilinx_verilogsynthesis</spirit:viewNameRef>
              <spirit:viewNameRef>xilinx_verilogbehavioralsimulation</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
        </spirit:wire>
      </spirit:port>
      <spirit:port>
        <spirit:name>m_sc_req</spirit:name>
        <spirit:wire>
          <spirit:direction>out</spirit:direction>
          <spirit:vector>
            <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="((spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_MI&apos;)) * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_SI&apos;))) - 1)">1</spirit:left>
            <spirit:right spirit:format="long">0</spirit:right>
          </spirit:vector>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>wire</spirit:typeName>
              <spirit:viewNameRef>xilinx_verilogsynthesis</spirit:viewNameRef>
              <spirit:viewNameRef>xilinx_verilogbehavioralsimulation</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
        </spirit:wire>
      </spirit:port>
      <spirit:port>
        <spirit:name>m_sc_info</spirit:name>
        <spirit:wire>
          <spirit:direction>out</spirit:direction>
          <spirit:vector>
            <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="(((spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_MI&apos;)) * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_SI&apos;))) * 1) - 1)">1</spirit:left>
            <spirit:right spirit:format="long">0</spirit:right>
          </spirit:vector>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>wire</spirit:typeName>
              <spirit:viewNameRef>xilinx_verilogsynthesis</spirit:viewNameRef>
              <spirit:viewNameRef>xilinx_verilogbehavioralsimulation</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
        </spirit:wire>
      </spirit:port>
      <spirit:port>
        <spirit:name>m_sc_payld</spirit:name>
        <spirit:wire>
          <spirit:direction>out</spirit:direction>
          <spirit:vector>
            <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="((spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_MI&apos;)) * spirit:decode(id(&apos;MODELPARAM_VALUE.C_PAYLD_WIDTH&apos;))) - 1)">54</spirit:left>
            <spirit:right spirit:format="long">0</spirit:right>
          </spirit:vector>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>wire</spirit:typeName>
              <spirit:viewNameRef>xilinx_verilogsynthesis</spirit:viewNameRef>
              <spirit:viewNameRef>xilinx_verilogbehavioralsimulation</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
        </spirit:wire>
      </spirit:port>
    </spirit:ports>
    <spirit:modelParameters>
      <spirit:modelParameter xsi:type="spirit:nameValueTypeType" spirit:dataType="integer">
        <spirit:name>C_PAYLD_WIDTH</spirit:name>
        <spirit:displayName>C_PAYLD_WIDTH</spirit:displayName>
        <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_PAYLD_WIDTH" spirit:minimum="1" spirit:maximum="1854" spirit:rangeType="long">55</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="integer">
        <spirit:name>K_MAX_INFO_WIDTH</spirit:name>
        <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.K_MAX_INFO_WIDTH">1</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="integer">
        <spirit:name>C_S_PIPELINES</spirit:name>
        <spirit:displayName>C_S_PIPELINES</spirit:displayName>
        <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_S_PIPELINES" spirit:minimum="0" spirit:maximum="8" spirit:rangeType="long">0</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="integer">
        <spirit:name>C_M_PIPELINES</spirit:name>
        <spirit:displayName>C_M_PIPELINES</spirit:displayName>
        <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_M_PIPELINES" spirit:minimum="0" spirit:maximum="8" spirit:rangeType="long">0</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="integer">
        <spirit:name>C_S_LATENCY</spirit:name>
        <spirit:displayName>C_S_LATENCY</spirit:displayName>
        <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_S_LATENCY" spirit:minimum="0" spirit:maximum="15" spirit:rangeType="long">0</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="integer">
        <spirit:name>C_NUM_SI</spirit:name>
        <spirit:displayName>C_NUM_SI</spirit:displayName>
        <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_NUM_SI" spirit:minimum="0" spirit:maximum="16" spirit:rangeType="long">2</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="integer">
        <spirit:name>C_NUM_MI</spirit:name>
        <spirit:displayName>C_NUM_MI</spirit:displayName>
        <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_NUM_MI" spirit:minimum="0" spirit:maximum="16" spirit:rangeType="long">1</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="integer">
        <spirit:name>C_TESTING_MODE</spirit:name>
        <spirit:displayName>C_TESTING_MODE</spirit:displayName>
        <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_TESTING_MODE" spirit:minimum="0" spirit:maximum="1" spirit:rangeType="long">0</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="bitstring">
        <spirit:name>C_CONNECTIVITY</spirit:name>
        <spirit:displayName>C_CONNECTIVITY</spirit:displayName>
        <spirit:value spirit:format="bitString" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_CONNECTIVITY" spirit:dependency="{(spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_SI&apos;)) * spirit:decode(id(&apos;MODELPARAM_VALUE.C_NUM_MI&apos;))){&quot;1&quot;}}" spirit:bitStringLength="2">&quot;11&quot;</spirit:value>
        <spirit:vendorExtensions>
          <xilinx:parameterInfo>
            <xilinx:enablement>
              <xilinx:isEnabled xilinx:id="MODELPARAM_ENABLEMENT.C_CONNECTIVITY">false</xilinx:isEnabled>
            </xilinx:enablement>
          </xilinx:parameterInfo>
        </spirit:vendorExtensions>
      </spirit:modelParameter>
    </spirit:modelParameters>
  </spirit:model>
  <spirit:choices>
    <spirit:choice>
      <spirit:name>choice_pairs_4873554b</spirit:name>
      <spirit:enumeration spirit:text="false">0</spirit:enumeration>
      <spirit:enumeration spirit:text="true">1</spirit:enumeration>
    </spirit:choice>
  </spirit:choices>
  <spirit:fileSets>
    <spirit:fileSet>
      <spirit:name>xilinx_verilogbehavioralsimulation_xilinx_com_ip_sc_util_1_0__ref_view_fileset</spirit:name>
      <spirit:file>
        <spirit:name>../../../../../ipshared/f0b6/hdl/verilog/sc_util_v1_0_4_constants.vh</spirit:name>
        <spirit:fileType>verilogSource</spirit:fileType>
        <spirit:userFileType>USED_IN_ipstatic</spirit:userFileType>
        <spirit:isIncludeFile>true</spirit:isIncludeFile>
        <spirit:logicalName>smartconnect_v1_0</spirit:logicalName>
      </spirit:file>
      <spirit:file>
        <spirit:name>../../../../../ipshared/f0b6/hdl/verilog/sc_util_v1_0_4_constants_noc.vh</spirit:name>
        <spirit:fileType>verilogSource</spirit:fileType>
        <spirit:userFileType>USED_IN_ipstatic</spirit:userFileType>
        <spirit:isIncludeFile>true</spirit:isIncludeFile>
        <spirit:logicalName>smartconnect_v1_0</spirit:logicalName>
      </spirit:file>
      <spirit:file>
        <spirit:name>../../../../../ipshared/f0b6/hdl/verilog/sc_util_v1_0_4_structs.svh</spirit:name>
        <spirit:fileType>systemVerilogSource</spirit:fileType>
        <spirit:userFileType>USED_IN_ipstatic</spirit:userFileType>
        <spirit:isIncludeFile>true</spirit:isIncludeFile>
        <spirit:logicalName>smartconnect_v1_0</spirit:logicalName>
      </spirit:file>
      <spirit:file>
        <spirit:name>../../../../../ipshared/f0b6/hdl/sc_util_v1_0_vl_rfs.sv</spirit:name>
        <spirit:fileType>systemVerilogSource</spirit:fileType>
        <spirit:userFileType>USED_IN_ipstatic</spirit:userFileType>
        <spirit:logicalName>smartconnect_v1_0</spirit:logicalName>
      </spirit:file>
      <spirit:vendorExtensions>
        <xilinx:subCoreRef>
          <xilinx:componentRef xilinx:vendor="xilinx.com" xilinx:library="ip" xilinx:name="sc_util" xilinx:version="1.0" xilinx:isGenerated="true" xilinx:checksum="4a2c0200">
            <xilinx:mode xilinx:name="copy_mode"/>
          </xilinx:componentRef>
        </xilinx:subCoreRef>
      </spirit:vendorExtensions>
    </spirit:fileSet>
    <spirit:fileSet>
      <spirit:name>xilinx_verilogbehavioralsimulation_view_fileset</spirit:name>
      <spirit:file>
        <spirit:name>../../../../../ipshared/3718/hdl/sc_switchboard_v1_0_vl_rfs.sv</spirit:name>
        <spirit:fileType>systemVerilogSource</spirit:fileType>
        <spirit:userFileType>USED_IN_ipstatic</spirit:userFileType>
        <spirit:logicalName>smartconnect_v1_0</spirit:logicalName>
      </spirit:file>
    </spirit:fileSet>
    <spirit:fileSet>
      <spirit:name>xilinx_verilogsimulationwrapper_view_fileset</spirit:name>
      <spirit:file>
        <spirit:name>sim/bd_2aab_rinsw_6.sv</spirit:name>
        <spirit:fileType>systemVerilogSource</spirit:fileType>
        <spirit:logicalName>xil_defaultlib</spirit:logicalName>
      </spirit:file>
    </spirit:fileSet>
    <spirit:fileSet>
      <spirit:name>xilinx_verilogsynthesis_xilinx_com_ip_sc_util_1_0__ref_view_fileset</spirit:name>
      <spirit:file>
        <spirit:name>../../../../../ipshared/f0b6/hdl/verilog/sc_util_v1_0_4_constants.vh</spirit:name>
        <spirit:fileType>verilogSource</spirit:fileType>
        <spirit:isIncludeFile>true</spirit:isIncludeFile>
        <spirit:logicalName>smartconnect_v1_0</spirit:logicalName>
      </spirit:file>
      <spirit:file>
        <spirit:name>../../../../../ipshared/f0b6/hdl/verilog/sc_util_v1_0_4_constants_noc.vh</spirit:name>
        <spirit:fileType>verilogSource</spirit:fileType>
        <spirit:isIncludeFile>true</spirit:isIncludeFile>
        <spirit:logicalName>smartconnect_v1_0</spirit:logicalName>
      </spirit:file>
      <spirit:file>
        <spirit:name>../../../../../ipshared/f0b6/hdl/verilog/sc_util_v1_0_4_structs.svh</spirit:name>
        <spirit:fileType>systemVerilogSource</spirit:fileType>
        <spirit:isIncludeFile>true</spirit:isIncludeFile>
        <spirit:logicalName>smartconnect_v1_0</spirit:logicalName>
      </spirit:file>
      <spirit:file>
        <spirit:name>../../../../../ipshared/f0b6/hdl/sc_util_v1_0_vl_rfs.sv</spirit:name>
        <spirit:fileType>systemVerilogSource</spirit:fileType>
        <spirit:logicalName>smartconnect_v1_0</spirit:logicalName>
      </spirit:file>
      <spirit:vendorExtensions>
        <xilinx:subCoreRef>
          <xilinx:componentRef xilinx:vendor="xilinx.com" xilinx:library="ip" xilinx:name="sc_util" xilinx:version="1.0" xilinx:isGenerated="true" xilinx:checksum="4a2c0200">
            <xilinx:mode xilinx:name="copy_mode"/>
          </xilinx:componentRef>
        </xilinx:subCoreRef>
      </spirit:vendorExtensions>
    </spirit:fileSet>
    <spirit:fileSet>
      <spirit:name>xilinx_verilogsynthesis_view_fileset</spirit:name>
      <spirit:file>
        <spirit:name>bd_2aab_rinsw_6_ooc.xdc</spirit:name>
        <spirit:userFileType>xdc</spirit:userFileType>
        <spirit:userFileType>USED_IN_implementation</spirit:userFileType>
        <spirit:userFileType>USED_IN_out_of_context</spirit:userFileType>
        <spirit:userFileType>USED_IN_synthesis</spirit:userFileType>
      </spirit:file>
      <spirit:file>
        <spirit:name>../../../../../ipshared/3718/hdl/sc_switchboard_v1_0_vl_rfs.sv</spirit:name>
        <spirit:fileType>systemVerilogSource</spirit:fileType>
        <spirit:logicalName>smartconnect_v1_0</spirit:logicalName>
      </spirit:file>
    </spirit:fileSet>
    <spirit:fileSet>
      <spirit:name>xilinx_verilogsynthesiswrapper_view_fileset</spirit:name>
      <spirit:file>
        <spirit:name>synth/bd_2aab_rinsw_6.sv</spirit:name>
        <spirit:fileType>systemVerilogSource</spirit:fileType>
        <spirit:logicalName>xil_defaultlib</spirit:logicalName>
      </spirit:file>
    </spirit:fileSet>
  </spirit:fileSets>
  <spirit:description>SmartConnect Switchboard (internal)</spirit:description>
  <spirit:parameters>
    <spirit:parameter>
      <spirit:name>S_PIPELINES</spirit:name>
      <spirit:displayName>S_PIPELINES</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.S_PIPELINES" spirit:order="2" spirit:minimum="0" spirit:maximum="8" spirit:rangeType="long">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M_PIPELINES</spirit:name>
      <spirit:displayName>M_PIPELINES</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M_PIPELINES" spirit:order="3" spirit:minimum="0" spirit:maximum="8" spirit:rangeType="long">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>S_LATENCY</spirit:name>
      <spirit:displayName>S_LATENCY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.S_LATENCY" spirit:order="4" spirit:minimum="0" spirit:maximum="15" spirit:rangeType="long">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>NUM_SI</spirit:name>
      <spirit:displayName>NUM_SI</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.NUM_SI" spirit:order="5" spirit:minimum="1" spirit:maximum="16" spirit:rangeType="long">2</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>NUM_MI</spirit:name>
      <spirit:displayName>NUM_MI</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.NUM_MI" spirit:order="6" spirit:minimum="1" spirit:maximum="16" spirit:rangeType="long">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>TESTING_MODE</spirit:name>
      <spirit:displayName>TESTING_MODE</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.TESTING_MODE" spirit:order="7" spirit:minimum="0" spirit:maximum="1" spirit:rangeType="long">0</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>PAYLD_WIDTH</spirit:name>
      <spirit:displayName>PAYLD_WIDTH</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.PAYLD_WIDTH" spirit:order="8" spirit:minimum="0" spirit:maximum="1854" spirit:rangeType="long">55</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M00_S00_CONNECTIVITY</spirit:name>
      <spirit:displayName>My M00_S00_CONNECTIVITY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M00_S00_CONNECTIVITY" spirit:choiceRef="choice_pairs_4873554b">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M00_S01_CONNECTIVITY</spirit:name>
      <spirit:displayName>My M00_S01_CONNECTIVITY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M00_S01_CONNECTIVITY" spirit:choiceRef="choice_pairs_4873554b">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M00_S02_CONNECTIVITY</spirit:name>
      <spirit:displayName>My M00_S02_CONNECTIVITY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M00_S02_CONNECTIVITY" spirit:choiceRef="choice_pairs_4873554b">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M00_S03_CONNECTIVITY</spirit:name>
      <spirit:displayName>My M00_S03_CONNECTIVITY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M00_S03_CONNECTIVITY" spirit:choiceRef="choice_pairs_4873554b">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M00_S04_CONNECTIVITY</spirit:name>
      <spirit:displayName>My M00_S04_CONNECTIVITY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M00_S04_CONNECTIVITY" spirit:choiceRef="choice_pairs_4873554b">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M00_S05_CONNECTIVITY</spirit:name>
      <spirit:displayName>My M00_S05_CONNECTIVITY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M00_S05_CONNECTIVITY" spirit:choiceRef="choice_pairs_4873554b">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M00_S06_CONNECTIVITY</spirit:name>
      <spirit:displayName>My M00_S06_CONNECTIVITY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M00_S06_CONNECTIVITY" spirit:choiceRef="choice_pairs_4873554b">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M00_S07_CONNECTIVITY</spirit:name>
      <spirit:displayName>My M00_S07_CONNECTIVITY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M00_S07_CONNECTIVITY" spirit:choiceRef="choice_pairs_4873554b">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M00_S08_CONNECTIVITY</spirit:name>
      <spirit:displayName>My M00_S08_CONNECTIVITY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M00_S08_CONNECTIVITY" spirit:choiceRef="choice_pairs_4873554b">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M00_S09_CONNECTIVITY</spirit:name>
      <spirit:displayName>My M00_S09_CONNECTIVITY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M00_S09_CONNECTIVITY" spirit:choiceRef="choice_pairs_4873554b">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M00_S10_CONNECTIVITY</spirit:name>
      <spirit:displayName>My M00_S10_CONNECTIVITY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M00_S10_CONNECTIVITY" spirit:choiceRef="choice_pairs_4873554b">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M00_S11_CONNECTIVITY</spirit:name>
      <spirit:displayName>My M00_S11_CONNECTIVITY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M00_S11_CONNECTIVITY" spirit:choiceRef="choice_pairs_4873554b">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M00_S12_CONNECTIVITY</spirit:name>
      <spirit:displayName>My M00_S12_CONNECTIVITY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M00_S12_CONNECTIVITY" spirit:choiceRef="choice_pairs_4873554b">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M00_S13_CONNECTIVITY</spirit:name>
      <spirit:displayName>My M00_S13_CONNECTIVITY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M00_S13_CONNECTIVITY" spirit:choiceRef="choice_pairs_4873554b">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M00_S14_CONNECTIVITY</spirit:name>
      <spirit:displayName>My M00_S14_CONNECTIVITY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M00_S14_CONNECTIVITY" spirit:choiceRef="choice_pairs_4873554b">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M00_S15_CONNECTIVITY</spirit:name>
      <spirit:displayName>My M00_S15_CONNECTIVITY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M00_S15_CONNECTIVITY" spirit:choiceRef="choice_pairs_4873554b" spirit:order="501">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M01_S00_CONNECTIVITY</spirit:name>
      <spirit:displayName>My M01_S00_CONNECTIVITY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M01_S00_CONNECTIVITY" spirit:choiceRef="choice_pairs_4873554b">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M01_S01_CONNECTIVITY</spirit:name>
      <spirit:displayName>My M01_S01_CONNECTIVITY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M01_S01_CONNECTIVITY" spirit:choiceRef="choice_pairs_4873554b">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M01_S02_CONNECTIVITY</spirit:name>
      <spirit:displayName>My M01_S02_CONNECTIVITY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M01_S02_CONNECTIVITY" spirit:choiceRef="choice_pairs_4873554b">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M01_S03_CONNECTIVITY</spirit:name>
      <spirit:displayName>My M01_S03_CONNECTIVITY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M01_S03_CONNECTIVITY" spirit:choiceRef="choice_pairs_4873554b">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M01_S04_CONNECTIVITY</spirit:name>
      <spirit:displayName>My M01_S04_CONNECTIVITY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M01_S04_CONNECTIVITY" spirit:choiceRef="choice_pairs_4873554b">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M01_S05_CONNECTIVITY</spirit:name>
      <spirit:displayName>My M01_S05_CONNECTIVITY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M01_S05_CONNECTIVITY" spirit:choiceRef="choice_pairs_4873554b">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M01_S06_CONNECTIVITY</spirit:name>
      <spirit:displayName>My M01_S06_CONNECTIVITY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M01_S06_CONNECTIVITY" spirit:choiceRef="choice_pairs_4873554b">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M01_S07_CONNECTIVITY</spirit:name>
      <spirit:displayName>My M01_S07_CONNECTIVITY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M01_S07_CONNECTIVITY" spirit:choiceRef="choice_pairs_4873554b">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M01_S08_CONNECTIVITY</spirit:name>
      <spirit:displayName>My M01_S08_CONNECTIVITY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M01_S08_CONNECTIVITY" spirit:choiceRef="choice_pairs_4873554b">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M01_S09_CONNECTIVITY</spirit:name>
      <spirit:displayName>My M01_S09_CONNECTIVITY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M01_S09_CONNECTIVITY" spirit:choiceRef="choice_pairs_4873554b">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M01_S10_CONNECTIVITY</spirit:name>
      <spirit:displayName>My M01_S10_CONNECTIVITY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M01_S10_CONNECTIVITY" spirit:choiceRef="choice_pairs_4873554b">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M01_S11_CONNECTIVITY</spirit:name>
      <spirit:displayName>My M01_S11_CONNECTIVITY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M01_S11_CONNECTIVITY" spirit:choiceRef="choice_pairs_4873554b">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M01_S12_CONNECTIVITY</spirit:name>
      <spirit:displayName>My M01_S12_CONNECTIVITY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M01_S12_CONNECTIVITY" spirit:choiceRef="choice_pairs_4873554b">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M01_S13_CONNECTIVITY</spirit:name>
      <spirit:displayName>My M01_S13_CONNECTIVITY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M01_S13_CONNECTIVITY" spirit:choiceRef="choice_pairs_4873554b">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M01_S14_CONNECTIVITY</spirit:name>
      <spirit:displayName>My M01_S14_CONNECTIVITY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M01_S14_CONNECTIVITY" spirit:choiceRef="choice_pairs_4873554b">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M01_S15_CONNECTIVITY</spirit:name>
      <spirit:displayName>My M01_S15_CONNECTIVITY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M01_S15_CONNECTIVITY" spirit:choiceRef="choice_pairs_4873554b" spirit:order="502">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M02_S00_CONNECTIVITY</spirit:name>
      <spirit:displayName>My M02_S00_CONNECTIVITY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M02_S00_CONNECTIVITY" spirit:choiceRef="choice_pairs_4873554b">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M02_S01_CONNECTIVITY</spirit:name>
      <spirit:displayName>My M02_S01_CONNECTIVITY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M02_S01_CONNECTIVITY" spirit:choiceRef="choice_pairs_4873554b">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M02_S02_CONNECTIVITY</spirit:name>
      <spirit:displayName>My M02_S02_CONNECTIVITY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M02_S02_CONNECTIVITY" spirit:choiceRef="choice_pairs_4873554b">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M02_S03_CONNECTIVITY</spirit:name>
      <spirit:displayName>My M02_S03_CONNECTIVITY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M02_S03_CONNECTIVITY" spirit:choiceRef="choice_pairs_4873554b">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M02_S04_CONNECTIVITY</spirit:name>
      <spirit:displayName>My M02_S04_CONNECTIVITY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M02_S04_CONNECTIVITY" spirit:choiceRef="choice_pairs_4873554b">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M02_S05_CONNECTIVITY</spirit:name>
      <spirit:displayName>My M02_S05_CONNECTIVITY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M02_S05_CONNECTIVITY" spirit:choiceRef="choice_pairs_4873554b">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M02_S06_CONNECTIVITY</spirit:name>
      <spirit:displayName>My M02_S06_CONNECTIVITY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M02_S06_CONNECTIVITY" spirit:choiceRef="choice_pairs_4873554b">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M02_S07_CONNECTIVITY</spirit:name>
      <spirit:displayName>My M02_S07_CONNECTIVITY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M02_S07_CONNECTIVITY" spirit:choiceRef="choice_pairs_4873554b">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M02_S08_CONNECTIVITY</spirit:name>
      <spirit:displayName>My M02_S08_CONNECTIVITY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M02_S08_CONNECTIVITY" spirit:choiceRef="choice_pairs_4873554b">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M02_S09_CONNECTIVITY</spirit:name>
      <spirit:displayName>My M02_S09_CONNECTIVITY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M02_S09_CONNECTIVITY" spirit:choiceRef="choice_pairs_4873554b">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M02_S10_CONNECTIVITY</spirit:name>
      <spirit:displayName>My M02_S10_CONNECTIVITY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M02_S10_CONNECTIVITY" spirit:choiceRef="choice_pairs_4873554b">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M02_S11_CONNECTIVITY</spirit:name>
      <spirit:displayName>My M02_S11_CONNECTIVITY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M02_S11_CONNECTIVITY" spirit:choiceRef="choice_pairs_4873554b">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M02_S12_CONNECTIVITY</spirit:name>
      <spirit:displayName>My M02_S12_CONNECTIVITY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M02_S12_CONNECTIVITY" spirit:choiceRef="choice_pairs_4873554b">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M02_S13_CONNECTIVITY</spirit:name>
      <spirit:displayName>My M02_S13_CONNECTIVITY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M02_S13_CONNECTIVITY" spirit:choiceRef="choice_pairs_4873554b">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M02_S14_CONNECTIVITY</spirit:name>
      <spirit:displayName>My M02_S14_CONNECTIVITY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M02_S14_CONNECTIVITY" spirit:choiceRef="choice_pairs_4873554b">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M02_S15_CONNECTIVITY</spirit:name>
      <spirit:displayName>My M02_S15_CONNECTIVITY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M02_S15_CONNECTIVITY" spirit:choiceRef="choice_pairs_4873554b" spirit:order="503">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M03_S00_CONNECTIVITY</spirit:name>
      <spirit:displayName>My M03_S00_CONNECTIVITY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M03_S00_CONNECTIVITY" spirit:choiceRef="choice_pairs_4873554b">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M03_S01_CONNECTIVITY</spirit:name>
      <spirit:displayName>My M03_S01_CONNECTIVITY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M03_S01_CONNECTIVITY" spirit:choiceRef="choice_pairs_4873554b">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M03_S02_CONNECTIVITY</spirit:name>
      <spirit:displayName>My M03_S02_CONNECTIVITY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M03_S02_CONNECTIVITY" spirit:choiceRef="choice_pairs_4873554b">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M03_S03_CONNECTIVITY</spirit:name>
      <spirit:displayName>My M03_S03_CONNECTIVITY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M03_S03_CONNECTIVITY" spirit:choiceRef="choice_pairs_4873554b">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M03_S04_CONNECTIVITY</spirit:name>
      <spirit:displayName>My M03_S04_CONNECTIVITY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M03_S04_CONNECTIVITY" spirit:choiceRef="choice_pairs_4873554b">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M03_S05_CONNECTIVITY</spirit:name>
      <spirit:displayName>My M03_S05_CONNECTIVITY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M03_S05_CONNECTIVITY" spirit:choiceRef="choice_pairs_4873554b">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M03_S06_CONNECTIVITY</spirit:name>
      <spirit:displayName>My M03_S06_CONNECTIVITY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M03_S06_CONNECTIVITY" spirit:choiceRef="choice_pairs_4873554b">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M03_S07_CONNECTIVITY</spirit:name>
      <spirit:displayName>My M03_S07_CONNECTIVITY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M03_S07_CONNECTIVITY" spirit:choiceRef="choice_pairs_4873554b">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M03_S08_CONNECTIVITY</spirit:name>
      <spirit:displayName>My M03_S08_CONNECTIVITY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M03_S08_CONNECTIVITY" spirit:choiceRef="choice_pairs_4873554b">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M03_S09_CONNECTIVITY</spirit:name>
      <spirit:displayName>My M03_S09_CONNECTIVITY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M03_S09_CONNECTIVITY" spirit:choiceRef="choice_pairs_4873554b">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M03_S10_CONNECTIVITY</spirit:name>
      <spirit:displayName>My M03_S10_CONNECTIVITY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M03_S10_CONNECTIVITY" spirit:choiceRef="choice_pairs_4873554b">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M03_S11_CONNECTIVITY</spirit:name>
      <spirit:displayName>My M03_S11_CONNECTIVITY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M03_S11_CONNECTIVITY" spirit:choiceRef="choice_pairs_4873554b">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M03_S12_CONNECTIVITY</spirit:name>
      <spirit:displayName>My M03_S12_CONNECTIVITY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M03_S12_CONNECTIVITY" spirit:choiceRef="choice_pairs_4873554b">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M03_S13_CONNECTIVITY</spirit:name>
      <spirit:displayName>My M03_S13_CONNECTIVITY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M03_S13_CONNECTIVITY" spirit:choiceRef="choice_pairs_4873554b">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M03_S14_CONNECTIVITY</spirit:name>
      <spirit:displayName>My M03_S14_CONNECTIVITY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M03_S14_CONNECTIVITY" spirit:choiceRef="choice_pairs_4873554b">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M03_S15_CONNECTIVITY</spirit:name>
      <spirit:displayName>My M03_S15_CONNECTIVITY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M03_S15_CONNECTIVITY" spirit:choiceRef="choice_pairs_4873554b" spirit:order="504">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M04_S00_CONNECTIVITY</spirit:name>
      <spirit:displayName>My M04_S00_CONNECTIVITY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M04_S00_CONNECTIVITY" spirit:choiceRef="choice_pairs_4873554b">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M04_S01_CONNECTIVITY</spirit:name>
      <spirit:displayName>My M04_S01_CONNECTIVITY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M04_S01_CONNECTIVITY" spirit:choiceRef="choice_pairs_4873554b">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M04_S02_CONNECTIVITY</spirit:name>
      <spirit:displayName>My M04_S02_CONNECTIVITY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M04_S02_CONNECTIVITY" spirit:choiceRef="choice_pairs_4873554b">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M04_S03_CONNECTIVITY</spirit:name>
      <spirit:displayName>My M04_S03_CONNECTIVITY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M04_S03_CONNECTIVITY" spirit:choiceRef="choice_pairs_4873554b">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M04_S04_CONNECTIVITY</spirit:name>
      <spirit:displayName>My M04_S04_CONNECTIVITY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M04_S04_CONNECTIVITY" spirit:choiceRef="choice_pairs_4873554b">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M04_S05_CONNECTIVITY</spirit:name>
      <spirit:displayName>My M04_S05_CONNECTIVITY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M04_S05_CONNECTIVITY" spirit:choiceRef="choice_pairs_4873554b">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M04_S06_CONNECTIVITY</spirit:name>
      <spirit:displayName>My M04_S06_CONNECTIVITY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M04_S06_CONNECTIVITY" spirit:choiceRef="choice_pairs_4873554b">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M04_S07_CONNECTIVITY</spirit:name>
      <spirit:displayName>My M04_S07_CONNECTIVITY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M04_S07_CONNECTIVITY" spirit:choiceRef="choice_pairs_4873554b">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M04_S08_CONNECTIVITY</spirit:name>
      <spirit:displayName>My M04_S08_CONNECTIVITY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M04_S08_CONNECTIVITY" spirit:choiceRef="choice_pairs_4873554b">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M04_S09_CONNECTIVITY</spirit:name>
      <spirit:displayName>My M04_S09_CONNECTIVITY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M04_S09_CONNECTIVITY" spirit:choiceRef="choice_pairs_4873554b">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M04_S10_CONNECTIVITY</spirit:name>
      <spirit:displayName>My M04_S10_CONNECTIVITY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M04_S10_CONNECTIVITY" spirit:choiceRef="choice_pairs_4873554b">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M04_S11_CONNECTIVITY</spirit:name>
      <spirit:displayName>My M04_S11_CONNECTIVITY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M04_S11_CONNECTIVITY" spirit:choiceRef="choice_pairs_4873554b">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M04_S12_CONNECTIVITY</spirit:name>
      <spirit:displayName>My M04_S12_CONNECTIVITY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M04_S12_CONNECTIVITY" spirit:choiceRef="choice_pairs_4873554b">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M04_S13_CONNECTIVITY</spirit:name>
      <spirit:displayName>My M04_S13_CONNECTIVITY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M04_S13_CONNECTIVITY" spirit:choiceRef="choice_pairs_4873554b">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M04_S14_CONNECTIVITY</spirit:name>
      <spirit:displayName>My M04_S14_CONNECTIVITY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M04_S14_CONNECTIVITY" spirit:choiceRef="choice_pairs_4873554b">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M04_S15_CONNECTIVITY</spirit:name>
      <spirit:displayName>My M04_S15_CONNECTIVITY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M04_S15_CONNECTIVITY" spirit:choiceRef="choice_pairs_4873554b" spirit:order="505">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M05_S00_CONNECTIVITY</spirit:name>
      <spirit:displayName>My M05_S00_CONNECTIVITY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M05_S00_CONNECTIVITY" spirit:choiceRef="choice_pairs_4873554b">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M05_S01_CONNECTIVITY</spirit:name>
      <spirit:displayName>My M05_S01_CONNECTIVITY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M05_S01_CONNECTIVITY" spirit:choiceRef="choice_pairs_4873554b">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M05_S02_CONNECTIVITY</spirit:name>
      <spirit:displayName>My M05_S02_CONNECTIVITY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M05_S02_CONNECTIVITY" spirit:choiceRef="choice_pairs_4873554b">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M05_S03_CONNECTIVITY</spirit:name>
      <spirit:displayName>My M05_S03_CONNECTIVITY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M05_S03_CONNECTIVITY" spirit:choiceRef="choice_pairs_4873554b">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M05_S04_CONNECTIVITY</spirit:name>
      <spirit:displayName>My M05_S04_CONNECTIVITY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M05_S04_CONNECTIVITY" spirit:choiceRef="choice_pairs_4873554b">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M05_S05_CONNECTIVITY</spirit:name>
      <spirit:displayName>My M05_S05_CONNECTIVITY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M05_S05_CONNECTIVITY" spirit:choiceRef="choice_pairs_4873554b">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M05_S06_CONNECTIVITY</spirit:name>
      <spirit:displayName>My M05_S06_CONNECTIVITY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M05_S06_CONNECTIVITY" spirit:choiceRef="choice_pairs_4873554b">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M05_S07_CONNECTIVITY</spirit:name>
      <spirit:displayName>My M05_S07_CONNECTIVITY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M05_S07_CONNECTIVITY" spirit:choiceRef="choice_pairs_4873554b">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M05_S08_CONNECTIVITY</spirit:name>
      <spirit:displayName>My M05_S08_CONNECTIVITY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M05_S08_CONNECTIVITY" spirit:choiceRef="choice_pairs_4873554b">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M05_S09_CONNECTIVITY</spirit:name>
      <spirit:displayName>My M05_S09_CONNECTIVITY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M05_S09_CONNECTIVITY" spirit:choiceRef="choice_pairs_4873554b">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M05_S10_CONNECTIVITY</spirit:name>
      <spirit:displayName>My M05_S10_CONNECTIVITY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M05_S10_CONNECTIVITY" spirit:choiceRef="choice_pairs_4873554b">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M05_S11_CONNECTIVITY</spirit:name>
      <spirit:displayName>My M05_S11_CONNECTIVITY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M05_S11_CONNECTIVITY" spirit:choiceRef="choice_pairs_4873554b">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M05_S12_CONNECTIVITY</spirit:name>
      <spirit:displayName>My M05_S12_CONNECTIVITY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M05_S12_CONNECTIVITY" spirit:choiceRef="choice_pairs_4873554b">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M05_S13_CONNECTIVITY</spirit:name>
      <spirit:displayName>My M05_S13_CONNECTIVITY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M05_S13_CONNECTIVITY" spirit:choiceRef="choice_pairs_4873554b">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M05_S14_CONNECTIVITY</spirit:name>
      <spirit:displayName>My M05_S14_CONNECTIVITY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M05_S14_CONNECTIVITY" spirit:choiceRef="choice_pairs_4873554b">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M05_S15_CONNECTIVITY</spirit:name>
      <spirit:displayName>My M05_S15_CONNECTIVITY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M05_S15_CONNECTIVITY" spirit:choiceRef="choice_pairs_4873554b" spirit:order="506">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M06_S00_CONNECTIVITY</spirit:name>
      <spirit:displayName>My M06_S00_CONNECTIVITY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M06_S00_CONNECTIVITY" spirit:choiceRef="choice_pairs_4873554b">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M06_S01_CONNECTIVITY</spirit:name>
      <spirit:displayName>My M06_S01_CONNECTIVITY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M06_S01_CONNECTIVITY" spirit:choiceRef="choice_pairs_4873554b">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M06_S02_CONNECTIVITY</spirit:name>
      <spirit:displayName>My M06_S02_CONNECTIVITY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M06_S02_CONNECTIVITY" spirit:choiceRef="choice_pairs_4873554b">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M06_S03_CONNECTIVITY</spirit:name>
      <spirit:displayName>My M06_S03_CONNECTIVITY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M06_S03_CONNECTIVITY" spirit:choiceRef="choice_pairs_4873554b">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M06_S04_CONNECTIVITY</spirit:name>
      <spirit:displayName>My M06_S04_CONNECTIVITY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M06_S04_CONNECTIVITY" spirit:choiceRef="choice_pairs_4873554b">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M06_S05_CONNECTIVITY</spirit:name>
      <spirit:displayName>My M06_S05_CONNECTIVITY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M06_S05_CONNECTIVITY" spirit:choiceRef="choice_pairs_4873554b">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M06_S06_CONNECTIVITY</spirit:name>
      <spirit:displayName>My M06_S06_CONNECTIVITY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M06_S06_CONNECTIVITY" spirit:choiceRef="choice_pairs_4873554b">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M06_S07_CONNECTIVITY</spirit:name>
      <spirit:displayName>My M06_S07_CONNECTIVITY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M06_S07_CONNECTIVITY" spirit:choiceRef="choice_pairs_4873554b">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M06_S08_CONNECTIVITY</spirit:name>
      <spirit:displayName>My M06_S08_CONNECTIVITY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M06_S08_CONNECTIVITY" spirit:choiceRef="choice_pairs_4873554b">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M06_S09_CONNECTIVITY</spirit:name>
      <spirit:displayName>My M06_S09_CONNECTIVITY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M06_S09_CONNECTIVITY" spirit:choiceRef="choice_pairs_4873554b">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M06_S10_CONNECTIVITY</spirit:name>
      <spirit:displayName>My M06_S10_CONNECTIVITY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M06_S10_CONNECTIVITY" spirit:choiceRef="choice_pairs_4873554b">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M06_S11_CONNECTIVITY</spirit:name>
      <spirit:displayName>My M06_S11_CONNECTIVITY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M06_S11_CONNECTIVITY" spirit:choiceRef="choice_pairs_4873554b">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M06_S12_CONNECTIVITY</spirit:name>
      <spirit:displayName>My M06_S12_CONNECTIVITY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M06_S12_CONNECTIVITY" spirit:choiceRef="choice_pairs_4873554b">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M06_S13_CONNECTIVITY</spirit:name>
      <spirit:displayName>My M06_S13_CONNECTIVITY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M06_S13_CONNECTIVITY" spirit:choiceRef="choice_pairs_4873554b">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M06_S14_CONNECTIVITY</spirit:name>
      <spirit:displayName>My M06_S14_CONNECTIVITY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M06_S14_CONNECTIVITY" spirit:choiceRef="choice_pairs_4873554b">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M06_S15_CONNECTIVITY</spirit:name>
      <spirit:displayName>My M06_S15_CONNECTIVITY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M06_S15_CONNECTIVITY" spirit:choiceRef="choice_pairs_4873554b" spirit:order="507">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M07_S00_CONNECTIVITY</spirit:name>
      <spirit:displayName>My M07_S00_CONNECTIVITY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M07_S00_CONNECTIVITY" spirit:choiceRef="choice_pairs_4873554b">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M07_S01_CONNECTIVITY</spirit:name>
      <spirit:displayName>My M07_S01_CONNECTIVITY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M07_S01_CONNECTIVITY" spirit:choiceRef="choice_pairs_4873554b">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M07_S02_CONNECTIVITY</spirit:name>
      <spirit:displayName>My M07_S02_CONNECTIVITY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M07_S02_CONNECTIVITY" spirit:choiceRef="choice_pairs_4873554b">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M07_S03_CONNECTIVITY</spirit:name>
      <spirit:displayName>My M07_S03_CONNECTIVITY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M07_S03_CONNECTIVITY" spirit:choiceRef="choice_pairs_4873554b">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M07_S04_CONNECTIVITY</spirit:name>
      <spirit:displayName>My M07_S04_CONNECTIVITY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M07_S04_CONNECTIVITY" spirit:choiceRef="choice_pairs_4873554b">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M07_S05_CONNECTIVITY</spirit:name>
      <spirit:displayName>My M07_S05_CONNECTIVITY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M07_S05_CONNECTIVITY" spirit:choiceRef="choice_pairs_4873554b">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M07_S06_CONNECTIVITY</spirit:name>
      <spirit:displayName>My M07_S06_CONNECTIVITY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M07_S06_CONNECTIVITY" spirit:choiceRef="choice_pairs_4873554b">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M07_S07_CONNECTIVITY</spirit:name>
      <spirit:displayName>My M07_S07_CONNECTIVITY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M07_S07_CONNECTIVITY" spirit:choiceRef="choice_pairs_4873554b">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M07_S08_CONNECTIVITY</spirit:name>
      <spirit:displayName>My M07_S08_CONNECTIVITY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M07_S08_CONNECTIVITY" spirit:choiceRef="choice_pairs_4873554b">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M07_S09_CONNECTIVITY</spirit:name>
      <spirit:displayName>My M07_S09_CONNECTIVITY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M07_S09_CONNECTIVITY" spirit:choiceRef="choice_pairs_4873554b">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M07_S10_CONNECTIVITY</spirit:name>
      <spirit:displayName>My M07_S10_CONNECTIVITY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M07_S10_CONNECTIVITY" spirit:choiceRef="choice_pairs_4873554b">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M07_S11_CONNECTIVITY</spirit:name>
      <spirit:displayName>My M07_S11_CONNECTIVITY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M07_S11_CONNECTIVITY" spirit:choiceRef="choice_pairs_4873554b">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M07_S12_CONNECTIVITY</spirit:name>
      <spirit:displayName>My M07_S12_CONNECTIVITY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M07_S12_CONNECTIVITY" spirit:choiceRef="choice_pairs_4873554b">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M07_S13_CONNECTIVITY</spirit:name>
      <spirit:displayName>My M07_S13_CONNECTIVITY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M07_S13_CONNECTIVITY" spirit:choiceRef="choice_pairs_4873554b">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M07_S14_CONNECTIVITY</spirit:name>
      <spirit:displayName>My M07_S14_CONNECTIVITY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M07_S14_CONNECTIVITY" spirit:choiceRef="choice_pairs_4873554b">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M07_S15_CONNECTIVITY</spirit:name>
      <spirit:displayName>My M07_S15_CONNECTIVITY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M07_S15_CONNECTIVITY" spirit:choiceRef="choice_pairs_4873554b" spirit:order="508">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M08_S00_CONNECTIVITY</spirit:name>
      <spirit:displayName>My M08_S00_CONNECTIVITY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M08_S00_CONNECTIVITY" spirit:choiceRef="choice_pairs_4873554b">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M08_S01_CONNECTIVITY</spirit:name>
      <spirit:displayName>My M08_S01_CONNECTIVITY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M08_S01_CONNECTIVITY" spirit:choiceRef="choice_pairs_4873554b">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M08_S02_CONNECTIVITY</spirit:name>
      <spirit:displayName>My M08_S02_CONNECTIVITY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M08_S02_CONNECTIVITY" spirit:choiceRef="choice_pairs_4873554b">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M08_S03_CONNECTIVITY</spirit:name>
      <spirit:displayName>My M08_S03_CONNECTIVITY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M08_S03_CONNECTIVITY" spirit:choiceRef="choice_pairs_4873554b">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M08_S04_CONNECTIVITY</spirit:name>
      <spirit:displayName>My M08_S04_CONNECTIVITY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M08_S04_CONNECTIVITY" spirit:choiceRef="choice_pairs_4873554b">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M08_S05_CONNECTIVITY</spirit:name>
      <spirit:displayName>My M08_S05_CONNECTIVITY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M08_S05_CONNECTIVITY" spirit:choiceRef="choice_pairs_4873554b">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M08_S06_CONNECTIVITY</spirit:name>
      <spirit:displayName>My M08_S06_CONNECTIVITY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M08_S06_CONNECTIVITY" spirit:choiceRef="choice_pairs_4873554b">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M08_S07_CONNECTIVITY</spirit:name>
      <spirit:displayName>My M08_S07_CONNECTIVITY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M08_S07_CONNECTIVITY" spirit:choiceRef="choice_pairs_4873554b">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M08_S08_CONNECTIVITY</spirit:name>
      <spirit:displayName>My M08_S08_CONNECTIVITY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M08_S08_CONNECTIVITY" spirit:choiceRef="choice_pairs_4873554b">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M08_S09_CONNECTIVITY</spirit:name>
      <spirit:displayName>My M08_S09_CONNECTIVITY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M08_S09_CONNECTIVITY" spirit:choiceRef="choice_pairs_4873554b">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M08_S10_CONNECTIVITY</spirit:name>
      <spirit:displayName>My M08_S10_CONNECTIVITY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M08_S10_CONNECTIVITY" spirit:choiceRef="choice_pairs_4873554b">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M08_S11_CONNECTIVITY</spirit:name>
      <spirit:displayName>My M08_S11_CONNECTIVITY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M08_S11_CONNECTIVITY" spirit:choiceRef="choice_pairs_4873554b">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M08_S12_CONNECTIVITY</spirit:name>
      <spirit:displayName>My M08_S12_CONNECTIVITY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M08_S12_CONNECTIVITY" spirit:choiceRef="choice_pairs_4873554b">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M08_S13_CONNECTIVITY</spirit:name>
      <spirit:displayName>My M08_S13_CONNECTIVITY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M08_S13_CONNECTIVITY" spirit:choiceRef="choice_pairs_4873554b">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M08_S14_CONNECTIVITY</spirit:name>
      <spirit:displayName>My M08_S14_CONNECTIVITY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M08_S14_CONNECTIVITY" spirit:choiceRef="choice_pairs_4873554b">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M08_S15_CONNECTIVITY</spirit:name>
      <spirit:displayName>My M08_S15_CONNECTIVITY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M08_S15_CONNECTIVITY" spirit:choiceRef="choice_pairs_4873554b" spirit:order="509">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M09_S00_CONNECTIVITY</spirit:name>
      <spirit:displayName>My M09_S00_CONNECTIVITY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M09_S00_CONNECTIVITY" spirit:choiceRef="choice_pairs_4873554b">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M09_S01_CONNECTIVITY</spirit:name>
      <spirit:displayName>My M09_S01_CONNECTIVITY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M09_S01_CONNECTIVITY" spirit:choiceRef="choice_pairs_4873554b">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M09_S02_CONNECTIVITY</spirit:name>
      <spirit:displayName>My M09_S02_CONNECTIVITY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M09_S02_CONNECTIVITY" spirit:choiceRef="choice_pairs_4873554b">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M09_S03_CONNECTIVITY</spirit:name>
      <spirit:displayName>My M09_S03_CONNECTIVITY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M09_S03_CONNECTIVITY" spirit:choiceRef="choice_pairs_4873554b">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M09_S04_CONNECTIVITY</spirit:name>
      <spirit:displayName>My M09_S04_CONNECTIVITY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M09_S04_CONNECTIVITY" spirit:choiceRef="choice_pairs_4873554b">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M09_S05_CONNECTIVITY</spirit:name>
      <spirit:displayName>My M09_S05_CONNECTIVITY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M09_S05_CONNECTIVITY" spirit:choiceRef="choice_pairs_4873554b">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M09_S06_CONNECTIVITY</spirit:name>
      <spirit:displayName>My M09_S06_CONNECTIVITY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M09_S06_CONNECTIVITY" spirit:choiceRef="choice_pairs_4873554b">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M09_S07_CONNECTIVITY</spirit:name>
      <spirit:displayName>My M09_S07_CONNECTIVITY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M09_S07_CONNECTIVITY" spirit:choiceRef="choice_pairs_4873554b">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M09_S08_CONNECTIVITY</spirit:name>
      <spirit:displayName>My M09_S08_CONNECTIVITY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M09_S08_CONNECTIVITY" spirit:choiceRef="choice_pairs_4873554b">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M09_S09_CONNECTIVITY</spirit:name>
      <spirit:displayName>My M09_S09_CONNECTIVITY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M09_S09_CONNECTIVITY" spirit:choiceRef="choice_pairs_4873554b">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M09_S10_CONNECTIVITY</spirit:name>
      <spirit:displayName>My M09_S10_CONNECTIVITY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M09_S10_CONNECTIVITY" spirit:choiceRef="choice_pairs_4873554b">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M09_S11_CONNECTIVITY</spirit:name>
      <spirit:displayName>My M09_S11_CONNECTIVITY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M09_S11_CONNECTIVITY" spirit:choiceRef="choice_pairs_4873554b">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M09_S12_CONNECTIVITY</spirit:name>
      <spirit:displayName>My M09_S12_CONNECTIVITY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M09_S12_CONNECTIVITY" spirit:choiceRef="choice_pairs_4873554b">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M09_S13_CONNECTIVITY</spirit:name>
      <spirit:displayName>My M09_S13_CONNECTIVITY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M09_S13_CONNECTIVITY" spirit:choiceRef="choice_pairs_4873554b">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M09_S14_CONNECTIVITY</spirit:name>
      <spirit:displayName>My M09_S14_CONNECTIVITY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M09_S14_CONNECTIVITY" spirit:choiceRef="choice_pairs_4873554b">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M09_S15_CONNECTIVITY</spirit:name>
      <spirit:displayName>My M09_S15_CONNECTIVITY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M09_S15_CONNECTIVITY" spirit:choiceRef="choice_pairs_4873554b" spirit:order="510">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M10_S00_CONNECTIVITY</spirit:name>
      <spirit:displayName>My M10_S00_CONNECTIVITY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M10_S00_CONNECTIVITY" spirit:choiceRef="choice_pairs_4873554b">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M10_S01_CONNECTIVITY</spirit:name>
      <spirit:displayName>My M10_S01_CONNECTIVITY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M10_S01_CONNECTIVITY" spirit:choiceRef="choice_pairs_4873554b">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M10_S02_CONNECTIVITY</spirit:name>
      <spirit:displayName>My M10_S02_CONNECTIVITY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M10_S02_CONNECTIVITY" spirit:choiceRef="choice_pairs_4873554b">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M10_S03_CONNECTIVITY</spirit:name>
      <spirit:displayName>My M10_S03_CONNECTIVITY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M10_S03_CONNECTIVITY" spirit:choiceRef="choice_pairs_4873554b">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M10_S04_CONNECTIVITY</spirit:name>
      <spirit:displayName>My M10_S04_CONNECTIVITY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M10_S04_CONNECTIVITY" spirit:choiceRef="choice_pairs_4873554b">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M10_S05_CONNECTIVITY</spirit:name>
      <spirit:displayName>My M10_S05_CONNECTIVITY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M10_S05_CONNECTIVITY" spirit:choiceRef="choice_pairs_4873554b">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M10_S06_CONNECTIVITY</spirit:name>
      <spirit:displayName>My M10_S06_CONNECTIVITY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M10_S06_CONNECTIVITY" spirit:choiceRef="choice_pairs_4873554b">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M10_S07_CONNECTIVITY</spirit:name>
      <spirit:displayName>My M10_S07_CONNECTIVITY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M10_S07_CONNECTIVITY" spirit:choiceRef="choice_pairs_4873554b">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M10_S08_CONNECTIVITY</spirit:name>
      <spirit:displayName>My M10_S08_CONNECTIVITY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M10_S08_CONNECTIVITY" spirit:choiceRef="choice_pairs_4873554b">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M10_S09_CONNECTIVITY</spirit:name>
      <spirit:displayName>My M10_S09_CONNECTIVITY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M10_S09_CONNECTIVITY" spirit:choiceRef="choice_pairs_4873554b">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M10_S10_CONNECTIVITY</spirit:name>
      <spirit:displayName>My M10_S10_CONNECTIVITY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M10_S10_CONNECTIVITY" spirit:choiceRef="choice_pairs_4873554b">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M10_S11_CONNECTIVITY</spirit:name>
      <spirit:displayName>My M10_S11_CONNECTIVITY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M10_S11_CONNECTIVITY" spirit:choiceRef="choice_pairs_4873554b">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M10_S12_CONNECTIVITY</spirit:name>
      <spirit:displayName>My M10_S12_CONNECTIVITY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M10_S12_CONNECTIVITY" spirit:choiceRef="choice_pairs_4873554b">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M10_S13_CONNECTIVITY</spirit:name>
      <spirit:displayName>My M10_S13_CONNECTIVITY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M10_S13_CONNECTIVITY" spirit:choiceRef="choice_pairs_4873554b">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M10_S14_CONNECTIVITY</spirit:name>
      <spirit:displayName>My M10_S14_CONNECTIVITY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M10_S14_CONNECTIVITY" spirit:choiceRef="choice_pairs_4873554b">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M10_S15_CONNECTIVITY</spirit:name>
      <spirit:displayName>My M10_S15_CONNECTIVITY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M10_S15_CONNECTIVITY" spirit:choiceRef="choice_pairs_4873554b" spirit:order="511">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M11_S00_CONNECTIVITY</spirit:name>
      <spirit:displayName>My M11_S00_CONNECTIVITY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M11_S00_CONNECTIVITY" spirit:choiceRef="choice_pairs_4873554b">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M11_S01_CONNECTIVITY</spirit:name>
      <spirit:displayName>My M11_S01_CONNECTIVITY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M11_S01_CONNECTIVITY" spirit:choiceRef="choice_pairs_4873554b">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M11_S02_CONNECTIVITY</spirit:name>
      <spirit:displayName>My M11_S02_CONNECTIVITY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M11_S02_CONNECTIVITY" spirit:choiceRef="choice_pairs_4873554b">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M11_S03_CONNECTIVITY</spirit:name>
      <spirit:displayName>My M11_S03_CONNECTIVITY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M11_S03_CONNECTIVITY" spirit:choiceRef="choice_pairs_4873554b">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M11_S04_CONNECTIVITY</spirit:name>
      <spirit:displayName>My M11_S04_CONNECTIVITY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M11_S04_CONNECTIVITY" spirit:choiceRef="choice_pairs_4873554b">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M11_S05_CONNECTIVITY</spirit:name>
      <spirit:displayName>My M11_S05_CONNECTIVITY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M11_S05_CONNECTIVITY" spirit:choiceRef="choice_pairs_4873554b">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M11_S06_CONNECTIVITY</spirit:name>
      <spirit:displayName>My M11_S06_CONNECTIVITY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M11_S06_CONNECTIVITY" spirit:choiceRef="choice_pairs_4873554b">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M11_S07_CONNECTIVITY</spirit:name>
      <spirit:displayName>My M11_S07_CONNECTIVITY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M11_S07_CONNECTIVITY" spirit:choiceRef="choice_pairs_4873554b">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M11_S08_CONNECTIVITY</spirit:name>
      <spirit:displayName>My M11_S08_CONNECTIVITY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M11_S08_CONNECTIVITY" spirit:choiceRef="choice_pairs_4873554b">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M11_S09_CONNECTIVITY</spirit:name>
      <spirit:displayName>My M11_S09_CONNECTIVITY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M11_S09_CONNECTIVITY" spirit:choiceRef="choice_pairs_4873554b">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M11_S10_CONNECTIVITY</spirit:name>
      <spirit:displayName>My M11_S10_CONNECTIVITY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M11_S10_CONNECTIVITY" spirit:choiceRef="choice_pairs_4873554b">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M11_S11_CONNECTIVITY</spirit:name>
      <spirit:displayName>My M11_S11_CONNECTIVITY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M11_S11_CONNECTIVITY" spirit:choiceRef="choice_pairs_4873554b">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M11_S12_CONNECTIVITY</spirit:name>
      <spirit:displayName>My M11_S12_CONNECTIVITY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M11_S12_CONNECTIVITY" spirit:choiceRef="choice_pairs_4873554b">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M11_S13_CONNECTIVITY</spirit:name>
      <spirit:displayName>My M11_S13_CONNECTIVITY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M11_S13_CONNECTIVITY" spirit:choiceRef="choice_pairs_4873554b">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M11_S14_CONNECTIVITY</spirit:name>
      <spirit:displayName>My M11_S14_CONNECTIVITY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M11_S14_CONNECTIVITY" spirit:choiceRef="choice_pairs_4873554b">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M11_S15_CONNECTIVITY</spirit:name>
      <spirit:displayName>My M11_S15_CONNECTIVITY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M11_S15_CONNECTIVITY" spirit:choiceRef="choice_pairs_4873554b" spirit:order="512">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M12_S00_CONNECTIVITY</spirit:name>
      <spirit:displayName>My M12_S00_CONNECTIVITY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M12_S00_CONNECTIVITY" spirit:choiceRef="choice_pairs_4873554b">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M12_S01_CONNECTIVITY</spirit:name>
      <spirit:displayName>My M12_S01_CONNECTIVITY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M12_S01_CONNECTIVITY" spirit:choiceRef="choice_pairs_4873554b">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M12_S02_CONNECTIVITY</spirit:name>
      <spirit:displayName>My M12_S02_CONNECTIVITY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M12_S02_CONNECTIVITY" spirit:choiceRef="choice_pairs_4873554b">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M12_S03_CONNECTIVITY</spirit:name>
      <spirit:displayName>My M12_S03_CONNECTIVITY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M12_S03_CONNECTIVITY" spirit:choiceRef="choice_pairs_4873554b">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M12_S04_CONNECTIVITY</spirit:name>
      <spirit:displayName>My M12_S04_CONNECTIVITY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M12_S04_CONNECTIVITY" spirit:choiceRef="choice_pairs_4873554b">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M12_S05_CONNECTIVITY</spirit:name>
      <spirit:displayName>My M12_S05_CONNECTIVITY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M12_S05_CONNECTIVITY" spirit:choiceRef="choice_pairs_4873554b">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M12_S06_CONNECTIVITY</spirit:name>
      <spirit:displayName>My M12_S06_CONNECTIVITY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M12_S06_CONNECTIVITY" spirit:choiceRef="choice_pairs_4873554b">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M12_S07_CONNECTIVITY</spirit:name>
      <spirit:displayName>My M12_S07_CONNECTIVITY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M12_S07_CONNECTIVITY" spirit:choiceRef="choice_pairs_4873554b">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M12_S08_CONNECTIVITY</spirit:name>
      <spirit:displayName>My M12_S08_CONNECTIVITY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M12_S08_CONNECTIVITY" spirit:choiceRef="choice_pairs_4873554b">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M12_S09_CONNECTIVITY</spirit:name>
      <spirit:displayName>My M12_S09_CONNECTIVITY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M12_S09_CONNECTIVITY" spirit:choiceRef="choice_pairs_4873554b">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M12_S10_CONNECTIVITY</spirit:name>
      <spirit:displayName>My M12_S10_CONNECTIVITY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M12_S10_CONNECTIVITY" spirit:choiceRef="choice_pairs_4873554b">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M12_S11_CONNECTIVITY</spirit:name>
      <spirit:displayName>My M12_S11_CONNECTIVITY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M12_S11_CONNECTIVITY" spirit:choiceRef="choice_pairs_4873554b">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M12_S12_CONNECTIVITY</spirit:name>
      <spirit:displayName>My M12_S12_CONNECTIVITY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M12_S12_CONNECTIVITY" spirit:choiceRef="choice_pairs_4873554b">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M12_S13_CONNECTIVITY</spirit:name>
      <spirit:displayName>My M12_S13_CONNECTIVITY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M12_S13_CONNECTIVITY" spirit:choiceRef="choice_pairs_4873554b">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M12_S14_CONNECTIVITY</spirit:name>
      <spirit:displayName>My M12_S14_CONNECTIVITY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M12_S14_CONNECTIVITY" spirit:choiceRef="choice_pairs_4873554b">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M12_S15_CONNECTIVITY</spirit:name>
      <spirit:displayName>My M12_S15_CONNECTIVITY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M12_S15_CONNECTIVITY" spirit:choiceRef="choice_pairs_4873554b" spirit:order="513">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M13_S00_CONNECTIVITY</spirit:name>
      <spirit:displayName>My M13_S00_CONNECTIVITY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M13_S00_CONNECTIVITY" spirit:choiceRef="choice_pairs_4873554b">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M13_S01_CONNECTIVITY</spirit:name>
      <spirit:displayName>My M13_S01_CONNECTIVITY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M13_S01_CONNECTIVITY" spirit:choiceRef="choice_pairs_4873554b">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M13_S02_CONNECTIVITY</spirit:name>
      <spirit:displayName>My M13_S02_CONNECTIVITY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M13_S02_CONNECTIVITY" spirit:choiceRef="choice_pairs_4873554b">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M13_S03_CONNECTIVITY</spirit:name>
      <spirit:displayName>My M13_S03_CONNECTIVITY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M13_S03_CONNECTIVITY" spirit:choiceRef="choice_pairs_4873554b">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M13_S04_CONNECTIVITY</spirit:name>
      <spirit:displayName>My M13_S04_CONNECTIVITY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M13_S04_CONNECTIVITY" spirit:choiceRef="choice_pairs_4873554b">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M13_S05_CONNECTIVITY</spirit:name>
      <spirit:displayName>My M13_S05_CONNECTIVITY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M13_S05_CONNECTIVITY" spirit:choiceRef="choice_pairs_4873554b">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M13_S06_CONNECTIVITY</spirit:name>
      <spirit:displayName>My M13_S06_CONNECTIVITY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M13_S06_CONNECTIVITY" spirit:choiceRef="choice_pairs_4873554b">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M13_S07_CONNECTIVITY</spirit:name>
      <spirit:displayName>My M13_S07_CONNECTIVITY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M13_S07_CONNECTIVITY" spirit:choiceRef="choice_pairs_4873554b">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M13_S08_CONNECTIVITY</spirit:name>
      <spirit:displayName>My M13_S08_CONNECTIVITY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M13_S08_CONNECTIVITY" spirit:choiceRef="choice_pairs_4873554b">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M13_S09_CONNECTIVITY</spirit:name>
      <spirit:displayName>My M13_S09_CONNECTIVITY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M13_S09_CONNECTIVITY" spirit:choiceRef="choice_pairs_4873554b">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M13_S10_CONNECTIVITY</spirit:name>
      <spirit:displayName>My M13_S10_CONNECTIVITY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M13_S10_CONNECTIVITY" spirit:choiceRef="choice_pairs_4873554b">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M13_S11_CONNECTIVITY</spirit:name>
      <spirit:displayName>My M13_S11_CONNECTIVITY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M13_S11_CONNECTIVITY" spirit:choiceRef="choice_pairs_4873554b">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M13_S12_CONNECTIVITY</spirit:name>
      <spirit:displayName>My M13_S12_CONNECTIVITY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M13_S12_CONNECTIVITY" spirit:choiceRef="choice_pairs_4873554b">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M13_S13_CONNECTIVITY</spirit:name>
      <spirit:displayName>My M13_S13_CONNECTIVITY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M13_S13_CONNECTIVITY" spirit:choiceRef="choice_pairs_4873554b">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M13_S14_CONNECTIVITY</spirit:name>
      <spirit:displayName>My M13_S14_CONNECTIVITY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M13_S14_CONNECTIVITY" spirit:choiceRef="choice_pairs_4873554b">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M13_S15_CONNECTIVITY</spirit:name>
      <spirit:displayName>My M13_S15_CONNECTIVITY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M13_S15_CONNECTIVITY" spirit:choiceRef="choice_pairs_4873554b" spirit:order="514">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M14_S00_CONNECTIVITY</spirit:name>
      <spirit:displayName>My M14_S00_CONNECTIVITY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M14_S00_CONNECTIVITY" spirit:choiceRef="choice_pairs_4873554b">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M14_S01_CONNECTIVITY</spirit:name>
      <spirit:displayName>My M14_S01_CONNECTIVITY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M14_S01_CONNECTIVITY" spirit:choiceRef="choice_pairs_4873554b">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M14_S02_CONNECTIVITY</spirit:name>
      <spirit:displayName>My M14_S02_CONNECTIVITY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M14_S02_CONNECTIVITY" spirit:choiceRef="choice_pairs_4873554b">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M14_S03_CONNECTIVITY</spirit:name>
      <spirit:displayName>My M14_S03_CONNECTIVITY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M14_S03_CONNECTIVITY" spirit:choiceRef="choice_pairs_4873554b">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M14_S04_CONNECTIVITY</spirit:name>
      <spirit:displayName>My M14_S04_CONNECTIVITY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M14_S04_CONNECTIVITY" spirit:choiceRef="choice_pairs_4873554b">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M14_S05_CONNECTIVITY</spirit:name>
      <spirit:displayName>My M14_S05_CONNECTIVITY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M14_S05_CONNECTIVITY" spirit:choiceRef="choice_pairs_4873554b">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M14_S06_CONNECTIVITY</spirit:name>
      <spirit:displayName>My M14_S06_CONNECTIVITY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M14_S06_CONNECTIVITY" spirit:choiceRef="choice_pairs_4873554b">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M14_S07_CONNECTIVITY</spirit:name>
      <spirit:displayName>My M14_S07_CONNECTIVITY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M14_S07_CONNECTIVITY" spirit:choiceRef="choice_pairs_4873554b">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M14_S08_CONNECTIVITY</spirit:name>
      <spirit:displayName>My M14_S08_CONNECTIVITY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M14_S08_CONNECTIVITY" spirit:choiceRef="choice_pairs_4873554b">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M14_S09_CONNECTIVITY</spirit:name>
      <spirit:displayName>My M14_S09_CONNECTIVITY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M14_S09_CONNECTIVITY" spirit:choiceRef="choice_pairs_4873554b">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M14_S10_CONNECTIVITY</spirit:name>
      <spirit:displayName>My M14_S10_CONNECTIVITY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M14_S10_CONNECTIVITY" spirit:choiceRef="choice_pairs_4873554b">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M14_S11_CONNECTIVITY</spirit:name>
      <spirit:displayName>My M14_S11_CONNECTIVITY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M14_S11_CONNECTIVITY" spirit:choiceRef="choice_pairs_4873554b">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M14_S12_CONNECTIVITY</spirit:name>
      <spirit:displayName>My M14_S12_CONNECTIVITY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M14_S12_CONNECTIVITY" spirit:choiceRef="choice_pairs_4873554b">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M14_S13_CONNECTIVITY</spirit:name>
      <spirit:displayName>My M14_S13_CONNECTIVITY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M14_S13_CONNECTIVITY" spirit:choiceRef="choice_pairs_4873554b">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M14_S14_CONNECTIVITY</spirit:name>
      <spirit:displayName>My M14_S14_CONNECTIVITY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M14_S14_CONNECTIVITY" spirit:choiceRef="choice_pairs_4873554b">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M14_S15_CONNECTIVITY</spirit:name>
      <spirit:displayName>My M14_S15_CONNECTIVITY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M14_S15_CONNECTIVITY" spirit:choiceRef="choice_pairs_4873554b" spirit:order="515">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M15_S00_CONNECTIVITY</spirit:name>
      <spirit:displayName>My M15_S00_CONNECTIVITY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M15_S00_CONNECTIVITY" spirit:choiceRef="choice_pairs_4873554b">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M15_S01_CONNECTIVITY</spirit:name>
      <spirit:displayName>My M15_S01_CONNECTIVITY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M15_S01_CONNECTIVITY" spirit:choiceRef="choice_pairs_4873554b">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M15_S02_CONNECTIVITY</spirit:name>
      <spirit:displayName>My M15_S02_CONNECTIVITY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M15_S02_CONNECTIVITY" spirit:choiceRef="choice_pairs_4873554b">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M15_S03_CONNECTIVITY</spirit:name>
      <spirit:displayName>My M15_S03_CONNECTIVITY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M15_S03_CONNECTIVITY" spirit:choiceRef="choice_pairs_4873554b">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M15_S04_CONNECTIVITY</spirit:name>
      <spirit:displayName>My M15_S04_CONNECTIVITY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M15_S04_CONNECTIVITY" spirit:choiceRef="choice_pairs_4873554b">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M15_S05_CONNECTIVITY</spirit:name>
      <spirit:displayName>My M15_S05_CONNECTIVITY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M15_S05_CONNECTIVITY" spirit:choiceRef="choice_pairs_4873554b">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M15_S06_CONNECTIVITY</spirit:name>
      <spirit:displayName>My M15_S06_CONNECTIVITY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M15_S06_CONNECTIVITY" spirit:choiceRef="choice_pairs_4873554b">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M15_S07_CONNECTIVITY</spirit:name>
      <spirit:displayName>My M15_S07_CONNECTIVITY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M15_S07_CONNECTIVITY" spirit:choiceRef="choice_pairs_4873554b">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M15_S08_CONNECTIVITY</spirit:name>
      <spirit:displayName>My M15_S08_CONNECTIVITY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M15_S08_CONNECTIVITY" spirit:choiceRef="choice_pairs_4873554b">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M15_S09_CONNECTIVITY</spirit:name>
      <spirit:displayName>My M15_S09_CONNECTIVITY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M15_S09_CONNECTIVITY" spirit:choiceRef="choice_pairs_4873554b">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M15_S10_CONNECTIVITY</spirit:name>
      <spirit:displayName>My M15_S10_CONNECTIVITY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M15_S10_CONNECTIVITY" spirit:choiceRef="choice_pairs_4873554b">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M15_S11_CONNECTIVITY</spirit:name>
      <spirit:displayName>My M15_S11_CONNECTIVITY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M15_S11_CONNECTIVITY" spirit:choiceRef="choice_pairs_4873554b">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M15_S12_CONNECTIVITY</spirit:name>
      <spirit:displayName>My M15_S12_CONNECTIVITY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M15_S12_CONNECTIVITY" spirit:choiceRef="choice_pairs_4873554b">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M15_S13_CONNECTIVITY</spirit:name>
      <spirit:displayName>My M15_S13_CONNECTIVITY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M15_S13_CONNECTIVITY" spirit:choiceRef="choice_pairs_4873554b">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M15_S14_CONNECTIVITY</spirit:name>
      <spirit:displayName>My M15_S14_CONNECTIVITY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M15_S14_CONNECTIVITY" spirit:choiceRef="choice_pairs_4873554b">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M15_S15_CONNECTIVITY</spirit:name>
      <spirit:displayName>My M15_S15_CONNECTIVITY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M15_S15_CONNECTIVITY" spirit:choiceRef="choice_pairs_4873554b" spirit:order="516">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>Component_Name</spirit:name>
      <spirit:value spirit:resolve="user" spirit:id="PARAM_VALUE.Component_Name" spirit:order="1">bd_2aab_rinsw_6</spirit:value>
    </spirit:parameter>
  </spirit:parameters>
  <spirit:vendorExtensions>
    <xilinx:coreExtensions>
      <xilinx:displayName>SmartConnect Switchboard</xilinx:displayName>
      <xilinx:xpmLibraries>
        <xilinx:xpmLibrary>XPM_MEMORY</xilinx:xpmLibrary>
        <xilinx:xpmLibrary>XPM_FIFO</xilinx:xpmLibrary>
      </xilinx:xpmLibraries>
      <xilinx:coreRevision>8</xilinx:coreRevision>
      <xilinx:configElementInfos>
        <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.ACLK.ASSOCIATED_BUSIF" xilinx:valuePermission="bd"/>
        <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.ACLK.ASSOCIATED_PORT" xilinx:valuePermission="bd"/>
        <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.ACLK.ASSOCIATED_RESET" xilinx:valuePermission="bd"/>
        <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.ACLK.CLK_DOMAIN" xilinx:valueSource="default_prop" xilinx:valuePermission="bd"/>
        <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.ACLK.FREQ_HZ" xilinx:valueSource="user_prop" xilinx:valuePermission="bd"/>
        <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.ACLK.FREQ_TOLERANCE_HZ" xilinx:valuePermission="bd"/>
        <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.ACLK.PHASE" xilinx:valuePermission="bd"/>
        <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.ACLKEN.POLARITY" xilinx:valuePermission="bd"/>
        <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.S00_SC.BRIDGES" xilinx:valuePermission="bd"/>
        <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.S01_SC.BRIDGES" xilinx:valuePermission="bd"/>
        <xilinx:configElementInfo xilinx:referenceId="PARAM_VALUE.NUM_MI" xilinx:valueSource="user"/>
        <xilinx:configElementInfo xilinx:referenceId="PARAM_VALUE.NUM_SI" xilinx:valueSource="user"/>
        <xilinx:configElementInfo xilinx:referenceId="PARAM_VALUE.PAYLD_WIDTH" xilinx:valueSource="user"/>
      </xilinx:configElementInfos>
    </xilinx:coreExtensions>
    <xilinx:packagingInfo>
      <xilinx:xilinxVersion>2024.2</xilinx:xilinxVersion>
      <xilinx:checksum xilinx:scope="busInterfaces" xilinx:value="d09a1c27"/>
      <xilinx:checksum xilinx:scope="fileGroups" xilinx:value="81c05794"/>
      <xilinx:checksum xilinx:scope="ports" xilinx:value="21c28d95"/>
      <xilinx:checksum xilinx:scope="hdlParameters" xilinx:value="ad59c4b8"/>
      <xilinx:checksum xilinx:scope="parameters" xilinx:value="46a6cfe1"/>
    </xilinx:packagingInfo>
  </spirit:vendorExtensions>
</spirit:component>
