BEGIN	{
	Count = 0;
	Flag = 0;
	fake=0;
	ModuleName = ""
	TheModuleVersion = ""
	TheModuleType = ""
	TheModuleUnload = ""
	FS="[[:space:]+]"
}

/LIBRARY/{ 
	if( Flag == 0 )
	{
		i = NF
		while( i > 1 )
		{
			if( $i != "" )
			{
				ModuleName = $i
				break
			}
			i--
		}
		
		
	}
}

/VERSION/{ 
	if( Flag == 0 )
	{
		i = NF
		while( i > 1 )
		{
			if( $i != "" )
			{
				TheModuleVersion = $i
				break
			}
			i--
		}
		 
	}
}

/TYPE/{ 
	if( Flag == 0 )
	{
		i = NF
		while( i > 1 )
		{
			if( $i != "" )
			{
				TheModuleType = $i
				break
			}
			i--
		}
		 
	}
}


/EXPORTS/	{ Flag = 1 }

END	{
	print "\n\n"
	print "#include <sysLpm.h>\n\n"
	print "extern unsigned int " ModuleName "_symbol_table[];"
	if( ModuleName == "" )
	{
		print "??? No module name defined ???"
	}
	print "int " ModuleName "_lib_register( int EntityID )"
	print "{"
	print "	int RetVal;\n"
	print "	RetVal = fnLPM_RegisterDynamicLibrary("

#The module name
	printf "		%c%s%c,\n", 34, ModuleName, 34
#The module version
	if( TheModuleVersion == "" )
	{
		print "		0,"
	}
	else
	{
		print "		" TheModuleVersion ","
	}
#The module type	
	if( TheModuleType == "" )
	{
		print "		LLDR_MODULE_TYPE_NORMAL,"
	}
	else
	{
		print "		" TheModuleType ","
	}
#The entity ID
	print "		EntityID,"	
#The call table address
	print "		" ModuleName "_symbol_table    );\n"		
	print "	return( RetVal );"
	print "}"
}
