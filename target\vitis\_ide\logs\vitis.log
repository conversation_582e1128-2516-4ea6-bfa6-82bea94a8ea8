20:35:05 INFO  : updated workspace path: C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis
20:35:05 INFO  : Marking the target connection as default Local
20:35:05 INFO  : Marking the target connection as default Linux Agent
20:35:05 INFO  : Marking the target connection as default XRTServer
20:35:05 INFO  : Marking the target connection as default LinuxEmulation
20:35:05 INFO  : Marking the target connection as default QEMU
20:35:05 INFO  : created .gitignore file for the project C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis
20:35:06 INFO  : Successfully created repository data at C:\Users\<USER>\AppData\Local\Temp\rigel_lopper_lymz17812715793978235938
20:35:06 INFO  : cmd.exe, /C, C:\Xilinx\VitisEmbedded\Vitis\2024.2\tps\win64\lopper-1.1.0\env\Scripts\activate.bat && python C:\Xilinx\VitisEmbedded\Vitis\2024.2\data\embeddedsw\scripts\pyesw\\get_template_data.py -d C:\Users\<USER>\AppData\Local\Temp\rigel_lopper_lymz12021755418976008877 -r C:\Users\<USER>\AppData\Local\Temp\rigel_lopper_lymz17812715793978235938\.repo.yaml && C:\Xilinx\VitisEmbedded\Vitis\2024.2\tps\win64\lopper-1.1.0\env\Scripts\deactivate.bat
20:35:07 INFO  : Embedded Template List generated successfully
20:35:07 INFO  : cmd.exe, /C, C:\Xilinx\VitisEmbedded\Vitis\2024.2\tps\win64\lopper-1.1.0\env\Scripts\activate.bat && python C:\Xilinx\VitisEmbedded\Vitis\2024.2\data\embeddedsw\scripts\pyesw\\repo.py -st   C:\Xilinx\VitisEmbedded\Vitis\2024.2\data\embeddedsw && C:\Xilinx\VitisEmbedded\Vitis\2024.2\tps\win64\lopper-1.1.0\env\Scripts\deactivate.bat
20:35:08 INFO  : Successfully created repository data at C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis/_ide\.wsdata
20:35:08 INFO  : cmd.exe, /C, C:\Xilinx\VitisEmbedded\Vitis\2024.2\tps\win64\lopper-1.1.0\env\Scripts\activate.bat && python C:\Xilinx\VitisEmbedded\Vitis\2024.2\data\embeddedsw\scripts\pyesw\\get_template_data.py -d C:\Users\<USER>\AppData\Local\Temp\rigel_lopper_lymz16153805581676412786 -r C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\_ide\.wsdata\.repo.yaml && C:\Xilinx\VitisEmbedded\Vitis\2024.2\tps\win64\lopper-1.1.0\env\Scripts\deactivate.bat
20:35:08 INFO  : Embedded Template List generated successfully
20:36:41 INFO  : Found no platform with name 'ZynqDesign_wrapper' in install repositories
20:36:41 INFO  : Using sdt generation script from C:\Xilinx\VitisEmbedded\Vitis\2024.2\vitis-server\scripts\platformutil.tcl
20:36:41 INFO  : cmd.exe, /C, C:\Xilinx\VitisEmbedded\Vitis\2024.2\tps\win64\lopper-1.1.0\env\Scripts\activate.bat && xsct.bat C:\Xilinx\VitisEmbedded\Vitis\2024.2\vitis-server\scripts\platformutil.tcl C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vivado\vivado_prj\ZynqDesign_wrapper.xsa C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\.rigel_lopper_temp_platform\ZynqDesign_wrapper\hw\sdt && C:\Xilinx\VitisEmbedded\Vitis\2024.2\tps\win64\lopper-1.1.0\env\Scripts\deactivate.bat
20:36:44 INFO  : SDT generated successfully
20:36:44 INFO  : Lopper command for cpu List generation: lopper --enhanced --werror -f -O C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\.rigel_lopper -i C:\Xilinx\VitisEmbedded\Vitis\2024.2\tps\win64\lopper-1.1.0\env\Lib\site-packages\lopper\lops\lop-cpu-oslist.dts C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\.rigel_lopper_temp_platform\ZynqDesign_wrapper\hw\sdt\system-top.dts
20:36:44 INFO  : cmd.exe, /C, C:\Xilinx\VitisEmbedded\Vitis\2024.2\tps\win64\lopper-1.1.0\env\Scripts\activate.bat && lopper --enhanced --werror -f -O C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\.rigel_lopper -i C:\Xilinx\VitisEmbedded\Vitis\2024.2\tps\win64\lopper-1.1.0\env\Lib\site-packages\lopper\lops\lop-cpu-oslist.dts C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\.rigel_lopper_temp_platform\ZynqDesign_wrapper\hw\sdt\system-top.dts && C:\Xilinx\VitisEmbedded\Vitis\2024.2\tps\win64\lopper-1.1.0\env\Scripts\deactivate.bat
20:36:45 INFO  : CPU List generated successfully
20:47:05 INFO  : Platform platform creation started.
20:47:05 INFO  : Found no platform with name 'ZynqDesign_wrapper' in install repositories
20:47:05 INFO  : Lopper command for cpu List generation: lopper --enhanced --werror -f -O C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\.rigel_lopper -i C:\Xilinx\VitisEmbedded\Vitis\2024.2\tps\win64\lopper-1.1.0\env\Lib\site-packages\lopper\lops\lop-cpu-oslist.dts C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\platform\hw\sdt\system-top.dts
20:47:05 INFO  : cmd.exe, /C, C:\Xilinx\VitisEmbedded\Vitis\2024.2\tps\win64\lopper-1.1.0\env\Scripts\activate.bat && lopper --enhanced --werror -f -O C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\.rigel_lopper -i C:\Xilinx\VitisEmbedded\Vitis\2024.2\tps\win64\lopper-1.1.0\env\Lib\site-packages\lopper\lops\lop-cpu-oslist.dts C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\platform\hw\sdt\system-top.dts && C:\Xilinx\VitisEmbedded\Vitis\2024.2\tps\win64\lopper-1.1.0\env\Scripts\deactivate.bat
20:47:05 INFO  : CPU List generated successfully
20:47:05 INFO  : SDT generated successfully.
20:47:05 INFO  : created .gitignore file for the project C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\platform
20:47:05 INFO  : ZYNQ: Using the QEMU args from install at  : C:\Xilinx\VitisEmbedded\Vitis\2024.2\data\emulation\platforms\zynq\sw\a9_linux\qemu\qemu_args.txt
20:47:05 INFO  : ZYNQ: Using the QEMU args from install at  : C:\Xilinx\VitisEmbedded\Vitis\2024.2\data\emulation\platforms\zynq\sw\a9_standalone\qemu\qemu_args.txt
20:47:05 INFO  : ZYNQ: Using the QEMU args from install at  : C:\Xilinx\VitisEmbedded\Vitis\2024.2\data\emulation\platforms\zynq\sw\a9_standalone\qemu\qemu_args.txt
20:47:05 INFO  : lopper command to generate BSP :python C:\Xilinx\VitisEmbedded\Vitis\2024.2\data\embeddedsw/scripts/pyesw//create_bsp.py -w C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\platform\ps7_cortexa9_0\standalone_ps7_cortexa9_0\bsp -p ps7_cortexa9_0 -o standalone -s C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\platform\hw\sdt\system-top.dts -t empty_application -r C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\_ide\.wsdata\.repo.yaml
20:47:05 INFO  : lopper command to generate BSP :python C:\Xilinx\VitisEmbedded\Vitis\2024.2\data\embeddedsw/scripts/pyesw//create_bsp.py -w C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\platform\zynq_fsbl\zynq_fsbl_bsp -p ps7_cortexa9_0 -o standalone -s C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\platform\hw\sdt\system-top.dts -t zynq_fsbl -r C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\_ide\.wsdata\.repo.yaml
20:47:05 INFO  : cmd.exe, /C, C:\Xilinx\VitisEmbedded\Vitis\2024.2\tps\win64\lopper-1.1.0\env\Scripts\activate.bat && python C:\Xilinx\VitisEmbedded\Vitis\2024.2\data\embeddedsw\scripts\pyesw\\create_bsp.py -w C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\platform\zynq_fsbl\zynq_fsbl_bsp -p ps7_cortexa9_0 -o standalone -s C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\platform\hw\sdt\system-top.dts -t zynq_fsbl -r C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\_ide\.wsdata\.repo.yaml && C:\Xilinx\VitisEmbedded\Vitis\2024.2\tps\win64\lopper-1.1.0\env\Scripts\deactivate.bat
20:47:05 INFO  : cmd.exe, /C, C:\Xilinx\VitisEmbedded\Vitis\2024.2\tps\win64\lopper-1.1.0\env\Scripts\activate.bat && python C:\Xilinx\VitisEmbedded\Vitis\2024.2\data\embeddedsw\scripts\pyesw\\create_bsp.py -w C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\platform\ps7_cortexa9_0\standalone_ps7_cortexa9_0\bsp -p ps7_cortexa9_0 -o standalone -s C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\platform\hw\sdt\system-top.dts -t empty_application -r C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\_ide\.wsdata\.repo.yaml && C:\Xilinx\VitisEmbedded\Vitis\2024.2\tps\win64\lopper-1.1.0\env\Scripts\deactivate.bat
20:47:13 INFO  : Successfully created Domain at C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\platform\ps7_cortexa9_0\standalone_ps7_cortexa9_0\bsp
20:47:13 INFO  : Successfully Generated Domain C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\platform\ps7_cortexa9_0\standalone_ps7_cortexa9_0\bsp
20:47:13 INFO  : Domain standalone_ps7_cortexa9_0 added successfully.
20:47:13 INFO  : Successfully created Domain at C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\platform\zynq_fsbl\zynq_fsbl_bsp
20:47:13 INFO  : Successfully Generated Domain C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\platform\zynq_fsbl\zynq_fsbl_bsp
20:47:13 INFO  : lopper command to create baremetal application :python C:\Xilinx\VitisEmbedded\Vitis\2024.2\data\embeddedsw/scripts/pyesw//create_app.py -s C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\platform\zynq_fsbl -t zynq_fsbl -d C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\platform\zynq_fsbl\zynq_fsbl_bsp -n fsbl -r C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\_ide\.wsdata\.repo.yaml
20:47:13 INFO  : cmd.exe, /C, C:\Xilinx\VitisEmbedded\Vitis\2024.2\tps\win64\lopper-1.1.0\env\Scripts\activate.bat && python C:\Xilinx\VitisEmbedded\Vitis\2024.2\data\embeddedsw\scripts\pyesw\\validate_bsp.py -t zynq_fsbl -d C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\platform\zynq_fsbl\zynq_fsbl_bsp -r C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\_ide\.wsdata\.repo.yaml && C:\Xilinx\VitisEmbedded\Vitis\2024.2\tps\win64\lopper-1.1.0\env\Scripts\deactivate.bat
20:47:14 INFO  : Successfully validated template zynq_fsbl
20:47:14 INFO  : cmd.exe, /C, C:\Xilinx\VitisEmbedded\Vitis\2024.2\tps\win64\lopper-1.1.0\env\Scripts\activate.bat && python C:\Xilinx\VitisEmbedded\Vitis\2024.2\data\embeddedsw\scripts\pyesw\\create_app.py -s C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\platform\zynq_fsbl -t zynq_fsbl -d C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\platform\zynq_fsbl\zynq_fsbl_bsp -n fsbl -r C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\_ide\.wsdata\.repo.yaml && C:\Xilinx\VitisEmbedded\Vitis\2024.2\tps\win64\lopper-1.1.0\env\Scripts\deactivate.bat
20:47:16 INFO  : Successfully Created Application sources at C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/platform/zynq_fsbl
20:47:16 INFO  : Successfully created Application C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\platform\zynq_fsbl
20:47:16 INFO  : Platform FSBL Boot domain added successfully.
20:47:16 INFO  : Platform creation finished successfully.
20:47:16 INFO  : Platform Quick Build initiated.
20:47:16 INFO  : Generating Export directory
20:47:16 INFO  : Generated platform metadata for creating application(s) based on platform platform.
20:51:45 INFO  : cmd.exe, /C, C:\Xilinx\VitisEmbedded\Vitis\2024.2\tps\win64\lopper-1.1.0\env\Scripts\activate.bat && lopper -O  . C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\platform\hw\sdt\system-top.dts -- baremetal_getsupported_comp_xlnx ps7_cortexa9_0 C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\_ide\.wsdata\.repo.yaml && C:\Xilinx\VitisEmbedded\Vitis\2024.2\tps\win64\lopper-1.1.0\env\Scripts\deactivate.bat
20:51:46 INFO  : Supported Library List generated.
20:51:49 INFO  : cmd.exe, /C, C:\Xilinx\VitisEmbedded\Vitis\2024.2\tps\win64\lopper-1.1.0\env\Scripts\activate.bat && lopper -O  . C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\platform\hw\sdt\system-top.dts -- baremetal_getsupported_comp_xlnx ps7_cortexa9_0 C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\_ide\.wsdata\.repo.yaml && C:\Xilinx\VitisEmbedded\Vitis\2024.2\tps\win64\lopper-1.1.0\env\Scripts\deactivate.bat
20:51:50 INFO  : Supported Library List generated.
20:52:01 INFO  : Generating BSP for the domain C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\platform\zynq_fsbl\zynq_fsbl_bsp
20:52:01 INFO  : lopper command to reconfigure BSP :python C:\Xilinx\VitisEmbedded\Vitis\2024.2\data\embeddedsw/scripts/pyesw//reconfig_bsp.py -d C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\platform\zynq_fsbl\zynq_fsbl_bsp
20:52:01 INFO  : cmd.exe, /C, C:\Xilinx\VitisEmbedded\Vitis\2024.2\tps\win64\lopper-1.1.0\env\Scripts\activate.bat && python C:\Xilinx\VitisEmbedded\Vitis\2024.2\data\embeddedsw\scripts\pyesw\\reconfig_bsp.py -d C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\platform\zynq_fsbl\zynq_fsbl_bsp && C:\Xilinx\VitisEmbedded\Vitis\2024.2\tps\win64\lopper-1.1.0\env\Scripts\deactivate.bat
20:52:01 INFO  : Domain reconfigured successfully
20:52:01 INFO  : Updating the given parameters of library in BSP C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\platform\zynq_fsbl\zynq_fsbl_bsp
20:52:01 INFO  : cmd.exe, /C, C:\Xilinx\VitisEmbedded\Vitis\2024.2\tps\win64\lopper-1.1.0\env\Scripts\activate.bat && python C:\Xilinx\VitisEmbedded\Vitis\2024.2\data\embeddedsw\scripts\pyesw\\config_bsp.py -d C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\platform\zynq_fsbl\zynq_fsbl_bsp -st standalone standalone_stdin:ps7_uart_1 -r C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\_ide\.wsdata\.repo.yaml && C:\Xilinx\VitisEmbedded\Vitis\2024.2\tps\win64\lopper-1.1.0\env\Scripts\deactivate.bat
20:52:02 INFO  : -- Configuring done
20:52:02 INFO  : -- Generating done
20:52:02 INFO  : CMake Warning:
20:52:02 INFO  :   Manually-specified variables were not used by the project:
20:52:02 INFO  : 
20:52:02 INFO  :     CMAKE_TOOLCHAIN_FILE
20:52:02 INFO  : 
20:52:02 INFO  : 
20:52:02 INFO  : -- Build files have been written to: C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/platform/zynq_fsbl/zynq_fsbl_bsp/libsrc/build_configs/gen_bsp
20:52:02 INFO  : Updated the library parameters successfully
20:52:02 INFO  : cmd.exe, /C, C:\Xilinx\VitisEmbedded\Vitis\2024.2\tps\win64\lopper-1.1.0\env\Scripts\activate.bat && lopper -O  . C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\platform\hw\sdt\system-top.dts -- baremetal_getsupported_comp_xlnx ps7_cortexa9_0 C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\_ide\.wsdata\.repo.yaml && C:\Xilinx\VitisEmbedded\Vitis\2024.2\tps\win64\lopper-1.1.0\env\Scripts\deactivate.bat
20:52:02 INFO  : Supported Library List generated.
20:52:03 INFO  : Generating BSP for the domain C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\platform\zynq_fsbl\zynq_fsbl_bsp
20:52:03 INFO  : lopper command to reconfigure BSP :python C:\Xilinx\VitisEmbedded\Vitis\2024.2\data\embeddedsw/scripts/pyesw//reconfig_bsp.py -d C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\platform\zynq_fsbl\zynq_fsbl_bsp
20:52:03 INFO  : cmd.exe, /C, C:\Xilinx\VitisEmbedded\Vitis\2024.2\tps\win64\lopper-1.1.0\env\Scripts\activate.bat && python C:\Xilinx\VitisEmbedded\Vitis\2024.2\data\embeddedsw\scripts\pyesw\\reconfig_bsp.py -d C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\platform\zynq_fsbl\zynq_fsbl_bsp && C:\Xilinx\VitisEmbedded\Vitis\2024.2\tps\win64\lopper-1.1.0\env\Scripts\deactivate.bat
20:52:04 INFO  : Domain reconfigured successfully
20:52:04 INFO  : Updating the given parameters of library in BSP C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\platform\zynq_fsbl\zynq_fsbl_bsp
20:52:04 INFO  : cmd.exe, /C, C:\Xilinx\VitisEmbedded\Vitis\2024.2\tps\win64\lopper-1.1.0\env\Scripts\activate.bat && python C:\Xilinx\VitisEmbedded\Vitis\2024.2\data\embeddedsw\scripts\pyesw\\config_bsp.py -d C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\platform\zynq_fsbl\zynq_fsbl_bsp -st standalone standalone_stdout:ps7_uart_1 -r C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\_ide\.wsdata\.repo.yaml && C:\Xilinx\VitisEmbedded\Vitis\2024.2\tps\win64\lopper-1.1.0\env\Scripts\deactivate.bat
20:52:04 INFO  : -- Configuring done
20:52:04 INFO  : -- Generating done
20:52:04 INFO  : -- Build files have been written to: C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/platform/zynq_fsbl/zynq_fsbl_bsp/libsrc/build_configs/gen_bsp
20:52:04 INFO  : Updated the library parameters successfully
20:52:05 INFO  : cmd.exe, /C, C:\Xilinx\VitisEmbedded\Vitis\2024.2\tps\win64\lopper-1.1.0\env\Scripts\activate.bat && lopper -O  . C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\platform\hw\sdt\system-top.dts -- baremetal_getsupported_comp_xlnx ps7_cortexa9_0 C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\_ide\.wsdata\.repo.yaml && C:\Xilinx\VitisEmbedded\Vitis\2024.2\tps\win64\lopper-1.1.0\env\Scripts\deactivate.bat
20:52:05 INFO  : Supported Library List generated.
20:52:16 INFO  : Generating BSP for the domain C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\platform\ps7_cortexa9_0\standalone_ps7_cortexa9_0\bsp
20:52:16 INFO  : lopper command to reconfigure BSP :python C:\Xilinx\VitisEmbedded\Vitis\2024.2\data\embeddedsw/scripts/pyesw//reconfig_bsp.py -d C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\platform\ps7_cortexa9_0\standalone_ps7_cortexa9_0\bsp
20:52:16 INFO  : cmd.exe, /C, C:\Xilinx\VitisEmbedded\Vitis\2024.2\tps\win64\lopper-1.1.0\env\Scripts\activate.bat && python C:\Xilinx\VitisEmbedded\Vitis\2024.2\data\embeddedsw\scripts\pyesw\\reconfig_bsp.py -d C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\platform\ps7_cortexa9_0\standalone_ps7_cortexa9_0\bsp && C:\Xilinx\VitisEmbedded\Vitis\2024.2\tps\win64\lopper-1.1.0\env\Scripts\deactivate.bat
20:52:16 INFO  : Domain reconfigured successfully
20:52:16 INFO  : Updating the given parameters of library in BSP C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\platform\ps7_cortexa9_0\standalone_ps7_cortexa9_0\bsp
20:52:16 INFO  : cmd.exe, /C, C:\Xilinx\VitisEmbedded\Vitis\2024.2\tps\win64\lopper-1.1.0\env\Scripts\activate.bat && python C:\Xilinx\VitisEmbedded\Vitis\2024.2\data\embeddedsw\scripts\pyesw\\config_bsp.py -d C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\platform\ps7_cortexa9_0\standalone_ps7_cortexa9_0\bsp -st standalone standalone_stdin:ps7_uart_1 -r C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\_ide\.wsdata\.repo.yaml && C:\Xilinx\VitisEmbedded\Vitis\2024.2\tps\win64\lopper-1.1.0\env\Scripts\deactivate.bat
20:52:17 INFO  : -- Configuring done
20:52:17 INFO  : -- Generating done
20:52:17 INFO  : CMake Warning:
20:52:17 INFO  :   Manually-specified variables were not used by the project:
20:52:17 INFO  : 
20:52:17 INFO  :     CMAKE_TOOLCHAIN_FILE
20:52:17 INFO  : 
20:52:17 INFO  : 
20:52:17 INFO  : -- Build files have been written to: C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/platform/ps7_cortexa9_0/standalone_ps7_cortexa9_0/bsp/libsrc/build_configs/gen_bsp
20:52:17 INFO  : Updated the library parameters successfully
20:52:17 INFO  : cmd.exe, /C, C:\Xilinx\VitisEmbedded\Vitis\2024.2\tps\win64\lopper-1.1.0\env\Scripts\activate.bat && lopper -O  . C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\platform\hw\sdt\system-top.dts -- baremetal_getsupported_comp_xlnx ps7_cortexa9_0 C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\_ide\.wsdata\.repo.yaml && C:\Xilinx\VitisEmbedded\Vitis\2024.2\tps\win64\lopper-1.1.0\env\Scripts\deactivate.bat
20:52:18 INFO  : Supported Library List generated.
20:52:19 INFO  : Generating BSP for the domain C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\platform\ps7_cortexa9_0\standalone_ps7_cortexa9_0\bsp
20:52:19 INFO  : lopper command to reconfigure BSP :python C:\Xilinx\VitisEmbedded\Vitis\2024.2\data\embeddedsw/scripts/pyesw//reconfig_bsp.py -d C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\platform\ps7_cortexa9_0\standalone_ps7_cortexa9_0\bsp
20:52:19 INFO  : cmd.exe, /C, C:\Xilinx\VitisEmbedded\Vitis\2024.2\tps\win64\lopper-1.1.0\env\Scripts\activate.bat && python C:\Xilinx\VitisEmbedded\Vitis\2024.2\data\embeddedsw\scripts\pyesw\\reconfig_bsp.py -d C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\platform\ps7_cortexa9_0\standalone_ps7_cortexa9_0\bsp && C:\Xilinx\VitisEmbedded\Vitis\2024.2\tps\win64\lopper-1.1.0\env\Scripts\deactivate.bat
20:52:19 INFO  : Domain reconfigured successfully
20:52:19 INFO  : Updating the given parameters of library in BSP C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\platform\ps7_cortexa9_0\standalone_ps7_cortexa9_0\bsp
20:52:19 INFO  : cmd.exe, /C, C:\Xilinx\VitisEmbedded\Vitis\2024.2\tps\win64\lopper-1.1.0\env\Scripts\activate.bat && python C:\Xilinx\VitisEmbedded\Vitis\2024.2\data\embeddedsw\scripts\pyesw\\config_bsp.py -d C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\platform\ps7_cortexa9_0\standalone_ps7_cortexa9_0\bsp -st standalone standalone_stdout:ps7_uart_1 -r C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\_ide\.wsdata\.repo.yaml && C:\Xilinx\VitisEmbedded\Vitis\2024.2\tps\win64\lopper-1.1.0\env\Scripts\deactivate.bat
20:52:20 INFO  : -- Configuring done
20:52:20 INFO  : -- Generating done
20:52:20 INFO  : -- Build files have been written to: C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/platform/ps7_cortexa9_0/standalone_ps7_cortexa9_0/bsp/libsrc/build_configs/gen_bsp
20:52:20 INFO  : Updated the library parameters successfully
20:52:20 INFO  : cmd.exe, /C, C:\Xilinx\VitisEmbedded\Vitis\2024.2\tps\win64\lopper-1.1.0\env\Scripts\activate.bat && lopper -O  . C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\platform\hw\sdt\system-top.dts -- baremetal_getsupported_comp_xlnx ps7_cortexa9_0 C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\_ide\.wsdata\.repo.yaml && C:\Xilinx\VitisEmbedded\Vitis\2024.2\tps\win64\lopper-1.1.0\env\Scripts\deactivate.bat
20:52:21 INFO  : Supported Library List generated.
20:52:22 INFO  : Updating Examples in Driver C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\platform\ps7_cortexa9_0\standalone_ps7_cortexa9_0\bsp
20:52:22 INFO  : cmd.exe, /C, C:\Xilinx\VitisEmbedded\Vitis\2024.2\tps\win64\lopper-1.1.0\env\Scripts\activate.bat && python C:\Xilinx\VitisEmbedded\Vitis\2024.2\data\embeddedsw\scripts\pyesw\\load_example.py -d C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\platform\ps7_cortexa9_0\standalone_ps7_cortexa9_0\bsp && C:\Xilinx\VitisEmbedded\Vitis\2024.2\tps\win64\lopper-1.1.0\env\Scripts\deactivate.bat
20:52:25 INFO  : -- The C compiler identification is GNU 13.3.0
20:52:25 INFO  : -- The CXX compiler identification is GNU 13.3.0
20:52:25 INFO  : -- Detecting C compiler ABI info
20:52:25 INFO  : -- Detecting C compiler ABI info - done
20:52:25 INFO  : -- Check for working C compiler: C:/Xilinx/VitisEmbedded/Vitis/2024.2/gnu/aarch32/nt/gcc-arm-none-eabi/bin/arm-none-eabi-gcc.exe - skipped
20:52:25 INFO  : -- Detecting C compile features
20:52:25 INFO  : -- Detecting C compile features - done
20:52:25 INFO  : -- Detecting CXX compiler ABI info
20:52:25 INFO  : -- Detecting CXX compiler ABI info - done
20:52:25 INFO  : -- Check for working CXX compiler: C:/Xilinx/VitisEmbedded/Vitis/2024.2/gnu/aarch32/nt/gcc-arm-none-eabi/bin/arm-none-eabi-g++.exe - skipped
20:52:25 INFO  : -- Detecting CXX compile features
20:52:25 INFO  : -- Detecting CXX compile features - done
20:52:25 INFO  : -- Configuring done
20:52:25 INFO  : -- Generating done
20:52:25 INFO  : CMake Warning:
20:52:25 INFO  :   Manually-specified variables were not used by the project:
20:52:25 INFO  : 
20:52:25 INFO  :     CMAKE_LIBRARY_PATH
20:52:25 INFO  : 
20:52:25 INFO  : 
20:52:25 INFO  : -- Build files have been written to: C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/platform/ps7_cortexa9_0/standalone_ps7_cortexa9_0/bsp/libsrc/build_configs/exmetadata
20:52:25 INFO  : Updated Examples in Driver
20:52:56 INFO  : BSP build will be initiated for Domain zynq_fsbl
20:52:56 INFO  : Generating BSP for the domain C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\platform\zynq_fsbl\zynq_fsbl_bsp
20:52:56 INFO  : lopper command to reconfigure BSP :python C:\Xilinx\VitisEmbedded\Vitis\2024.2\data\embeddedsw/scripts/pyesw//reconfig_bsp.py -d C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\platform\zynq_fsbl\zynq_fsbl_bsp
20:52:56 INFO  : cmd.exe, /C, C:\Xilinx\VitisEmbedded\Vitis\2024.2\tps\win64\lopper-1.1.0\env\Scripts\activate.bat && python C:\Xilinx\VitisEmbedded\Vitis\2024.2\data\embeddedsw\scripts\pyesw\\reconfig_bsp.py -d C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\platform\zynq_fsbl\zynq_fsbl_bsp && C:\Xilinx\VitisEmbedded\Vitis\2024.2\tps\win64\lopper-1.1.0\env\Scripts\deactivate.bat
20:52:56 INFO  : BSP build will be initiated for Domain standalone_ps7_cortexa9_0
20:52:56 INFO  : Generating BSP for the domain C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\platform\ps7_cortexa9_0\standalone_ps7_cortexa9_0\bsp
20:52:56 INFO  : lopper command to reconfigure BSP :python C:\Xilinx\VitisEmbedded\Vitis\2024.2\data\embeddedsw/scripts/pyesw//reconfig_bsp.py -d C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\platform\ps7_cortexa9_0\standalone_ps7_cortexa9_0\bsp
20:52:56 INFO  : cmd.exe, /C, C:\Xilinx\VitisEmbedded\Vitis\2024.2\tps\win64\lopper-1.1.0\env\Scripts\activate.bat && python C:\Xilinx\VitisEmbedded\Vitis\2024.2\data\embeddedsw\scripts\pyesw\\reconfig_bsp.py -d C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\platform\ps7_cortexa9_0\standalone_ps7_cortexa9_0\bsp && C:\Xilinx\VitisEmbedded\Vitis\2024.2\tps\win64\lopper-1.1.0\env\Scripts\deactivate.bat
20:52:56 INFO  : Domain reconfigured successfully
20:52:56 INFO  : Generating BSP for the domain C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\platform\zynq_fsbl\zynq_fsbl_bsp
20:52:56 INFO  : lopper command to build BSP :python C:\Xilinx\VitisEmbedded\Vitis\2024.2\data\embeddedsw/scripts/pyesw//build_bsp.py -d C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\platform\zynq_fsbl\zynq_fsbl_bsp
20:52:56 INFO  : cmd.exe, /C, C:\Xilinx\VitisEmbedded\Vitis\2024.2\tps\win64\lopper-1.1.0\env\Scripts\activate.bat && python C:\Xilinx\VitisEmbedded\Vitis\2024.2\data\embeddedsw\scripts\pyesw\\build_bsp.py -d C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\platform\zynq_fsbl\zynq_fsbl_bsp && C:\Xilinx\VitisEmbedded\Vitis\2024.2\tps\win64\lopper-1.1.0\env\Scripts\deactivate.bat
20:52:56 INFO  : Domain reconfigured successfully
20:52:56 INFO  : Generating BSP for the domain C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\platform\ps7_cortexa9_0\standalone_ps7_cortexa9_0\bsp
20:52:56 INFO  : lopper command to build BSP :python C:\Xilinx\VitisEmbedded\Vitis\2024.2\data\embeddedsw/scripts/pyesw//build_bsp.py -d C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\platform\ps7_cortexa9_0\standalone_ps7_cortexa9_0\bsp
20:52:56 INFO  : cmd.exe, /C, C:\Xilinx\VitisEmbedded\Vitis\2024.2\tps\win64\lopper-1.1.0\env\Scripts\activate.bat && python C:\Xilinx\VitisEmbedded\Vitis\2024.2\data\embeddedsw\scripts\pyesw\\build_bsp.py -d C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\platform\ps7_cortexa9_0\standalone_ps7_cortexa9_0\bsp && C:\Xilinx\VitisEmbedded\Vitis\2024.2\tps\win64\lopper-1.1.0\env\Scripts\deactivate.bat
20:53:00 INFO  : Domain build successfully
20:53:01 INFO  : Domain build successfully
20:53:01 INFO  : Generating Boot ELFs
20:53:01 INFO  : lopper command to retarget baremetal application :python C:\Xilinx\VitisEmbedded\Vitis\2024.2\data\embeddedsw/scripts/pyesw//retarget_app.py -s C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\platform\zynq_fsbl -d C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\platform\zynq_fsbl\zynq_fsbl_bsp -b C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\platform\zynq_fsbl\build
20:53:01 INFO  : cmd.exe, /C, C:\Xilinx\VitisEmbedded\Vitis\2024.2\tps\win64\lopper-1.1.0\env\Scripts\activate.bat && python C:\Xilinx\VitisEmbedded\Vitis\2024.2\data\embeddedsw\scripts\pyesw\\retarget_app.py -s C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\platform\zynq_fsbl -d C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\platform\zynq_fsbl\zynq_fsbl_bsp -b C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\platform\zynq_fsbl\build && C:\Xilinx\VitisEmbedded\Vitis\2024.2\tps\win64\lopper-1.1.0\env\Scripts\deactivate.bat
20:53:01 INFO  : Successfully retargeted Application C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\platform\zynq_fsbl
20:53:01 INFO  : lopper command to build baremetal application :python C:\Xilinx\VitisEmbedded\Vitis\2024.2\data\embeddedsw/scripts/pyesw//build_app.py -s C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\platform\zynq_fsbl -b C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\platform\zynq_fsbl\build
20:53:01 INFO  : cmd.exe, /C, C:\Xilinx\VitisEmbedded\Vitis\2024.2\tps\win64\lopper-1.1.0\env\Scripts\activate.bat && python C:\Xilinx\VitisEmbedded\Vitis\2024.2\data\embeddedsw\scripts\pyesw\\build_app.py -s C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\platform\zynq_fsbl -b C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\platform\zynq_fsbl\build && C:\Xilinx\VitisEmbedded\Vitis\2024.2\tps\win64\lopper-1.1.0\env\Scripts\deactivate.bat
20:53:04 INFO  : Successfully building the baremetal applicationC:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\platform\zynq_fsbl\build
20:53:04 INFO  : Generating Export directory
20:53:04 INFO  : Platform Build Finished successfully.
20:53:26 INFO  : cmd.exe, /C, C:\Xilinx\VitisEmbedded\Vitis\2024.2\tps\win64\lopper-1.1.0\env\Scripts\activate.bat && python C:\Xilinx\VitisEmbedded\Vitis\2024.2\data\embeddedsw\scripts\pyesw\\validate_bsp.py -t hello_world -d C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\platform\export\platform\sw\standalone_ps7_cortexa9_0 -r C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\_ide\.wsdata\.repo.yaml && C:\Xilinx\VitisEmbedded\Vitis\2024.2\tps\win64\lopper-1.1.0\env\Scripts\deactivate.bat
20:53:27 INFO  : Successfully validated template hello_world
20:53:29 INFO  : lopper command to create baremetal application :python C:\Xilinx\VitisEmbedded\Vitis\2024.2\data\embeddedsw/scripts/pyesw//create_app.py -s C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\hello_world\src -t hello_world -d C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\platform\export\platform\sw\standalone_ps7_cortexa9_0 -n hello_world -r C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\_ide\.wsdata\.repo.yaml
20:53:29 INFO  : cmd.exe, /C, C:\Xilinx\VitisEmbedded\Vitis\2024.2\tps\win64\lopper-1.1.0\env\Scripts\activate.bat && python C:\Xilinx\VitisEmbedded\Vitis\2024.2\data\embeddedsw\scripts\pyesw\\validate_bsp.py -t hello_world -d C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\platform\export\platform\sw\standalone_ps7_cortexa9_0 -r C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\_ide\.wsdata\.repo.yaml && C:\Xilinx\VitisEmbedded\Vitis\2024.2\tps\win64\lopper-1.1.0\env\Scripts\deactivate.bat
20:53:29 INFO  : Successfully validated template hello_world
20:53:29 INFO  : cmd.exe, /C, C:\Xilinx\VitisEmbedded\Vitis\2024.2\tps\win64\lopper-1.1.0\env\Scripts\activate.bat && python C:\Xilinx\VitisEmbedded\Vitis\2024.2\data\embeddedsw\scripts\pyesw\\create_app.py -s C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\hello_world\src -t hello_world -d C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\platform\export\platform\sw\standalone_ps7_cortexa9_0 -n hello_world -r C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\_ide\.wsdata\.repo.yaml && C:\Xilinx\VitisEmbedded\Vitis\2024.2\tps\win64\lopper-1.1.0\env\Scripts\deactivate.bat
20:53:32 INFO  : Successfully Created Application sources at C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/hello_world/src
20:53:32 INFO  : Successfully created Application C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\hello_world\src
20:53:32 INFO  : The hardware specification used by project 'C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\hello_world' is out of sync with the platform. Resource files extracted from the hardware specification will be updated.
20:53:32 INFO  : The updated bitstream files are copied from platform to folder 'C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\hello_world\_ide\bitstream' in project 'C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\hello_world'.
20:53:32 INFO  : The updated ps init files are copied from platform to folder 'C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\hello_world\_ide\psinit' in project 'C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\hello_world'.
20:53:32 INFO  : created .gitignore file for the project C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\hello_world
20:53:51 INFO  : BSP build will be initiated for Domain zynq_fsbl
20:53:51 INFO  : Generating BSP for the domain C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\platform\zynq_fsbl\zynq_fsbl_bsp
20:53:51 INFO  : lopper command to reconfigure BSP :python C:\Xilinx\VitisEmbedded\Vitis\2024.2\data\embeddedsw/scripts/pyesw//reconfig_bsp.py -d C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\platform\zynq_fsbl\zynq_fsbl_bsp
20:53:51 INFO  : cmd.exe, /C, C:\Xilinx\VitisEmbedded\Vitis\2024.2\tps\win64\lopper-1.1.0\env\Scripts\activate.bat && python C:\Xilinx\VitisEmbedded\Vitis\2024.2\data\embeddedsw\scripts\pyesw\\reconfig_bsp.py -d C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\platform\zynq_fsbl\zynq_fsbl_bsp && C:\Xilinx\VitisEmbedded\Vitis\2024.2\tps\win64\lopper-1.1.0\env\Scripts\deactivate.bat
20:53:51 INFO  : BSP build will be initiated for Domain standalone_ps7_cortexa9_0
20:53:51 INFO  : Generating BSP for the domain C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\platform\ps7_cortexa9_0\standalone_ps7_cortexa9_0\bsp
20:53:51 INFO  : lopper command to reconfigure BSP :python C:\Xilinx\VitisEmbedded\Vitis\2024.2\data\embeddedsw/scripts/pyesw//reconfig_bsp.py -d C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\platform\ps7_cortexa9_0\standalone_ps7_cortexa9_0\bsp
20:53:51 INFO  : cmd.exe, /C, C:\Xilinx\VitisEmbedded\Vitis\2024.2\tps\win64\lopper-1.1.0\env\Scripts\activate.bat && python C:\Xilinx\VitisEmbedded\Vitis\2024.2\data\embeddedsw\scripts\pyesw\\reconfig_bsp.py -d C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\platform\ps7_cortexa9_0\standalone_ps7_cortexa9_0\bsp && C:\Xilinx\VitisEmbedded\Vitis\2024.2\tps\win64\lopper-1.1.0\env\Scripts\deactivate.bat
20:53:51 INFO  : Domain reconfigured successfully
20:53:51 INFO  : Generating BSP for the domain C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\platform\zynq_fsbl\zynq_fsbl_bsp
20:53:51 INFO  : lopper command to build BSP :python C:\Xilinx\VitisEmbedded\Vitis\2024.2\data\embeddedsw/scripts/pyesw//build_bsp.py -d C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\platform\zynq_fsbl\zynq_fsbl_bsp
20:53:51 INFO  : cmd.exe, /C, C:\Xilinx\VitisEmbedded\Vitis\2024.2\tps\win64\lopper-1.1.0\env\Scripts\activate.bat && python C:\Xilinx\VitisEmbedded\Vitis\2024.2\data\embeddedsw\scripts\pyesw\\build_bsp.py -d C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\platform\zynq_fsbl\zynq_fsbl_bsp && C:\Xilinx\VitisEmbedded\Vitis\2024.2\tps\win64\lopper-1.1.0\env\Scripts\deactivate.bat
20:53:51 INFO  : Domain reconfigured successfully
20:53:51 INFO  : Generating BSP for the domain C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\platform\ps7_cortexa9_0\standalone_ps7_cortexa9_0\bsp
20:53:51 INFO  : lopper command to build BSP :python C:\Xilinx\VitisEmbedded\Vitis\2024.2\data\embeddedsw/scripts/pyesw//build_bsp.py -d C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\platform\ps7_cortexa9_0\standalone_ps7_cortexa9_0\bsp
20:53:51 INFO  : cmd.exe, /C, C:\Xilinx\VitisEmbedded\Vitis\2024.2\tps\win64\lopper-1.1.0\env\Scripts\activate.bat && python C:\Xilinx\VitisEmbedded\Vitis\2024.2\data\embeddedsw\scripts\pyesw\\build_bsp.py -d C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\platform\ps7_cortexa9_0\standalone_ps7_cortexa9_0\bsp && C:\Xilinx\VitisEmbedded\Vitis\2024.2\tps\win64\lopper-1.1.0\env\Scripts\deactivate.bat
20:53:52 INFO  : Domain build successfully
20:53:52 INFO  : Domain build successfully
20:53:52 INFO  : Generating Boot ELFs
20:53:52 INFO  : lopper command to retarget baremetal application :python C:\Xilinx\VitisEmbedded\Vitis\2024.2\data\embeddedsw/scripts/pyesw//retarget_app.py -s C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\platform\zynq_fsbl -d C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\platform\zynq_fsbl\zynq_fsbl_bsp -b C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\platform\zynq_fsbl\build
20:53:52 INFO  : cmd.exe, /C, C:\Xilinx\VitisEmbedded\Vitis\2024.2\tps\win64\lopper-1.1.0\env\Scripts\activate.bat && python C:\Xilinx\VitisEmbedded\Vitis\2024.2\data\embeddedsw\scripts\pyesw\\retarget_app.py -s C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\platform\zynq_fsbl -d C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\platform\zynq_fsbl\zynq_fsbl_bsp -b C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\platform\zynq_fsbl\build && C:\Xilinx\VitisEmbedded\Vitis\2024.2\tps\win64\lopper-1.1.0\env\Scripts\deactivate.bat
20:53:52 INFO  : Successfully retargeted Application C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\platform\zynq_fsbl
20:53:52 INFO  : lopper command to build baremetal application :python C:\Xilinx\VitisEmbedded\Vitis\2024.2\data\embeddedsw/scripts/pyesw//build_app.py -s C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\platform\zynq_fsbl -b C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\platform\zynq_fsbl\build
20:53:52 INFO  : cmd.exe, /C, C:\Xilinx\VitisEmbedded\Vitis\2024.2\tps\win64\lopper-1.1.0\env\Scripts\activate.bat && python C:\Xilinx\VitisEmbedded\Vitis\2024.2\data\embeddedsw\scripts\pyesw\\build_app.py -s C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\platform\zynq_fsbl -b C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\platform\zynq_fsbl\build && C:\Xilinx\VitisEmbedded\Vitis\2024.2\tps\win64\lopper-1.1.0\env\Scripts\deactivate.bat
20:53:53 INFO  : Successfully building the baremetal applicationC:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\platform\zynq_fsbl\build
20:53:53 INFO  : Generating Export directory
20:53:53 INFO  : Platform Build Finished successfully.
20:53:53 INFO  : lopper command to retarget baremetal application :python C:\Xilinx\VitisEmbedded\Vitis\2024.2\data\embeddedsw/scripts/pyesw//retarget_app.py -s c:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\hello_world\src -d C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\platform\export\platform\sw\standalone_ps7_cortexa9_0 -b c:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\hello_world\build
20:53:53 INFO  : cmd.exe, /C, C:\Xilinx\VitisEmbedded\Vitis\2024.2\tps\win64\lopper-1.1.0\env\Scripts\activate.bat && python C:\Xilinx\VitisEmbedded\Vitis\2024.2\data\embeddedsw\scripts\pyesw\\retarget_app.py -s c:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\hello_world\src -d C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\platform\export\platform\sw\standalone_ps7_cortexa9_0 -b c:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\hello_world\build && C:\Xilinx\VitisEmbedded\Vitis\2024.2\tps\win64\lopper-1.1.0\env\Scripts\deactivate.bat
20:53:54 INFO  : Successfully retargeted Application c:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\hello_world\src
20:53:54 INFO  : cmd.exe, /C, C:\Xilinx\VitisEmbedded\Vitis\2024.2\tps\win64\lopper-1.1.0\env\Scripts\activate.bat && python C:\Xilinx\VitisEmbedded\Vitis\2024.2\data\embeddedsw\scripts\pyesw\\build_app.py -s c:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\hello_world\src -b c:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\hello_world\build && C:\Xilinx\VitisEmbedded\Vitis\2024.2\tps\win64\lopper-1.1.0\env\Scripts\deactivate.bat
21:21:53 INFO  : The updated bitstream files are copied from platform to folder 'C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\hello_world\_ide\bitstream' in project 'C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\hello_world'.
21:21:53 INFO  : The updated ps init files are copied from platform to folder 'C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\hello_world\_ide\psinit' in project 'C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\hello_world'.
21:23:48 INFO  : XSDB server has started successfully from frontend.
21:23:48 INFO  : It has taken  1secs.
21:23:49 INFO  : Connection to XSDB Server established.
21:23:49 INFO  : It has taken  1secs.
21:23:52 INFO  : connect -url tcp:127.0.0.1:3121
21:23:52 INFO  : Done
21:23:53 INFO  : bpremove -all
21:23:53 INFO  : Context for 'APU' is selected.
21:23:53 INFO  : System reset is completed.
21:23:56 INFO  : 'after 3000' command is executed.
21:23:56 INFO  : 'targets -set -filter {jtag_cable_name =~ "Digilent JTAG-SMT2 210251A08870" && level==0 && jtag_device_ctx=="jsn-JTAG-SMT2-210251A08870-23727093-0"}' command is executed.
21:23:58 INFO  : Device configured successfully with "C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/hello_world/_ide/bitstream/ZynqDesign_wrapper.bit"
21:23:58 INFO  : targets -set -nocase -filter {name =~"APU*"}
21:23:59 INFO  : loadhw -hw C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/platform/export/platform/hw/ZynqDesign_wrapper.xsa -mem-ranges [list {0x40000000 0xbfffffff}]
21:23:59 INFO  : configparams force-mem-access 1
21:23:59 INFO  : targets -set -nocase -filter {name =~"APU*"}
21:23:59 INFO  : source C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/hello_world/_ide/psinit/ps7_init.tcl
21:23:59 INFO  : ps7_init
21:23:59 INFO  : ps7_post_config
21:23:59 INFO  : targets -set -nocase -filter {name =~ "*A9*#0"}
21:23:59 INFO  : rst -processor
21:23:59 INFO  : dow C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/workspace/TA/arm_xc7z0x0_le_hard/make/TA.elf
21:23:59 INFO  : con
21:23:59 INFO  : configparams force-mem-access 0
21:24:00 INFO  : Testing the connection for 127.0.0.1
21:25:18 INFO  : disconnect
21:25:19 INFO  : connect -url tcp:127.0.0.1:3121
21:25:19 INFO  : bpremove -all
21:25:28 ERROR : Failed to initialize the hardware Could not find ARM device on the board for connection 'Local'.
Check if the target is in:
1. Split JTAG - No operations are possible with ARM DAP.
2. Non JTAG bootmode - Bootrom may need time to enable DAP.
Please try again.


Troubleshooting hints:
1. Check whether board is connected to system properly.
2. In case of zynq board, check whether Digilent/Xilinx cable switch settings are correct.
3. If you are using Xilinx Platform Cable USB, ensure that status LED is green.
21:25:49 INFO  : connect -url tcp:127.0.0.1:3121
21:25:49 INFO  : bpremove -all
21:25:49 INFO  : Context for 'APU' is selected.
21:25:49 INFO  : System reset is completed.
21:25:52 INFO  : 'after 3000' command is executed.
21:25:52 INFO  : 'targets -set -filter {jtag_cable_name =~ "Digilent JTAG-SMT2 210251A08870" && level==0 && jtag_device_ctx=="jsn-JTAG-SMT2-210251A08870-23727093-0"}' command is executed.
21:25:54 INFO  : Device configured successfully with "C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/hello_world/_ide/bitstream/ZynqDesign_wrapper.bit"
21:25:54 INFO  : targets -set -nocase -filter {name =~"APU*"}
21:25:55 INFO  : loadhw -hw C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/platform/export/platform/hw/ZynqDesign_wrapper.xsa -mem-ranges [list {0x40000000 0xbfffffff}]
21:25:55 INFO  : configparams force-mem-access 1
21:25:55 INFO  : targets -set -nocase -filter {name =~"APU*"}
21:25:55 INFO  : source C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/hello_world/_ide/psinit/ps7_init.tcl
21:25:55 INFO  : ps7_init
21:25:55 INFO  : ps7_post_config
21:25:55 INFO  : targets -set -nocase -filter {name =~ "*A9*#0"}
21:25:55 INFO  : rst -processor
21:25:56 INFO  : dow C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/workspace/TA/arm_xc7z0x0_le_hard/make/TA.elf
21:25:56 INFO  : bpadd main
21:25:56 INFO  : con
21:25:56 INFO  : configparams force-mem-access 0
21:25:57 INFO  : Testing the connection for 127.0.0.1
21:26:32 INFO  : disconnect
21:26:54 INFO  : connect -url tcp:127.0.0.1:3121
21:26:54 INFO  : bpremove -all
21:26:54 INFO  : Context for 'APU' is selected.
21:26:54 INFO  : System reset is completed.
21:26:57 INFO  : 'after 3000' command is executed.
21:26:57 INFO  : 'targets -set -filter {jtag_cable_name =~ "Digilent JTAG-SMT2 210251A08870" && level==0 && jtag_device_ctx=="jsn-JTAG-SMT2-210251A08870-23727093-0"}' command is executed.
21:26:59 INFO  : Device configured successfully with "C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/hello_world/_ide/bitstream/ZynqDesign_wrapper.bit"
21:26:59 INFO  : targets -set -nocase -filter {name =~"APU*"}
21:27:00 INFO  : loadhw -hw C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/platform/export/platform/hw/ZynqDesign_wrapper.xsa -mem-ranges [list {0x40000000 0xbfffffff}]
21:27:00 INFO  : configparams force-mem-access 1
21:27:00 INFO  : targets -set -nocase -filter {name =~"APU*"}
21:27:00 INFO  : source C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/hello_world/_ide/psinit/ps7_init.tcl
21:27:01 INFO  : ps7_init
21:27:01 INFO  : ps7_post_config
21:27:01 INFO  : targets -set -nocase -filter {name =~ "*A9*#0"}
21:27:01 INFO  : rst -processor
21:27:01 INFO  : dow C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/workspace/TA/arm_xc7z0x0_le_hard/make/TA.elf
21:27:01 INFO  : bpadd main
21:27:01 INFO  : con
21:27:01 INFO  : configparams force-mem-access 0
21:27:02 INFO  : Testing the connection for 127.0.0.1
21:27:20 INFO  : disconnect
21:27:39 INFO  : connect -url tcp:127.0.0.1:3121
21:27:39 INFO  : bpremove -all
21:27:39 INFO  : Context for 'APU' is selected.
21:27:39 INFO  : System reset is completed.
21:27:42 INFO  : 'after 3000' command is executed.
21:27:42 INFO  : 'targets -set -filter {jtag_cable_name =~ "Digilent JTAG-SMT2 210251A08870" && level==0 && jtag_device_ctx=="jsn-JTAG-SMT2-210251A08870-23727093-0"}' command is executed.
21:27:45 INFO  : Device configured successfully with "C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/hello_world/_ide/bitstream/ZynqDesign_wrapper.bit"
21:27:45 INFO  : targets -set -nocase -filter {name =~"APU*"}
21:27:46 INFO  : loadhw -hw C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/platform/export/platform/hw/ZynqDesign_wrapper.xsa -mem-ranges [list {0x40000000 0xbfffffff}]
21:27:46 INFO  : configparams force-mem-access 1
21:27:46 INFO  : targets -set -nocase -filter {name =~"APU*"}
21:27:46 INFO  : source C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/hello_world/_ide/psinit/ps7_init.tcl
21:27:46 INFO  : ps7_init
21:27:46 INFO  : ps7_post_config
21:27:46 INFO  : targets -set -nocase -filter {name =~ "*A9*#0"}
21:27:46 INFO  : rst -processor
21:27:46 INFO  : dow C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/workspace/TA/arm_xc7z0x0_le_hard/make/TA.elf
21:27:47 INFO  : bpadd main
21:27:47 INFO  : con
21:27:47 INFO  : configparams force-mem-access 0
21:27:48 INFO  : Testing the connection for 127.0.0.1
21:29:40 INFO  : disconnect
21:30:00 INFO  : connect -url tcp:127.0.0.1:3121
21:30:00 INFO  : bpremove -all
21:30:00 INFO  : Context for 'APU' is selected.
21:30:00 INFO  : System reset is completed.
21:30:04 INFO  : 'after 3000' command is executed.
21:30:04 INFO  : 'targets -set -filter {jtag_cable_name =~ "Digilent JTAG-SMT2 210251A08870" && level==0 && jtag_device_ctx=="jsn-JTAG-SMT2-210251A08870-23727093-0"}' command is executed.
21:30:06 INFO  : Device configured successfully with "C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/hello_world/_ide/bitstream/ZynqDesign_wrapper.bit"
21:30:06 INFO  : targets -set -nocase -filter {name =~"APU*"}
21:30:07 INFO  : loadhw -hw C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/platform/export/platform/hw/ZynqDesign_wrapper.xsa -mem-ranges [list {0x40000000 0xbfffffff}]
21:30:07 INFO  : configparams force-mem-access 1
21:30:07 INFO  : targets -set -nocase -filter {name =~"APU*"}
21:30:07 INFO  : source C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/hello_world/_ide/psinit/ps7_init.tcl
21:30:07 INFO  : ps7_init
21:30:07 INFO  : ps7_post_config
21:30:07 INFO  : targets -set -nocase -filter {name =~ "*A9*#0"}
21:30:07 INFO  : rst -processor
21:30:08 INFO  : dow C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/workspace/TA/arm_xc7z0x0_le_hard/make/TA.elf
21:30:08 INFO  : bpadd main
21:30:08 INFO  : con
21:30:08 INFO  : configparams force-mem-access 0
21:30:09 INFO  : Testing the connection for 127.0.0.1
21:32:29 INFO  : disconnect
21:33:52 INFO  : The updated bitstream files are copied from platform to folder 'C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\hello_world\_ide\bitstream' in project 'C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\hello_world'.
21:33:52 INFO  : The updated ps init files are copied from platform to folder 'C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\hello_world\_ide\psinit' in project 'C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\hello_world'.
21:35:10 INFO  : connect -url tcp:127.0.0.1:3121
21:35:10 INFO  : bpremove -all
21:35:10 INFO  : Context for 'APU' is selected.
21:35:10 INFO  : System reset is completed.
21:35:13 INFO  : 'after 3000' command is executed.
21:35:13 INFO  : 'targets -set -filter {jtag_cable_name =~ "Digilent JTAG-SMT2 210251A08870" && level==0 && jtag_device_ctx=="jsn-JTAG-SMT2-210251A08870-23727093-0"}' command is executed.
21:35:15 INFO  : Device configured successfully with "C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/hello_world/_ide/bitstream/ZynqDesign_wrapper.bit"
21:35:15 INFO  : targets -set -nocase -filter {name =~"APU*"}
21:35:17 INFO  : loadhw -hw C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/platform/export/platform/hw/ZynqDesign_wrapper.xsa -mem-ranges [list {0x40000000 0xbfffffff}]
21:35:17 INFO  : configparams force-mem-access 1
21:35:17 INFO  : targets -set -nocase -filter {name =~"APU*"}
21:35:17 INFO  : source C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/hello_world/_ide/psinit/ps7_init.tcl
21:35:17 INFO  : ps7_init
21:35:17 INFO  : ps7_post_config
21:35:17 INFO  : targets -set -nocase -filter {name =~ "*A9*#0"}
21:35:17 INFO  : rst -processor
21:35:17 INFO  : dow C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/workspace/TA/arm_xc7z0x0_le_hard/make/TA.elf
21:35:17 INFO  : bpadd main
21:35:17 INFO  : con
21:35:17 INFO  : configparams force-mem-access 0
21:35:19 INFO  : Testing the connection for 127.0.0.1
21:37:24 INFO  : disconnect
21:37:25 INFO  : connect -url tcp:127.0.0.1:3121
21:37:25 INFO  : bpremove -all
21:37:28 INFO  : Context for 'APU' is selected.
21:37:28 INFO  : System reset is completed.
21:37:31 INFO  : 'after 3000' command is executed.
21:37:31 INFO  : 'targets -set -filter {jtag_cable_name =~ "Digilent JTAG-SMT2 210251A08870" && level==0 && jtag_device_ctx=="jsn-JTAG-SMT2-210251A08870-23727093-0"}' command is executed.
21:37:33 INFO  : Device configured successfully with "C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/hello_world/_ide/bitstream/ZynqDesign_wrapper.bit"
21:37:33 INFO  : targets -set -nocase -filter {name =~"APU*"}
21:37:34 INFO  : loadhw -hw C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/platform/export/platform/hw/ZynqDesign_wrapper.xsa -mem-ranges [list {0x40000000 0xbfffffff}]
21:37:34 INFO  : configparams force-mem-access 1
21:37:34 INFO  : targets -set -nocase -filter {name =~"APU*"}
21:37:34 INFO  : source C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/hello_world/_ide/psinit/ps7_init.tcl
21:37:34 INFO  : ps7_init
21:37:34 INFO  : ps7_post_config
21:37:34 INFO  : targets -set -nocase -filter {name =~ "*A9*#0"}
21:37:34 INFO  : rst -processor
21:37:35 INFO  : dow C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/workspace/TA/arm_xc7z0x0_le_hard/make/TA.elf
21:37:35 INFO  : bpadd main
21:37:35 INFO  : con
21:37:35 INFO  : configparams force-mem-access 0
21:37:36 INFO  : Testing the connection for 127.0.0.1
21:38:10 INFO  : disconnect
21:39:55 INFO  : connect -url tcp:127.0.0.1:3121
21:39:55 INFO  : bpremove -all
21:39:55 INFO  : Context for 'APU' is selected.
21:39:55 INFO  : System reset is completed.
21:39:58 INFO  : 'after 3000' command is executed.
21:39:58 INFO  : 'targets -set -filter {jtag_cable_name =~ "Digilent JTAG-SMT2 210251A08870" && level==0 && jtag_device_ctx=="jsn-JTAG-SMT2-210251A08870-23727093-0"}' command is executed.
21:40:00 INFO  : Device configured successfully with "C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/hello_world/_ide/bitstream/ZynqDesign_wrapper.bit"
21:40:00 INFO  : targets -set -nocase -filter {name =~"APU*"}
21:40:01 INFO  : loadhw -hw C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/platform/export/platform/hw/ZynqDesign_wrapper.xsa -mem-ranges [list {0x40000000 0xbfffffff}]
21:40:01 INFO  : configparams force-mem-access 1
21:40:01 INFO  : targets -set -nocase -filter {name =~"APU*"}
21:40:01 INFO  : source C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/hello_world/_ide/psinit/ps7_init.tcl
21:40:02 INFO  : ps7_init
21:40:02 INFO  : ps7_post_config
21:40:02 INFO  : targets -set -nocase -filter {name =~ "*A9*#0"}
21:40:02 INFO  : rst -processor
21:40:02 INFO  : dow C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/workspace/TA/arm_xc7z0x0_le_hard/make/TA.elf
21:40:02 INFO  : bpadd main
21:40:02 INFO  : con
21:40:02 INFO  : configparams force-mem-access 0
21:40:03 INFO  : Testing the connection for 127.0.0.1
21:41:56 INFO  : disconnect
21:41:56 INFO  : connect -url tcp:127.0.0.1:3121
21:41:56 INFO  : bpremove -all
21:41:57 INFO  : Context for 'APU' is selected.
21:41:57 INFO  : System reset is completed.
21:42:00 INFO  : 'after 3000' command is executed.
21:42:00 INFO  : 'targets -set -filter {jtag_cable_name =~ "Digilent JTAG-SMT2 210251A08870" && level==0 && jtag_device_ctx=="jsn-JTAG-SMT2-210251A08870-23727093-0"}' command is executed.
21:42:02 INFO  : Device configured successfully with "C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/hello_world/_ide/bitstream/ZynqDesign_wrapper.bit"
21:42:02 INFO  : targets -set -nocase -filter {name =~"APU*"}
21:42:03 INFO  : loadhw -hw C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/platform/export/platform/hw/ZynqDesign_wrapper.xsa -mem-ranges [list {0x40000000 0xbfffffff}]
21:42:03 INFO  : configparams force-mem-access 1
21:42:03 INFO  : targets -set -nocase -filter {name =~"APU*"}
21:42:03 INFO  : source C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/hello_world/_ide/psinit/ps7_init.tcl
21:42:03 INFO  : ps7_init
21:42:03 INFO  : ps7_post_config
21:42:03 INFO  : targets -set -nocase -filter {name =~ "*A9*#0"}
21:42:04 INFO  : rst -processor
21:42:04 INFO  : dow C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/workspace/TA/arm_xc7z0x0_le_hard/make/TA.elf
21:42:04 INFO  : bpadd main
21:42:04 INFO  : con
21:42:04 INFO  : configparams force-mem-access 0
21:42:05 INFO  : Testing the connection for 127.0.0.1
21:43:47 INFO  : disconnect
21:43:48 INFO  : connect -url tcp:127.0.0.1:3121
21:43:48 INFO  : bpremove -all
21:43:48 INFO  : Context for 'APU' is selected.
21:43:48 INFO  : System reset is completed.
21:43:51 INFO  : 'after 3000' command is executed.
21:43:51 INFO  : 'targets -set -filter {jtag_cable_name =~ "Digilent JTAG-SMT2 210251A08870" && level==0 && jtag_device_ctx=="jsn-JTAG-SMT2-210251A08870-23727093-0"}' command is executed.
21:43:53 INFO  : Device configured successfully with "C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/hello_world/_ide/bitstream/ZynqDesign_wrapper.bit"
21:43:53 INFO  : targets -set -nocase -filter {name =~"APU*"}
21:43:54 INFO  : loadhw -hw C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/platform/export/platform/hw/ZynqDesign_wrapper.xsa -mem-ranges [list {0x40000000 0xbfffffff}]
21:43:54 INFO  : configparams force-mem-access 1
21:43:54 INFO  : targets -set -nocase -filter {name =~"APU*"}
21:43:54 INFO  : source C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/hello_world/_ide/psinit/ps7_init.tcl
21:43:55 INFO  : ps7_init
21:43:55 INFO  : ps7_post_config
21:43:55 INFO  : targets -set -nocase -filter {name =~ "*A9*#0"}
21:43:55 INFO  : rst -processor
21:43:55 INFO  : dow C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/workspace/TA/arm_xc7z0x0_le_hard/make/TA.elf
21:43:55 INFO  : bpadd main
21:43:55 INFO  : con
21:43:55 INFO  : configparams force-mem-access 0
21:43:56 INFO  : Testing the connection for 127.0.0.1
21:44:06 INFO  : disconnect
21:44:07 INFO  : connect -url tcp:127.0.0.1:3121
21:44:07 INFO  : bpremove -all
21:44:07 INFO  : Context for 'APU' is selected.
21:44:07 INFO  : System reset is completed.
21:44:10 INFO  : 'after 3000' command is executed.
21:44:10 INFO  : 'targets -set -filter {jtag_cable_name =~ "Digilent JTAG-SMT2 210251A08870" && level==0 && jtag_device_ctx=="jsn-JTAG-SMT2-210251A08870-23727093-0"}' command is executed.
21:44:13 INFO  : Device configured successfully with "C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/hello_world/_ide/bitstream/ZynqDesign_wrapper.bit"
21:44:13 INFO  : targets -set -nocase -filter {name =~"APU*"}
21:44:14 INFO  : loadhw -hw C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/platform/export/platform/hw/ZynqDesign_wrapper.xsa -mem-ranges [list {0x40000000 0xbfffffff}]
21:44:14 INFO  : configparams force-mem-access 1
21:44:14 INFO  : targets -set -nocase -filter {name =~"APU*"}
21:44:14 INFO  : source C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/hello_world/_ide/psinit/ps7_init.tcl
21:44:14 INFO  : ps7_init
21:44:14 INFO  : ps7_post_config
21:44:14 INFO  : targets -set -nocase -filter {name =~ "*A9*#0"}
21:44:14 INFO  : rst -processor
21:44:15 INFO  : dow C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/workspace/TA/arm_xc7z0x0_le_hard/make/TA.elf
21:44:15 INFO  : bpadd main
21:44:15 INFO  : con
21:44:15 INFO  : configparams force-mem-access 0
21:44:16 INFO  : Testing the connection for 127.0.0.1
21:45:18 INFO  : disconnect
21:45:19 INFO  : connect -url tcp:127.0.0.1:3121
21:45:19 INFO  : bpremove -all
21:45:19 INFO  : Context for 'APU' is selected.
21:45:19 INFO  : System reset is completed.
21:45:22 INFO  : 'after 3000' command is executed.
21:45:22 INFO  : 'targets -set -filter {jtag_cable_name =~ "Digilent JTAG-SMT2 210251A08870" && level==0 && jtag_device_ctx=="jsn-JTAG-SMT2-210251A08870-23727093-0"}' command is executed.
21:45:24 INFO  : Device configured successfully with "C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/hello_world/_ide/bitstream/ZynqDesign_wrapper.bit"
21:45:24 INFO  : targets -set -nocase -filter {name =~"APU*"}
21:45:26 INFO  : loadhw -hw C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/platform/export/platform/hw/ZynqDesign_wrapper.xsa -mem-ranges [list {0x40000000 0xbfffffff}]
21:45:26 INFO  : configparams force-mem-access 1
21:45:26 INFO  : targets -set -nocase -filter {name =~"APU*"}
21:45:26 INFO  : source C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/hello_world/_ide/psinit/ps7_init.tcl
21:45:26 INFO  : ps7_init
21:45:26 INFO  : ps7_post_config
21:45:26 INFO  : targets -set -nocase -filter {name =~ "*A9*#0"}
21:45:26 INFO  : rst -processor
21:45:26 INFO  : dow C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/workspace/TA/arm_xc7z0x0_le_hard/make/TA.elf
21:45:26 INFO  : bpadd main
21:45:26 INFO  : con
21:45:26 INFO  : configparams force-mem-access 0
21:45:27 INFO  : Testing the connection for 127.0.0.1
21:45:46 INFO  : disconnect
21:45:47 INFO  : connect -url tcp:127.0.0.1:3121
21:45:47 INFO  : bpremove -all
21:45:48 INFO  : Context for 'APU' is selected.
21:45:48 INFO  : System reset is completed.
21:45:51 INFO  : 'after 3000' command is executed.
21:45:51 INFO  : 'targets -set -filter {jtag_cable_name =~ "Digilent JTAG-SMT2 210251A08870" && level==0 && jtag_device_ctx=="jsn-JTAG-SMT2-210251A08870-23727093-0"}' command is executed.
21:45:53 INFO  : Device configured successfully with "C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/hello_world/_ide/bitstream/ZynqDesign_wrapper.bit"
21:45:53 INFO  : targets -set -nocase -filter {name =~"APU*"}
21:45:55 INFO  : loadhw -hw C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/platform/export/platform/hw/ZynqDesign_wrapper.xsa -mem-ranges [list {0x40000000 0xbfffffff}]
21:45:55 INFO  : configparams force-mem-access 1
21:45:55 INFO  : targets -set -nocase -filter {name =~"APU*"}
21:45:55 INFO  : source C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/hello_world/_ide/psinit/ps7_init.tcl
21:45:55 INFO  : ps7_init
21:45:55 INFO  : ps7_post_config
21:45:55 INFO  : targets -set -nocase -filter {name =~ "*A9*#0"}
21:45:55 INFO  : rst -processor
21:45:55 INFO  : dow C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/workspace/TA/arm_xc7z0x0_le_hard/make/TA.elf
21:45:55 INFO  : bpadd main
21:45:55 INFO  : con
21:45:55 INFO  : configparams force-mem-access 0
21:45:56 INFO  : Testing the connection for 127.0.0.1
21:51:11 INFO  : disconnect
21:51:15 INFO  : The updated bitstream files are copied from platform to folder 'C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\hello_world\_ide\bitstream' in project 'C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\hello_world'.
21:51:15 INFO  : The updated ps init files are copied from platform to folder 'C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\hello_world\_ide\psinit' in project 'C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\hello_world'.
21:52:32 INFO  : connect -url tcp:127.0.0.1:3121
21:52:32 INFO  : bpremove -all
21:52:32 INFO  : Context for 'APU' is selected.
21:52:32 INFO  : System reset is completed.
21:52:35 INFO  : 'after 3000' command is executed.
21:52:35 INFO  : 'targets -set -filter {jtag_cable_name =~ "Digilent JTAG-SMT2 210251A08870" && level==0 && jtag_device_ctx=="jsn-JTAG-SMT2-210251A08870-23727093-0"}' command is executed.
21:52:38 INFO  : Device configured successfully with "C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/hello_world/_ide/bitstream/ZynqDesign_wrapper.bit"
21:52:38 INFO  : targets -set -nocase -filter {name =~"APU*"}
21:52:39 INFO  : loadhw -hw C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/platform/export/platform/hw/ZynqDesign_wrapper.xsa -mem-ranges [list {0x40000000 0xbfffffff}]
21:52:39 INFO  : configparams force-mem-access 1
21:52:39 INFO  : targets -set -nocase -filter {name =~"APU*"}
21:52:39 INFO  : source C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/hello_world/_ide/psinit/ps7_init.tcl
21:52:40 INFO  : ps7_init
21:52:40 INFO  : ps7_post_config
21:52:40 INFO  : targets -set -nocase -filter {name =~ "*A9*#0"}
21:52:40 INFO  : rst -processor
21:52:40 INFO  : dow C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/workspace/TA/arm_xc7z0x0_le_hard/make/TA.elf
21:52:40 INFO  : bpadd main
21:52:40 INFO  : con
21:52:40 INFO  : configparams force-mem-access 0
21:52:41 INFO  : Testing the connection for 127.0.0.1
21:55:16 INFO  : disconnect
21:55:19 INFO  : The updated bitstream files are copied from platform to folder 'C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\hello_world\_ide\bitstream' in project 'C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\hello_world'.
21:55:19 INFO  : The updated ps init files are copied from platform to folder 'C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\hello_world\_ide\psinit' in project 'C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\hello_world'.
21:56:15 INFO  : connect -url tcp:127.0.0.1:3121
21:56:15 INFO  : bpremove -all
21:56:15 INFO  : Context for 'APU' is selected.
21:56:15 INFO  : System reset is completed.
21:56:18 INFO  : 'after 3000' command is executed.
21:56:18 INFO  : 'targets -set -filter {jtag_cable_name =~ "Digilent JTAG-SMT2 210251A08870" && level==0 && jtag_device_ctx=="jsn-JTAG-SMT2-210251A08870-23727093-0"}' command is executed.
21:56:20 INFO  : Device configured successfully with "C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/hello_world/_ide/bitstream/ZynqDesign_wrapper.bit"
21:56:20 INFO  : targets -set -nocase -filter {name =~"APU*"}
21:56:21 INFO  : loadhw -hw C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/platform/export/platform/hw/ZynqDesign_wrapper.xsa -mem-ranges [list {0x40000000 0xbfffffff}]
21:56:21 INFO  : configparams force-mem-access 1
21:56:21 INFO  : targets -set -nocase -filter {name =~"APU*"}
21:56:21 INFO  : source C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/hello_world/_ide/psinit/ps7_init.tcl
21:56:21 INFO  : ps7_init
21:56:21 INFO  : ps7_post_config
21:56:21 INFO  : targets -set -nocase -filter {name =~ "*A9*#0"}
21:56:21 INFO  : rst -processor
21:56:22 INFO  : dow C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/workspace/TA/arm_xc7z0x0_le_hard/make/TA.elf
21:56:22 INFO  : bpadd main
21:56:22 INFO  : con
21:56:22 INFO  : configparams force-mem-access 0
21:56:23 INFO  : Testing the connection for 127.0.0.1
21:56:35 INFO  : disconnect
21:57:26 INFO  : lopper command to import example :python C:\Xilinx\VitisEmbedded\Vitis\2024.2\data\embeddedsw/scripts/pyesw//create_example.py -d C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\platform\export\platform\sw\standalone_ps7_cortexa9_0 -e xsdps_raw_example.c -n ps7_sd_0 -w C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\xsdps_raw_example -r C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\_ide\.wsdata\.repo.yaml
21:57:26 INFO  : cmd.exe, /C, C:\Xilinx\VitisEmbedded\Vitis\2024.2\tps\win64\lopper-1.1.0\env\Scripts\activate.bat && python C:\Xilinx\VitisEmbedded\Vitis\2024.2\data\embeddedsw\scripts\pyesw\\create_example.py -d C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\platform\export\platform\sw\standalone_ps7_cortexa9_0 -e xsdps_raw_example.c -n ps7_sd_0 -w C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\xsdps_raw_example -r C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\_ide\.wsdata\.repo.yaml && C:\Xilinx\VitisEmbedded\Vitis\2024.2\tps\win64\lopper-1.1.0\env\Scripts\deactivate.bat
21:57:28 INFO  : Successfully Created Application sources at C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/xsdps_raw_example/src
21:57:28 INFO  : Successfully imported example xsdps_raw_example.c on C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\xsdps_raw_example
21:57:28 INFO  : The hardware specification used by project 'C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\xsdps_raw_example' is out of sync with the platform. Resource files extracted from the hardware specification will be updated.
21:57:28 INFO  : The updated bitstream files are copied from platform to folder 'C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\xsdps_raw_example\_ide\bitstream' in project 'C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\xsdps_raw_example'.
21:57:28 INFO  : The updated ps init files are copied from platform to folder 'C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\xsdps_raw_example\_ide\psinit' in project 'C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\xsdps_raw_example'.
21:57:28 INFO  : created .gitignore file for the project C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\xsdps_raw_example
22:18:59 INFO  : The updated bitstream files are copied from platform to folder 'C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\xsdps_raw_example\_ide\bitstream' in project 'C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\xsdps_raw_example'.
22:18:59 INFO  : The updated ps init files are copied from platform to folder 'C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\xsdps_raw_example\_ide\psinit' in project 'C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\xsdps_raw_example'.
22:20:42 INFO  : connect -url tcp:127.0.0.1:3121
22:20:42 INFO  : bpremove -all
22:20:42 INFO  : Context for 'APU' is selected.
22:20:42 INFO  : System reset is completed.
22:20:45 INFO  : 'after 3000' command is executed.
22:20:45 INFO  : 'targets -set -filter {jtag_cable_name =~ "Digilent JTAG-SMT2 210251A08870" && level==0 && jtag_device_ctx=="jsn-JTAG-SMT2-210251A08870-23727093-0"}' command is executed.
22:20:47 INFO  : Device configured successfully with "C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/hello_world/_ide/bitstream/ZynqDesign_wrapper.bit"
22:20:47 INFO  : targets -set -nocase -filter {name =~"APU*"}
22:20:49 INFO  : loadhw -hw C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/platform/export/platform/hw/ZynqDesign_wrapper.xsa -mem-ranges [list {0x40000000 0xbfffffff}]
22:20:49 INFO  : configparams force-mem-access 1
22:20:49 INFO  : targets -set -nocase -filter {name =~"APU*"}
22:20:49 INFO  : source C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/hello_world/_ide/psinit/ps7_init.tcl
22:20:49 INFO  : ps7_init
22:20:49 INFO  : ps7_post_config
22:20:49 INFO  : targets -set -nocase -filter {name =~ "*A9*#0"}
22:20:49 INFO  : rst -processor
22:20:50 INFO  : dow C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/workspace/TA/arm_xc7z0x0_le_hard/make/TA.elf
22:20:50 INFO  : bpadd main
22:20:50 INFO  : con
22:20:50 INFO  : configparams force-mem-access 0
22:20:51 INFO  : Testing the connection for 127.0.0.1
22:23:49 INFO  : The updated bitstream files are copied from platform to folder 'C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\xsdps_raw_example\_ide\bitstream' in project 'C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\xsdps_raw_example'.
22:23:49 INFO  : The updated ps init files are copied from platform to folder 'C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\xsdps_raw_example\_ide\psinit' in project 'C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\xsdps_raw_example'.
22:24:17 INFO  : disconnect
22:24:23 INFO  : connect -url tcp:127.0.0.1:3121
22:24:23 INFO  : bpremove -all
22:24:23 INFO  : Context for 'APU' is selected.
22:24:23 INFO  : System reset is completed.
22:24:26 INFO  : 'after 3000' command is executed.
22:24:26 INFO  : 'targets -set -filter {jtag_cable_name =~ "Digilent JTAG-SMT2 210251A08870" && level==0 && jtag_device_ctx=="jsn-JTAG-SMT2-210251A08870-23727093-0"}' command is executed.
22:24:28 INFO  : Device configured successfully with "C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/hello_world/_ide/bitstream/ZynqDesign_wrapper.bit"
22:24:28 INFO  : targets -set -nocase -filter {name =~"APU*"}
22:24:29 INFO  : loadhw -hw C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/platform/export/platform/hw/ZynqDesign_wrapper.xsa -mem-ranges [list {0x40000000 0xbfffffff}]
22:24:29 INFO  : configparams force-mem-access 1
22:24:29 INFO  : targets -set -nocase -filter {name =~"APU*"}
22:24:29 INFO  : source C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/hello_world/_ide/psinit/ps7_init.tcl
22:24:30 INFO  : ps7_init
22:24:30 INFO  : ps7_post_config
22:24:30 INFO  : targets -set -nocase -filter {name =~ "*A9*#0"}
22:24:30 INFO  : rst -processor
22:24:30 INFO  : dow C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/workspace/TA/arm_xc7z0x0_le_hard/make/TA.elf
22:24:30 INFO  : bpadd main
22:24:30 INFO  : con
22:24:30 INFO  : configparams force-mem-access 0
22:24:31 INFO  : Testing the connection for 127.0.0.1
22:24:44 INFO  : disconnect
22:24:45 INFO  : connect -url tcp:127.0.0.1:3121
22:24:45 INFO  : bpremove -all
22:24:45 INFO  : Context for 'APU' is selected.
22:24:45 INFO  : System reset is completed.
22:24:48 INFO  : 'after 3000' command is executed.
22:24:48 INFO  : 'targets -set -filter {jtag_cable_name =~ "Digilent JTAG-SMT2 210251A08870" && level==0 && jtag_device_ctx=="jsn-JTAG-SMT2-210251A08870-23727093-0"}' command is executed.
22:24:50 INFO  : Device configured successfully with "C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/hello_world/_ide/bitstream/ZynqDesign_wrapper.bit"
22:24:51 INFO  : targets -set -nocase -filter {name =~"APU*"}
22:24:52 INFO  : loadhw -hw C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/platform/export/platform/hw/ZynqDesign_wrapper.xsa -mem-ranges [list {0x40000000 0xbfffffff}]
22:24:52 INFO  : configparams force-mem-access 1
22:24:52 INFO  : targets -set -nocase -filter {name =~"APU*"}
22:24:52 INFO  : source C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/hello_world/_ide/psinit/ps7_init.tcl
22:24:52 INFO  : ps7_init
22:24:52 INFO  : ps7_post_config
22:24:52 INFO  : targets -set -nocase -filter {name =~ "*A9*#0"}
22:24:52 INFO  : rst -processor
22:24:52 INFO  : dow C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/workspace/TA/arm_xc7z0x0_le_hard/make/TA.elf
22:24:53 INFO  : bpadd main
22:24:53 INFO  : con
22:24:53 INFO  : configparams force-mem-access 0
22:24:54 INFO  : Testing the connection for 127.0.0.1
22:26:10 INFO  : disconnect
22:37:34 INFO  : The updated bitstream files are copied from platform to folder 'C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\xsdps_raw_example\_ide\bitstream' in project 'C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\xsdps_raw_example'.
22:37:34 INFO  : The updated ps init files are copied from platform to folder 'C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\xsdps_raw_example\_ide\psinit' in project 'C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\xsdps_raw_example'.
22:39:19 INFO  : connect -url tcp:127.0.0.1:3121
22:39:19 INFO  : bpremove -all
22:39:19 INFO  : Context for 'APU' is selected.
22:39:19 INFO  : System reset is completed.
22:39:22 INFO  : 'after 3000' command is executed.
22:39:22 INFO  : 'targets -set -filter {jtag_cable_name =~ "Digilent JTAG-SMT2 210251A08870" && level==0 && jtag_device_ctx=="jsn-JTAG-SMT2-210251A08870-23727093-0"}' command is executed.
22:39:25 INFO  : Device configured successfully with "C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/hello_world/_ide/bitstream/ZynqDesign_wrapper.bit"
22:39:25 INFO  : targets -set -nocase -filter {name =~"APU*"}
22:39:26 INFO  : loadhw -hw C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/platform/export/platform/hw/ZynqDesign_wrapper.xsa -mem-ranges [list {0x40000000 0xbfffffff}]
22:39:26 INFO  : configparams force-mem-access 1
22:39:26 INFO  : targets -set -nocase -filter {name =~"APU*"}
22:39:26 INFO  : source C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/hello_world/_ide/psinit/ps7_init.tcl
22:39:26 INFO  : ps7_init
22:39:26 INFO  : ps7_post_config
22:39:26 INFO  : targets -set -nocase -filter {name =~ "*A9*#0"}
22:39:26 INFO  : rst -processor
22:39:27 INFO  : dow C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/workspace/TA/arm_xc7z0x0_le_hard/make/TA.elf
22:39:27 INFO  : bpadd main
22:39:27 INFO  : con
22:39:27 INFO  : configparams force-mem-access 0
22:39:28 INFO  : Testing the connection for 127.0.0.1
22:39:51 INFO  : disconnect
22:42:30 INFO  : The updated bitstream files are copied from platform to folder 'C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\hello_world\_ide\bitstream' in project 'C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\hello_world'.
22:42:30 INFO  : The updated ps init files are copied from platform to folder 'C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\hello_world\_ide\psinit' in project 'C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\hello_world'.
22:44:38 INFO  : Updating Examples in Driver C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\platform\zynq_fsbl\zynq_fsbl_bsp
22:44:38 INFO  : cmd.exe, /C, C:\Xilinx\VitisEmbedded\Vitis\2024.2\tps\win64\lopper-1.1.0\env\Scripts\activate.bat && python C:\Xilinx\VitisEmbedded\Vitis\2024.2\data\embeddedsw\scripts\pyesw\\load_example.py -d C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\platform\zynq_fsbl\zynq_fsbl_bsp && C:\Xilinx\VitisEmbedded\Vitis\2024.2\tps\win64\lopper-1.1.0\env\Scripts\deactivate.bat
22:44:41 INFO  : -- The C compiler identification is GNU 13.3.0
22:44:41 INFO  : -- The CXX compiler identification is GNU 13.3.0
22:44:41 INFO  : -- Detecting C compiler ABI info
22:44:41 INFO  : -- Detecting C compiler ABI info - done
22:44:41 INFO  : -- Check for working C compiler: C:/Xilinx/VitisEmbedded/Vitis/2024.2/gnu/aarch32/nt/gcc-arm-none-eabi/bin/arm-none-eabi-gcc.exe - skipped
22:44:41 INFO  : -- Detecting C compile features
22:44:41 INFO  : -- Detecting C compile features - done
22:44:41 INFO  : -- Detecting CXX compiler ABI info
22:44:41 INFO  : -- Detecting CXX compiler ABI info - done
22:44:41 INFO  : -- Check for working CXX compiler: C:/Xilinx/VitisEmbedded/Vitis/2024.2/gnu/aarch32/nt/gcc-arm-none-eabi/bin/arm-none-eabi-g++.exe - skipped
22:44:41 INFO  : -- Detecting CXX compile features
22:44:41 INFO  : -- Detecting CXX compile features - done
22:44:41 INFO  : -- Configuring done
22:44:41 INFO  : -- Generating done
22:44:41 INFO  : CMake Warning:
22:44:41 INFO  :   Manually-specified variables were not used by the project:
22:44:41 INFO  : 
22:44:41 INFO  :     CMAKE_LIBRARY_PATH
22:44:41 INFO  : 
22:44:41 INFO  : 
22:44:41 INFO  : -- Build files have been written to: C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/platform/zynq_fsbl/zynq_fsbl_bsp/libsrc/build_configs/exmetadata
22:44:41 INFO  : Updated Examples in Driver
22:44:56 INFO  : Found no platform with name 'ZynqDesign_wrapper' in install repositories
22:44:56 INFO  : HW XSA update started
22:44:56 INFO  : Using sdt generation script from C:\Xilinx\VitisEmbedded\Vitis\2024.2\vitis-server\scripts\platformutil.tcl
22:44:56 INFO  : cmd.exe, /C, C:\Xilinx\VitisEmbedded\Vitis\2024.2\tps\win64\lopper-1.1.0\env\Scripts\activate.bat && xsct.bat C:\Xilinx\VitisEmbedded\Vitis\2024.2\vitis-server\scripts\platformutil.tcl C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vivado\vivado_prj\ZynqDesign_wrapper.xsa C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\platform\hw\sdt && C:\Xilinx\VitisEmbedded\Vitis\2024.2\tps\win64\lopper-1.1.0\env\Scripts\deactivate.bat
22:44:59 INFO  : SDT generated successfully
22:44:59 INFO  : Generating BSP for the domain C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\platform\zynq_fsbl\zynq_fsbl_bsp
22:44:59 INFO  : lopper command to reconfigure BSP :python C:\Xilinx\VitisEmbedded\Vitis\2024.2\data\embeddedsw/scripts/pyesw//reconfig_bsp.py -d C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\platform\zynq_fsbl\zynq_fsbl_bsp
22:44:59 INFO  : cmd.exe, /C, C:\Xilinx\VitisEmbedded\Vitis\2024.2\tps\win64\lopper-1.1.0\env\Scripts\activate.bat && python C:\Xilinx\VitisEmbedded\Vitis\2024.2\data\embeddedsw\scripts\pyesw\\reconfig_bsp.py -d C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\platform\zynq_fsbl\zynq_fsbl_bsp && C:\Xilinx\VitisEmbedded\Vitis\2024.2\tps\win64\lopper-1.1.0\env\Scripts\deactivate.bat
22:44:59 INFO  : Domain reconfigured successfully
22:44:59 INFO  : lopper command to regenerate BSP :python C:\Xilinx\VitisEmbedded\Vitis\2024.2\data\embeddedsw/scripts/pyesw//regen_bsp.py -s C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\platform\hw\sdt\system-top.dts -d C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\platform\zynq_fsbl\zynq_fsbl_bsp -r C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\_ide\.wsdata\.repo.yaml
22:44:59 INFO  : cmd.exe, /C, C:\Xilinx\VitisEmbedded\Vitis\2024.2\tps\win64\lopper-1.1.0\env\Scripts\activate.bat && python C:\Xilinx\VitisEmbedded\Vitis\2024.2\data\embeddedsw\scripts\pyesw\\regen_bsp.py -s C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\platform\hw\sdt\system-top.dts -d C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\platform\zynq_fsbl\zynq_fsbl_bsp -r C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\_ide\.wsdata\.repo.yaml && C:\Xilinx\VitisEmbedded\Vitis\2024.2\tps\win64\lopper-1.1.0\env\Scripts\deactivate.bat
22:45:06 INFO  : -- Configuring done
22:45:06 INFO  : -- Generating done
22:45:06 INFO  : CMake Warning:
22:45:06 INFO  :   Manually-specified variables were not used by the project:
22:45:06 INFO  : 
22:45:06 INFO  :     CMAKE_TOOLCHAIN_FILE
22:45:06 INFO  : 
22:45:06 INFO  : 
22:45:06 INFO  : -- Build files have been written to: C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/platform/zynq_fsbl/zynq_fsbl_bsp/libsrc/build_configs/gen_bsp
22:45:06 INFO  : Successfully created Domain at C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\platform\zynq_fsbl\zynq_fsbl_bsp
22:45:06 INFO  : BSP regenerated successfully
22:45:06 INFO  : Generating BSP for the domain C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\platform\ps7_cortexa9_0\standalone_ps7_cortexa9_0\bsp
22:45:06 INFO  : lopper command to reconfigure BSP :python C:\Xilinx\VitisEmbedded\Vitis\2024.2\data\embeddedsw/scripts/pyesw//reconfig_bsp.py -d C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\platform\ps7_cortexa9_0\standalone_ps7_cortexa9_0\bsp
22:45:06 INFO  : cmd.exe, /C, C:\Xilinx\VitisEmbedded\Vitis\2024.2\tps\win64\lopper-1.1.0\env\Scripts\activate.bat && python C:\Xilinx\VitisEmbedded\Vitis\2024.2\data\embeddedsw\scripts\pyesw\\reconfig_bsp.py -d C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\platform\ps7_cortexa9_0\standalone_ps7_cortexa9_0\bsp && C:\Xilinx\VitisEmbedded\Vitis\2024.2\tps\win64\lopper-1.1.0\env\Scripts\deactivate.bat
22:45:06 INFO  : Domain reconfigured successfully
22:45:06 INFO  : lopper command to regenerate BSP :python C:\Xilinx\VitisEmbedded\Vitis\2024.2\data\embeddedsw/scripts/pyesw//regen_bsp.py -s C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\platform\hw\sdt\system-top.dts -d C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\platform\ps7_cortexa9_0\standalone_ps7_cortexa9_0\bsp -r C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\_ide\.wsdata\.repo.yaml
22:45:06 INFO  : cmd.exe, /C, C:\Xilinx\VitisEmbedded\Vitis\2024.2\tps\win64\lopper-1.1.0\env\Scripts\activate.bat && python C:\Xilinx\VitisEmbedded\Vitis\2024.2\data\embeddedsw\scripts\pyesw\\regen_bsp.py -s C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\platform\hw\sdt\system-top.dts -d C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\platform\ps7_cortexa9_0\standalone_ps7_cortexa9_0\bsp -r C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\_ide\.wsdata\.repo.yaml && C:\Xilinx\VitisEmbedded\Vitis\2024.2\tps\win64\lopper-1.1.0\env\Scripts\deactivate.bat
22:45:13 INFO  : -- Configuring done
22:45:13 INFO  : -- Generating done
22:45:13 INFO  : CMake Warning:
22:45:13 INFO  :   Manually-specified variables were not used by the project:
22:45:13 INFO  : 
22:45:13 INFO  :     CMAKE_TOOLCHAIN_FILE
22:45:13 INFO  : 
22:45:13 INFO  : 
22:45:13 INFO  : -- Build files have been written to: C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/platform/ps7_cortexa9_0/standalone_ps7_cortexa9_0/bsp/libsrc/build_configs/gen_bsp
22:45:13 INFO  : Successfully created Domain at C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\platform\ps7_cortexa9_0\standalone_ps7_cortexa9_0\bsp
22:45:13 INFO  : BSP regenerated successfully
22:45:13 INFO  : Hardware XSA update finished successfully
22:45:16 INFO  : BSP build will be initiated for Domain zynq_fsbl
22:45:16 INFO  : Generating BSP for the domain C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\platform\zynq_fsbl\zynq_fsbl_bsp
22:45:16 INFO  : lopper command to reconfigure BSP :python C:\Xilinx\VitisEmbedded\Vitis\2024.2\data\embeddedsw/scripts/pyesw//reconfig_bsp.py -d C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\platform\zynq_fsbl\zynq_fsbl_bsp
22:45:16 INFO  : cmd.exe, /C, C:\Xilinx\VitisEmbedded\Vitis\2024.2\tps\win64\lopper-1.1.0\env\Scripts\activate.bat && python C:\Xilinx\VitisEmbedded\Vitis\2024.2\data\embeddedsw\scripts\pyesw\\reconfig_bsp.py -d C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\platform\zynq_fsbl\zynq_fsbl_bsp && C:\Xilinx\VitisEmbedded\Vitis\2024.2\tps\win64\lopper-1.1.0\env\Scripts\deactivate.bat
22:45:16 INFO  : BSP build will be initiated for Domain standalone_ps7_cortexa9_0
22:45:16 INFO  : Generating BSP for the domain C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\platform\ps7_cortexa9_0\standalone_ps7_cortexa9_0\bsp
22:45:16 INFO  : lopper command to reconfigure BSP :python C:\Xilinx\VitisEmbedded\Vitis\2024.2\data\embeddedsw/scripts/pyesw//reconfig_bsp.py -d C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\platform\ps7_cortexa9_0\standalone_ps7_cortexa9_0\bsp
22:45:16 INFO  : cmd.exe, /C, C:\Xilinx\VitisEmbedded\Vitis\2024.2\tps\win64\lopper-1.1.0\env\Scripts\activate.bat && python C:\Xilinx\VitisEmbedded\Vitis\2024.2\data\embeddedsw\scripts\pyesw\\reconfig_bsp.py -d C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\platform\ps7_cortexa9_0\standalone_ps7_cortexa9_0\bsp && C:\Xilinx\VitisEmbedded\Vitis\2024.2\tps\win64\lopper-1.1.0\env\Scripts\deactivate.bat
22:45:16 INFO  : Domain reconfigured successfully
22:45:16 INFO  : Generating BSP for the domain C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\platform\ps7_cortexa9_0\standalone_ps7_cortexa9_0\bsp
22:45:16 INFO  : lopper command to build BSP :python C:\Xilinx\VitisEmbedded\Vitis\2024.2\data\embeddedsw/scripts/pyesw//build_bsp.py -d C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\platform\ps7_cortexa9_0\standalone_ps7_cortexa9_0\bsp
22:45:16 INFO  : cmd.exe, /C, C:\Xilinx\VitisEmbedded\Vitis\2024.2\tps\win64\lopper-1.1.0\env\Scripts\activate.bat && python C:\Xilinx\VitisEmbedded\Vitis\2024.2\data\embeddedsw\scripts\pyesw\\build_bsp.py -d C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\platform\ps7_cortexa9_0\standalone_ps7_cortexa9_0\bsp && C:\Xilinx\VitisEmbedded\Vitis\2024.2\tps\win64\lopper-1.1.0\env\Scripts\deactivate.bat
22:45:16 INFO  : Domain reconfigured successfully
22:45:16 INFO  : Generating BSP for the domain C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\platform\zynq_fsbl\zynq_fsbl_bsp
22:45:16 INFO  : lopper command to build BSP :python C:\Xilinx\VitisEmbedded\Vitis\2024.2\data\embeddedsw/scripts/pyesw//build_bsp.py -d C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\platform\zynq_fsbl\zynq_fsbl_bsp
22:45:16 INFO  : cmd.exe, /C, C:\Xilinx\VitisEmbedded\Vitis\2024.2\tps\win64\lopper-1.1.0\env\Scripts\activate.bat && python C:\Xilinx\VitisEmbedded\Vitis\2024.2\data\embeddedsw\scripts\pyesw\\build_bsp.py -d C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\platform\zynq_fsbl\zynq_fsbl_bsp && C:\Xilinx\VitisEmbedded\Vitis\2024.2\tps\win64\lopper-1.1.0\env\Scripts\deactivate.bat
22:45:21 INFO  : Domain build successfully
22:45:21 INFO  : Domain build successfully
22:45:21 INFO  : Generating Boot ELFs
22:45:21 INFO  : lopper command to retarget baremetal application :python C:\Xilinx\VitisEmbedded\Vitis\2024.2\data\embeddedsw/scripts/pyesw//retarget_app.py -s C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\platform\zynq_fsbl -d C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\platform\zynq_fsbl\zynq_fsbl_bsp -b C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\platform\zynq_fsbl\build
22:45:21 INFO  : cmd.exe, /C, C:\Xilinx\VitisEmbedded\Vitis\2024.2\tps\win64\lopper-1.1.0\env\Scripts\activate.bat && python C:\Xilinx\VitisEmbedded\Vitis\2024.2\data\embeddedsw\scripts\pyesw\\retarget_app.py -s C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\platform\zynq_fsbl -d C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\platform\zynq_fsbl\zynq_fsbl_bsp -b C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\platform\zynq_fsbl\build && C:\Xilinx\VitisEmbedded\Vitis\2024.2\tps\win64\lopper-1.1.0\env\Scripts\deactivate.bat
22:45:21 INFO  : Successfully retargeted Application C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\platform\zynq_fsbl
22:45:21 INFO  : lopper command to build baremetal application :python C:\Xilinx\VitisEmbedded\Vitis\2024.2\data\embeddedsw/scripts/pyesw//build_app.py -s C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\platform\zynq_fsbl -b C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\platform\zynq_fsbl\build
22:45:21 INFO  : cmd.exe, /C, C:\Xilinx\VitisEmbedded\Vitis\2024.2\tps\win64\lopper-1.1.0\env\Scripts\activate.bat && python C:\Xilinx\VitisEmbedded\Vitis\2024.2\data\embeddedsw\scripts\pyesw\\build_app.py -s C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\platform\zynq_fsbl -b C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\platform\zynq_fsbl\build && C:\Xilinx\VitisEmbedded\Vitis\2024.2\tps\win64\lopper-1.1.0\env\Scripts\deactivate.bat
22:45:23 INFO  : Successfully building the baremetal applicationC:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\platform\zynq_fsbl\build
22:45:23 INFO  : Generating Export directory
22:45:23 INFO  : Platform Build Finished successfully.
22:47:40 INFO  : connection terminated
22:47:40 INFO  : connection terminated
22:47:40 INFO  : scheduling shutdown timer for 2 seconds ...
16:30:29 INFO  : updated workspace path: C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis
16:30:29 INFO  : created .gitignore file for the project C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis
16:30:30 INFO  : Successfully created repository data at C:\Users\<USER>\AppData\Local\Temp\rigel_lopper_lymz2421463514918558252
16:30:30 INFO  : cmd.exe, /C, C:\Xilinx\VitisEmbedded\Vitis\2024.2\tps\win64\lopper-1.1.0\env\Scripts\activate.bat && python C:\Xilinx\VitisEmbedded\Vitis\2024.2\data\embeddedsw\scripts\pyesw\\get_template_data.py -d C:\Users\<USER>\AppData\Local\Temp\rigel_lopper_lymz4580212967188116320 -r C:\Users\<USER>\AppData\Local\Temp\rigel_lopper_lymz2421463514918558252\.repo.yaml && C:\Xilinx\VitisEmbedded\Vitis\2024.2\tps\win64\lopper-1.1.0\env\Scripts\deactivate.bat
16:30:31 INFO  : Embedded Template List generated successfully
16:30:31 INFO  : cmd.exe, /C, C:\Xilinx\VitisEmbedded\Vitis\2024.2\tps\win64\lopper-1.1.0\env\Scripts\activate.bat && python C:\Xilinx\VitisEmbedded\Vitis\2024.2\data\embeddedsw\scripts\pyesw\\get_template_data.py -d C:\Users\<USER>\AppData\Local\Temp\rigel_lopper_lymz2846553103968265989 -r C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\_ide\.wsdata\.repo.yaml && C:\Xilinx\VitisEmbedded\Vitis\2024.2\tps\win64\lopper-1.1.0\env\Scripts\deactivate.bat
16:30:31 INFO  : Embedded Template List generated successfully
16:31:21 INFO  : cmd.exe, /C, C:\Xilinx\VitisEmbedded\Vitis\2024.2\tps\win64\lopper-1.1.0\env\Scripts\activate.bat && lopper -O  . C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\platform\hw\sdt\system-top.dts -- baremetal_getsupported_comp_xlnx ps7_cortexa9_0 C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\_ide\.wsdata\.repo.yaml && C:\Xilinx\VitisEmbedded\Vitis\2024.2\tps\win64\lopper-1.1.0\env\Scripts\deactivate.bat
16:31:21 INFO  : Supported Library List generated.
16:31:24 INFO  : Updating Examples in Driver C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\platform\ps7_cortexa9_0\standalone_ps7_cortexa9_0\bsp
16:31:24 INFO  : cmd.exe, /C, C:\Xilinx\VitisEmbedded\Vitis\2024.2\tps\win64\lopper-1.1.0\env\Scripts\activate.bat && python C:\Xilinx\VitisEmbedded\Vitis\2024.2\data\embeddedsw\scripts\pyesw\\load_example.py -d C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\platform\ps7_cortexa9_0\standalone_ps7_cortexa9_0\bsp && C:\Xilinx\VitisEmbedded\Vitis\2024.2\tps\win64\lopper-1.1.0\env\Scripts\deactivate.bat
16:31:28 INFO  : -- The C compiler identification is GNU 13.3.0
16:31:28 INFO  : -- The CXX compiler identification is GNU 13.3.0
16:31:28 INFO  : -- Detecting C compiler ABI info
16:31:28 INFO  : -- Detecting C compiler ABI info - done
16:31:28 INFO  : -- Check for working C compiler: C:/Xilinx/VitisEmbedded/Vitis/2024.2/gnu/aarch32/nt/gcc-arm-none-eabi/bin/arm-none-eabi-gcc.exe - skipped
16:31:28 INFO  : -- Detecting C compile features
16:31:28 INFO  : -- Detecting C compile features - done
16:31:28 INFO  : -- Detecting CXX compiler ABI info
16:31:28 INFO  : -- Detecting CXX compiler ABI info - done
16:31:28 INFO  : -- Check for working CXX compiler: C:/Xilinx/VitisEmbedded/Vitis/2024.2/gnu/aarch32/nt/gcc-arm-none-eabi/bin/arm-none-eabi-g++.exe - skipped
16:31:28 INFO  : -- Detecting CXX compile features
16:31:28 INFO  : -- Detecting CXX compile features - done
16:31:28 INFO  : -- Configuring done
16:31:28 INFO  : -- Generating done
16:31:28 INFO  : CMake Warning:
16:31:28 INFO  :   Manually-specified variables were not used by the project:
16:31:28 INFO  : 
16:31:28 INFO  :     CMAKE_LIBRARY_PATH
16:31:28 INFO  : 
16:31:28 INFO  : 
16:31:28 INFO  : -- Build files have been written to: C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/platform/ps7_cortexa9_0/standalone_ps7_cortexa9_0/bsp/libsrc/build_configs/exmetadata
16:31:28 INFO  : Updated Examples in Driver
15:57:10 INFO  : connection terminated
15:57:10 INFO  : connection terminated
15:57:10 INFO  : scheduling shutdown timer for 2 seconds ...
15:57:10 INFO  : scheduling shutdown timer for 2 seconds ...
19:12:00 INFO  : updated workspace path: C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis
19:12:00 INFO  : created .gitignore file for the project C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis
19:12:01 INFO  : Successfully created repository data at C:\Users\<USER>\AppData\Local\Temp\rigel_lopper_lymz10849631178979937633
19:12:01 INFO  : cmd.exe, /C, C:\Xilinx\VitisEmbedded\Vitis\2024.2\tps\win64\lopper-1.1.0\env\Scripts\activate.bat && python C:\Xilinx\VitisEmbedded\Vitis\2024.2\data\embeddedsw\scripts\pyesw\\get_template_data.py -d C:\Users\<USER>\AppData\Local\Temp\rigel_lopper_lymz5763196470626036389 -r C:\Users\<USER>\AppData\Local\Temp\rigel_lopper_lymz10849631178979937633\.repo.yaml && C:\Xilinx\VitisEmbedded\Vitis\2024.2\tps\win64\lopper-1.1.0\env\Scripts\deactivate.bat
19:12:01 INFO  : Embedded Template List generated successfully
19:12:02 INFO  : cmd.exe, /C, C:\Xilinx\VitisEmbedded\Vitis\2024.2\tps\win64\lopper-1.1.0\env\Scripts\activate.bat && python C:\Xilinx\VitisEmbedded\Vitis\2024.2\data\embeddedsw\scripts\pyesw\\get_template_data.py -d C:\Users\<USER>\AppData\Local\Temp\rigel_lopper_lymz16266454789079648566 -r C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\_ide\.wsdata\.repo.yaml && C:\Xilinx\VitisEmbedded\Vitis\2024.2\tps\win64\lopper-1.1.0\env\Scripts\deactivate.bat
19:12:02 INFO  : Embedded Template List generated successfully
19:12:13 INFO  : The hardware specification used by project 'C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\hello_world' is out of sync with the platform. Resource files extracted from the hardware specification will be updated.
19:12:13 INFO  : The file 'C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\hello_world\_ide\bitstream\ZynqDesign_wrapper.bit' stored in project is removed.
19:12:13 INFO  : The updated bitstream files are copied from platform to folder 'C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\hello_world\_ide\bitstream' in project 'C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\hello_world'.
19:12:13 INFO  : The file 'C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\hello_world\_ide\psinit\ps7_init.tcl' stored in project is removed.
19:12:13 INFO  : The updated ps init files are copied from platform to folder 'C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\hello_world\_ide\psinit' in project 'C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\hello_world'.
19:12:42 INFO  : The updated bitstream files are copied from platform to folder 'C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\hello_world\_ide\bitstream' in project 'C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\hello_world'.
19:12:42 INFO  : The updated ps init files are copied from platform to folder 'C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\hello_world\_ide\psinit' in project 'C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\hello_world'.
19:14:07 INFO  : XSDB server has started successfully from frontend.
19:14:07 INFO  : It has taken  1secs.
19:14:08 INFO  : Connection to XSDB Server established.
19:14:08 INFO  : It has taken  1secs.
19:14:10 INFO  : connect -url tcp:127.0.0.1:3121
19:14:10 INFO  : Done
19:14:12 INFO  : bpremove -all
19:14:12 INFO  : Context for 'APU' is selected.
19:14:12 INFO  : System reset is completed.
19:14:15 INFO  : 'after 3000' command is executed.
19:14:15 INFO  : 'targets -set -filter {jtag_cable_name =~ "Digilent JTAG-SMT2 210251A08870" && level==0 && jtag_device_ctx=="jsn-JTAG-SMT2-210251A08870-23727093-0"}' command is executed.
19:14:17 INFO  : Device configured successfully with "C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/hello_world/_ide/bitstream/ZynqDesign_wrapper.bit"
19:14:17 INFO  : targets -set -nocase -filter {name =~"APU*"}
19:14:17 INFO  : loadhw -hw C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/platform/export/platform/hw/ZynqDesign_wrapper.xsa -mem-ranges [list {0x40000000 0xbfffffff}]
19:14:17 INFO  : configparams force-mem-access 1
19:14:17 INFO  : targets -set -nocase -filter {name =~"APU*"}
19:14:17 INFO  : source C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/hello_world/_ide/psinit/ps7_init.tcl
19:14:18 INFO  : ps7_init
19:14:18 INFO  : ps7_post_config
19:14:18 INFO  : targets -set -nocase -filter {name =~ "*A9*#0"}
19:14:18 INFO  : rst -processor
19:14:18 INFO  : dow C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/workspace/TA/arm_xc7z0x0_le_hard/make/TA.elf
19:14:18 INFO  : bpadd main
19:14:18 INFO  : con
19:14:18 INFO  : configparams force-mem-access 0
19:14:19 INFO  : Testing the connection for 127.0.0.1
19:14:39 INFO  : disconnect
19:22:21 INFO  : connect -url tcp:127.0.0.1:3121
19:22:21 INFO  : bpremove -all
19:22:22 INFO  : Context for 'APU' is selected.
19:22:22 INFO  : System reset is completed.
19:22:25 INFO  : 'after 3000' command is executed.
19:22:25 INFO  : 'targets -set -filter {jtag_cable_name =~ "Digilent JTAG-SMT2 210251A08870" && level==0 && jtag_device_ctx=="jsn-JTAG-SMT2-210251A08870-23727093-0"}' command is executed.
19:22:27 INFO  : Device configured successfully with "C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/hello_world/_ide/bitstream/ZynqDesign_wrapper.bit"
19:22:27 INFO  : targets -set -nocase -filter {name =~"APU*"}
19:22:28 INFO  : loadhw -hw C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/platform/export/platform/hw/ZynqDesign_wrapper.xsa -mem-ranges [list {0x40000000 0xbfffffff}]
19:22:28 INFO  : configparams force-mem-access 1
19:22:28 INFO  : targets -set -nocase -filter {name =~"APU*"}
19:22:28 INFO  : source C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/hello_world/_ide/psinit/ps7_init.tcl
19:22:28 INFO  : ps7_init
19:22:28 INFO  : ps7_post_config
19:22:28 INFO  : targets -set -nocase -filter {name =~ "*A9*#0"}
19:22:28 INFO  : rst -processor
19:22:29 INFO  : dow C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/workspace/TA/arm_xc7z0x0_le_hard/make/TA.elf
19:22:29 INFO  : bpadd main
19:22:29 INFO  : con
19:22:29 INFO  : configparams force-mem-access 0
19:22:30 INFO  : Testing the connection for 127.0.0.1
19:22:45 INFO  : disconnect
19:26:20 INFO  : connect -url tcp:127.0.0.1:3121
19:26:20 INFO  : bpremove -all
19:26:20 INFO  : Context for 'APU' is selected.
19:26:20 INFO  : System reset is completed.
19:26:23 INFO  : 'after 3000' command is executed.
19:26:23 INFO  : 'targets -set -filter {jtag_cable_name =~ "Digilent JTAG-SMT2 210251A08870" && level==0 && jtag_device_ctx=="jsn-JTAG-SMT2-210251A08870-23727093-0"}' command is executed.
19:26:25 INFO  : Device configured successfully with "C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/hello_world/_ide/bitstream/ZynqDesign_wrapper.bit"
19:26:25 INFO  : targets -set -nocase -filter {name =~"APU*"}
19:26:26 INFO  : loadhw -hw C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/platform/export/platform/hw/ZynqDesign_wrapper.xsa -mem-ranges [list {0x40000000 0xbfffffff}]
19:26:26 INFO  : configparams force-mem-access 1
19:26:26 INFO  : targets -set -nocase -filter {name =~"APU*"}
19:26:26 INFO  : source C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/hello_world/_ide/psinit/ps7_init.tcl
19:26:27 INFO  : ps7_init
19:26:27 INFO  : ps7_post_config
19:26:27 INFO  : targets -set -nocase -filter {name =~ "*A9*#0"}
19:26:27 INFO  : rst -processor
19:26:27 INFO  : dow C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/workspace/TA/arm_xc7z0x0_le_hard/make/TA.elf
19:26:27 INFO  : bpadd main
19:26:27 INFO  : con
19:26:27 INFO  : configparams force-mem-access 0
19:26:28 INFO  : Testing the connection for 127.0.0.1
19:30:23 INFO  : disconnect
19:30:24 INFO  : connect -url tcp:127.0.0.1:3121
19:30:24 INFO  : bpremove -all
19:30:26 INFO  : Context for 'APU' is selected.
19:30:26 INFO  : System reset is completed.
19:30:29 INFO  : 'after 3000' command is executed.
19:30:30 INFO  : 'targets -set -filter {jtag_cable_name =~ "Digilent JTAG-SMT2 210251A08870" && level==0 && jtag_device_ctx=="jsn-JTAG-SMT2-210251A08870-23727093-0"}' command is executed.
19:30:32 INFO  : Device configured successfully with "C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/hello_world/_ide/bitstream/ZynqDesign_wrapper.bit"
19:30:32 INFO  : targets -set -nocase -filter {name =~"APU*"}
19:30:33 INFO  : loadhw -hw C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/platform/export/platform/hw/ZynqDesign_wrapper.xsa -mem-ranges [list {0x40000000 0xbfffffff}]
19:30:33 INFO  : configparams force-mem-access 1
19:30:33 INFO  : targets -set -nocase -filter {name =~"APU*"}
19:30:33 INFO  : source C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/hello_world/_ide/psinit/ps7_init.tcl
19:30:33 INFO  : ps7_init
19:30:33 INFO  : ps7_post_config
19:30:33 INFO  : targets -set -nocase -filter {name =~ "*A9*#0"}
19:30:33 INFO  : rst -processor
19:30:34 INFO  : dow C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/workspace/TA/arm_xc7z0x0_le_hard/make/TA.elf
19:30:34 INFO  : bpadd main
19:30:34 INFO  : con
19:30:34 INFO  : configparams force-mem-access 0
19:30:35 INFO  : Testing the connection for 127.0.0.1
19:32:25 INFO  : disconnect
19:32:27 INFO  : connect -url tcp:127.0.0.1:3121
19:32:27 INFO  : bpremove -all
19:32:28 INFO  : Context for 'APU' is selected.
19:32:28 INFO  : System reset is completed.
19:32:31 INFO  : 'after 3000' command is executed.
19:32:31 INFO  : 'targets -set -filter {jtag_cable_name =~ "Digilent JTAG-SMT2 210251A08870" && level==0 && jtag_device_ctx=="jsn-JTAG-SMT2-210251A08870-23727093-0"}' command is executed.
19:32:34 INFO  : Device configured successfully with "C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/hello_world/_ide/bitstream/ZynqDesign_wrapper.bit"
19:32:34 INFO  : targets -set -nocase -filter {name =~"APU*"}
19:32:35 INFO  : loadhw -hw C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/platform/export/platform/hw/ZynqDesign_wrapper.xsa -mem-ranges [list {0x40000000 0xbfffffff}]
19:32:35 INFO  : configparams force-mem-access 1
19:32:35 INFO  : targets -set -nocase -filter {name =~"APU*"}
19:32:35 INFO  : source C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/hello_world/_ide/psinit/ps7_init.tcl
19:32:35 INFO  : ps7_init
19:32:35 INFO  : ps7_post_config
19:32:35 INFO  : targets -set -nocase -filter {name =~ "*A9*#0"}
19:32:35 INFO  : rst -processor
19:32:36 INFO  : dow C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/workspace/TA/arm_xc7z0x0_le_hard/make/TA.elf
19:32:36 INFO  : bpadd main
19:32:36 INFO  : con
19:32:36 INFO  : configparams force-mem-access 0
19:32:37 INFO  : Testing the connection for 127.0.0.1
19:33:24 INFO  : disconnect
19:33:42 INFO  : cmd.exe, /C, C:\Xilinx\VitisEmbedded\Vitis\2024.2\tps\win64\lopper-1.1.0\env\Scripts\activate.bat && lopper -O  . C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\platform\hw\sdt\system-top.dts -- baremetal_getsupported_comp_xlnx ps7_cortexa9_0 C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\_ide\.wsdata\.repo.yaml && C:\Xilinx\VitisEmbedded\Vitis\2024.2\tps\win64\lopper-1.1.0\env\Scripts\deactivate.bat
19:33:43 INFO  : Supported Library List generated.
19:33:45 INFO  : Updating Examples in Driver C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\platform\ps7_cortexa9_0\standalone_ps7_cortexa9_0\bsp
19:33:45 INFO  : cmd.exe, /C, C:\Xilinx\VitisEmbedded\Vitis\2024.2\tps\win64\lopper-1.1.0\env\Scripts\activate.bat && python C:\Xilinx\VitisEmbedded\Vitis\2024.2\data\embeddedsw\scripts\pyesw\\load_example.py -d C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\platform\ps7_cortexa9_0\standalone_ps7_cortexa9_0\bsp && C:\Xilinx\VitisEmbedded\Vitis\2024.2\tps\win64\lopper-1.1.0\env\Scripts\deactivate.bat
19:33:45 INFO  : Updated Examples in Driver
19:34:22 INFO  : lopper command to import example :python C:\Xilinx\VitisEmbedded\Vitis\2024.2\data\embeddedsw/scripts/pyesw//create_example.py -d C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\platform\export\platform\sw\standalone_ps7_cortexa9_0 -e xttcps_intr_example.c -n ps7_ttc_0 -w C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\xttcps_intr_example -r C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\_ide\.wsdata\.repo.yaml
19:34:22 INFO  : cmd.exe, /C, C:\Xilinx\VitisEmbedded\Vitis\2024.2\tps\win64\lopper-1.1.0\env\Scripts\activate.bat && python C:\Xilinx\VitisEmbedded\Vitis\2024.2\data\embeddedsw\scripts\pyesw\\create_example.py -d C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\platform\export\platform\sw\standalone_ps7_cortexa9_0 -e xttcps_intr_example.c -n ps7_ttc_0 -w C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\xttcps_intr_example -r C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\_ide\.wsdata\.repo.yaml && C:\Xilinx\VitisEmbedded\Vitis\2024.2\tps\win64\lopper-1.1.0\env\Scripts\deactivate.bat
19:34:26 INFO  : Successfully Created Application sources at C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/xttcps_intr_example/src
19:34:26 INFO  : Successfully imported example xttcps_intr_example.c on C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\xttcps_intr_example
19:34:26 INFO  : The hardware specification used by project 'C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\xttcps_intr_example' is out of sync with the platform. Resource files extracted from the hardware specification will be updated.
19:34:26 INFO  : The updated bitstream files are copied from platform to folder 'C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\xttcps_intr_example\_ide\bitstream' in project 'C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\xttcps_intr_example'.
19:34:26 INFO  : The updated ps init files are copied from platform to folder 'C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\xttcps_intr_example\_ide\psinit' in project 'C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\xttcps_intr_example'.
19:34:26 INFO  : created .gitignore file for the project C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\xttcps_intr_example
19:54:18 INFO  : connect -url tcp:127.0.0.1:3121
19:54:18 INFO  : bpremove -all
19:54:19 INFO  : Context for 'APU' is selected.
19:54:19 INFO  : System reset is completed.
19:54:22 INFO  : 'after 3000' command is executed.
19:54:22 INFO  : 'targets -set -filter {jtag_cable_name =~ "Digilent JTAG-SMT2 210251A08870" && level==0 && jtag_device_ctx=="jsn-JTAG-SMT2-210251A08870-23727093-0"}' command is executed.
19:54:24 INFO  : Device configured successfully with "C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/hello_world/_ide/bitstream/ZynqDesign_wrapper.bit"
19:54:24 INFO  : targets -set -nocase -filter {name =~"APU*"}
19:54:25 INFO  : loadhw -hw C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/platform/export/platform/hw/ZynqDesign_wrapper.xsa -mem-ranges [list {0x40000000 0xbfffffff}]
19:54:25 INFO  : configparams force-mem-access 1
19:54:25 INFO  : targets -set -nocase -filter {name =~"APU*"}
19:54:25 INFO  : source C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/hello_world/_ide/psinit/ps7_init.tcl
19:54:25 INFO  : ps7_init
19:54:25 INFO  : ps7_post_config
19:54:26 INFO  : targets -set -nocase -filter {name =~ "*A9*#0"}
19:54:26 INFO  : rst -processor
19:54:26 INFO  : dow C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/workspace/TA/arm_xc7z0x0_le_hard/make/TA.elf
19:54:26 INFO  : bpadd main
19:54:26 INFO  : con
19:54:26 INFO  : configparams force-mem-access 0
19:54:27 INFO  : Testing the connection for 127.0.0.1
19:55:14 INFO  : disconnect
19:57:22 INFO  : The updated bitstream files are copied from platform to folder 'C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\hello_world\_ide\bitstream' in project 'C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\hello_world'.
19:57:22 INFO  : The updated ps init files are copied from platform to folder 'C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\hello_world\_ide\psinit' in project 'C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\hello_world'.
19:58:23 INFO  : connect -url tcp:127.0.0.1:3121
19:58:23 INFO  : bpremove -all
19:58:23 INFO  : Context for 'APU' is selected.
19:58:23 INFO  : System reset is completed.
19:58:26 INFO  : 'after 3000' command is executed.
19:58:26 INFO  : 'targets -set -filter {jtag_cable_name =~ "Digilent JTAG-SMT2 210251A08870" && level==0 && jtag_device_ctx=="jsn-JTAG-SMT2-210251A08870-23727093-0"}' command is executed.
19:58:28 INFO  : Device configured successfully with "C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/hello_world/_ide/bitstream/ZynqDesign_wrapper.bit"
19:58:28 INFO  : targets -set -nocase -filter {name =~"APU*"}
19:58:29 INFO  : loadhw -hw C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/platform/export/platform/hw/ZynqDesign_wrapper.xsa -mem-ranges [list {0x40000000 0xbfffffff}]
19:58:29 INFO  : configparams force-mem-access 1
19:58:29 INFO  : targets -set -nocase -filter {name =~"APU*"}
19:58:29 INFO  : source C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/hello_world/_ide/psinit/ps7_init.tcl
19:58:29 INFO  : ps7_init
19:58:29 INFO  : ps7_post_config
19:58:29 INFO  : targets -set -nocase -filter {name =~ "*A9*#0"}
19:58:29 INFO  : rst -processor
19:58:30 INFO  : dow C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/workspace/TA/arm_xc7z0x0_le_hard/make/TA.elf
19:58:30 INFO  : bpadd main
19:58:30 INFO  : con
19:58:30 INFO  : configparams force-mem-access 0
19:58:31 INFO  : Testing the connection for 127.0.0.1
20:09:56 INFO  : disconnect
20:10:06 INFO  : The updated bitstream files are copied from platform to folder 'C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\hello_world\_ide\bitstream' in project 'C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\hello_world'.
20:10:06 INFO  : The updated ps init files are copied from platform to folder 'C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\hello_world\_ide\psinit' in project 'C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\hello_world'.
20:12:41 INFO  : connect -url tcp:127.0.0.1:3121
20:12:41 INFO  : bpremove -all
20:12:41 INFO  : Context for 'APU' is selected.
20:12:41 INFO  : System reset is completed.
20:12:44 INFO  : 'after 3000' command is executed.
20:12:44 INFO  : 'targets -set -filter {jtag_cable_name =~ "Digilent JTAG-SMT2 210251A08870" && level==0 && jtag_device_ctx=="jsn-JTAG-SMT2-210251A08870-23727093-0"}' command is executed.
20:12:46 INFO  : Device configured successfully with "C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/hello_world/_ide/bitstream/ZynqDesign_wrapper.bit"
20:12:46 INFO  : targets -set -nocase -filter {name =~"APU*"}
20:12:47 INFO  : loadhw -hw C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/platform/export/platform/hw/ZynqDesign_wrapper.xsa -mem-ranges [list {0x40000000 0xbfffffff}]
20:12:47 INFO  : configparams force-mem-access 1
20:12:47 INFO  : targets -set -nocase -filter {name =~"APU*"}
20:12:47 INFO  : source C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/hello_world/_ide/psinit/ps7_init.tcl
20:12:47 INFO  : ps7_init
20:12:47 INFO  : ps7_post_config
20:12:47 INFO  : targets -set -nocase -filter {name =~ "*A9*#0"}
20:12:47 INFO  : rst -processor
20:12:48 INFO  : dow C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/workspace/TA/arm_xc7z0x0_le_hard/make/TA.elf
20:12:48 INFO  : bpadd main
20:12:48 INFO  : con
20:12:48 INFO  : configparams force-mem-access 0
20:12:49 INFO  : Testing the connection for 127.0.0.1
20:14:33 INFO  : disconnect
20:14:34 INFO  : connect -url tcp:127.0.0.1:3121
20:14:34 INFO  : bpremove -all
20:14:34 INFO  : Context for 'APU' is selected.
20:14:34 INFO  : System reset is completed.
20:14:37 INFO  : 'after 3000' command is executed.
20:14:37 INFO  : 'targets -set -filter {jtag_cable_name =~ "Digilent JTAG-SMT2 210251A08870" && level==0 && jtag_device_ctx=="jsn-JTAG-SMT2-210251A08870-23727093-0"}' command is executed.
20:14:39 INFO  : Device configured successfully with "C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/hello_world/_ide/bitstream/ZynqDesign_wrapper.bit"
20:14:39 INFO  : targets -set -nocase -filter {name =~"APU*"}
20:14:40 INFO  : loadhw -hw C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/platform/export/platform/hw/ZynqDesign_wrapper.xsa -mem-ranges [list {0x40000000 0xbfffffff}]
20:14:40 INFO  : configparams force-mem-access 1
20:14:40 INFO  : targets -set -nocase -filter {name =~"APU*"}
20:14:40 INFO  : source C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/hello_world/_ide/psinit/ps7_init.tcl
20:14:40 INFO  : ps7_init
20:14:40 INFO  : ps7_post_config
20:14:40 INFO  : targets -set -nocase -filter {name =~ "*A9*#0"}
20:14:40 INFO  : rst -processor
20:14:41 INFO  : dow C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/workspace/TA/arm_xc7z0x0_le_hard/make/TA.elf
20:14:41 INFO  : bpadd main
20:14:41 INFO  : con
20:14:41 INFO  : configparams force-mem-access 0
20:14:42 INFO  : Testing the connection for 127.0.0.1
20:16:59 INFO  : disconnect
20:16:59 INFO  : connect -url tcp:127.0.0.1:3121
20:16:59 INFO  : bpremove -all
20:17:00 INFO  : Context for 'APU' is selected.
20:17:00 INFO  : System reset is completed.
20:17:03 INFO  : 'after 3000' command is executed.
20:17:03 INFO  : 'targets -set -filter {jtag_cable_name =~ "Digilent JTAG-SMT2 210251A08870" && level==0 && jtag_device_ctx=="jsn-JTAG-SMT2-210251A08870-23727093-0"}' command is executed.
20:17:05 INFO  : Device configured successfully with "C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/hello_world/_ide/bitstream/ZynqDesign_wrapper.bit"
20:17:05 INFO  : targets -set -nocase -filter {name =~"APU*"}
20:17:06 INFO  : loadhw -hw C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/platform/export/platform/hw/ZynqDesign_wrapper.xsa -mem-ranges [list {0x40000000 0xbfffffff}]
20:17:06 INFO  : configparams force-mem-access 1
20:17:06 INFO  : targets -set -nocase -filter {name =~"APU*"}
20:17:06 INFO  : source C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/hello_world/_ide/psinit/ps7_init.tcl
20:17:06 INFO  : ps7_init
20:17:06 INFO  : ps7_post_config
20:17:06 INFO  : targets -set -nocase -filter {name =~ "*A9*#0"}
20:17:06 INFO  : rst -processor
20:17:07 INFO  : dow C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/workspace/TA/arm_xc7z0x0_le_hard/make/TA.elf
20:17:07 INFO  : bpadd main
20:17:07 INFO  : con
20:17:07 INFO  : configparams force-mem-access 0
20:17:08 INFO  : Testing the connection for 127.0.0.1
20:19:06 INFO  : disconnect
20:38:42 INFO  : connect -url tcp:127.0.0.1:3121
20:38:42 INFO  : bpremove -all
20:38:42 INFO  : Context for 'APU' is selected.
20:38:42 INFO  : System reset is completed.
20:38:45 INFO  : 'after 3000' command is executed.
20:38:45 INFO  : 'targets -set -filter {jtag_cable_name =~ "Digilent JTAG-SMT2 210251A08870" && level==0 && jtag_device_ctx=="jsn-JTAG-SMT2-210251A08870-23727093-0"}' command is executed.
20:38:48 INFO  : Device configured successfully with "C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/hello_world/_ide/bitstream/ZynqDesign_wrapper.bit"
20:38:48 INFO  : targets -set -nocase -filter {name =~"APU*"}
20:38:49 INFO  : loadhw -hw C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/platform/export/platform/hw/ZynqDesign_wrapper.xsa -mem-ranges [list {0x40000000 0xbfffffff}]
20:38:49 INFO  : configparams force-mem-access 1
20:38:49 INFO  : targets -set -nocase -filter {name =~"APU*"}
20:38:49 INFO  : source C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/hello_world/_ide/psinit/ps7_init.tcl
20:38:49 INFO  : ps7_init
20:38:49 INFO  : ps7_post_config
20:38:49 INFO  : targets -set -nocase -filter {name =~ "*A9*#0"}
20:38:49 INFO  : rst -processor
20:38:49 INFO  : dow C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/workspace/TA/arm_xc7z0x0_le_hard/make/TA.elf
20:38:50 INFO  : bpadd main
20:38:50 INFO  : con
20:38:50 INFO  : configparams force-mem-access 0
20:38:51 INFO  : Testing the connection for 127.0.0.1
20:43:09 INFO  : disconnect
20:43:10 INFO  : connect -url tcp:127.0.0.1:3121
20:43:10 INFO  : bpremove -all
20:43:11 INFO  : Context for 'APU' is selected.
20:43:11 INFO  : System reset is completed.
20:43:14 INFO  : 'after 3000' command is executed.
20:43:14 INFO  : 'targets -set -filter {jtag_cable_name =~ "Digilent JTAG-SMT2 210251A08870" && level==0 && jtag_device_ctx=="jsn-JTAG-SMT2-210251A08870-23727093-0"}' command is executed.
20:43:17 INFO  : Device configured successfully with "C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/hello_world/_ide/bitstream/ZynqDesign_wrapper.bit"
20:43:17 INFO  : targets -set -nocase -filter {name =~"APU*"}
20:43:18 INFO  : loadhw -hw C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/platform/export/platform/hw/ZynqDesign_wrapper.xsa -mem-ranges [list {0x40000000 0xbfffffff}]
20:43:18 INFO  : configparams force-mem-access 1
20:43:18 INFO  : targets -set -nocase -filter {name =~"APU*"}
20:43:18 INFO  : source C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/hello_world/_ide/psinit/ps7_init.tcl
20:43:18 INFO  : ps7_init
20:43:18 INFO  : ps7_post_config
20:43:18 INFO  : targets -set -nocase -filter {name =~ "*A9*#0"}
20:43:18 INFO  : rst -processor
20:43:19 INFO  : dow C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/workspace/TA/arm_xc7z0x0_le_hard/make/TA.elf
20:43:19 INFO  : bpadd main
20:43:19 INFO  : con
20:43:19 INFO  : configparams force-mem-access 0
20:43:20 INFO  : Testing the connection for 127.0.0.1
20:47:16 INFO  : disconnect
20:47:17 INFO  : connect -url tcp:127.0.0.1:3121
20:47:17 INFO  : bpremove -all
20:47:17 INFO  : Context for 'APU' is selected.
20:47:17 INFO  : System reset is completed.
20:47:20 INFO  : 'after 3000' command is executed.
20:47:20 INFO  : 'targets -set -filter {jtag_cable_name =~ "Digilent JTAG-SMT2 210251A08870" && level==0 && jtag_device_ctx=="jsn-JTAG-SMT2-210251A08870-23727093-0"}' command is executed.
20:47:22 INFO  : Device configured successfully with "C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/hello_world/_ide/bitstream/ZynqDesign_wrapper.bit"
20:47:22 INFO  : targets -set -nocase -filter {name =~"APU*"}
20:47:23 INFO  : loadhw -hw C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/platform/export/platform/hw/ZynqDesign_wrapper.xsa -mem-ranges [list {0x40000000 0xbfffffff}]
20:47:23 INFO  : configparams force-mem-access 1
20:47:23 INFO  : targets -set -nocase -filter {name =~"APU*"}
20:47:23 INFO  : source C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/hello_world/_ide/psinit/ps7_init.tcl
20:47:23 INFO  : ps7_init
20:47:23 INFO  : ps7_post_config
20:47:24 INFO  : targets -set -nocase -filter {name =~ "*A9*#0"}
20:47:24 INFO  : rst -processor
20:47:24 INFO  : dow C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/workspace/TA/arm_xc7z0x0_le_hard/make/TA.elf
20:47:24 INFO  : bpadd main
20:47:24 INFO  : con
20:47:24 INFO  : configparams force-mem-access 0
20:47:25 INFO  : Testing the connection for 127.0.0.1
20:49:11 INFO  : disconnect
20:49:13 INFO  : connect -url tcp:127.0.0.1:3121
20:49:13 INFO  : bpremove -all
20:49:22 ERROR : Failed to initialize the hardware Could not find ARM device on the board for connection 'Local'.
Check if the target is in:
1. Split JTAG - No operations are possible with ARM DAP.
2. Non JTAG bootmode - Bootrom may need time to enable DAP.
Please try again.


Troubleshooting hints:
1. Check whether board is connected to system properly.
2. In case of zynq board, check whether Digilent/Xilinx cable switch settings are correct.
3. If you are using Xilinx Platform Cable USB, ensure that status LED is green.
20:49:24 INFO  : connect -url tcp:127.0.0.1:3121
20:49:24 INFO  : bpremove -all
20:49:24 INFO  : Context for 'APU' is selected.
20:49:24 INFO  : System reset is completed.
20:49:27 INFO  : 'after 3000' command is executed.
20:49:27 INFO  : 'targets -set -filter {jtag_cable_name =~ "Digilent JTAG-SMT2 210251A08870" && level==0 && jtag_device_ctx=="jsn-JTAG-SMT2-210251A08870-23727093-0"}' command is executed.
20:49:30 INFO  : Device configured successfully with "C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/hello_world/_ide/bitstream/ZynqDesign_wrapper.bit"
20:49:30 INFO  : targets -set -nocase -filter {name =~"APU*"}
20:49:31 INFO  : loadhw -hw C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/platform/export/platform/hw/ZynqDesign_wrapper.xsa -mem-ranges [list {0x40000000 0xbfffffff}]
20:49:31 INFO  : configparams force-mem-access 1
20:49:31 INFO  : targets -set -nocase -filter {name =~"APU*"}
20:49:31 INFO  : source C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/hello_world/_ide/psinit/ps7_init.tcl
20:49:31 INFO  : ps7_init
20:49:31 INFO  : ps7_post_config
20:49:31 INFO  : targets -set -nocase -filter {name =~ "*A9*#0"}
20:49:31 INFO  : rst -processor
20:49:32 INFO  : dow C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/workspace/TA/arm_xc7z0x0_le_hard/make/TA.elf
20:49:32 INFO  : bpadd main
20:49:32 INFO  : con
20:49:32 INFO  : configparams force-mem-access 0
20:49:33 INFO  : Testing the connection for 127.0.0.1
20:51:30 INFO  : disconnect
20:51:41 INFO  : connect -url tcp:127.0.0.1:3121
20:51:41 INFO  : bpremove -all
20:51:41 INFO  : Context for 'APU' is selected.
20:51:41 INFO  : System reset is completed.
20:51:44 INFO  : 'after 3000' command is executed.
20:51:44 INFO  : 'targets -set -filter {jtag_cable_name =~ "Digilent JTAG-SMT2 210251A08870" && level==0 && jtag_device_ctx=="jsn-JTAG-SMT2-210251A08870-23727093-0"}' command is executed.
20:51:46 INFO  : Device configured successfully with "C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/hello_world/_ide/bitstream/ZynqDesign_wrapper.bit"
20:51:46 INFO  : targets -set -nocase -filter {name =~"APU*"}
20:51:47 INFO  : loadhw -hw C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/platform/export/platform/hw/ZynqDesign_wrapper.xsa -mem-ranges [list {0x40000000 0xbfffffff}]
20:51:47 INFO  : configparams force-mem-access 1
20:51:47 INFO  : targets -set -nocase -filter {name =~"APU*"}
20:51:47 INFO  : source C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/hello_world/_ide/psinit/ps7_init.tcl
20:51:47 INFO  : ps7_init
20:51:47 INFO  : ps7_post_config
20:51:47 INFO  : targets -set -nocase -filter {name =~ "*A9*#0"}
20:51:47 INFO  : rst -processor
20:51:48 INFO  : dow C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/workspace/TA/arm_xc7z0x0_le_hard/make/TA.elf
20:51:48 INFO  : bpadd main
20:51:48 INFO  : con
20:51:48 INFO  : configparams force-mem-access 0
20:51:49 INFO  : Testing the connection for 127.0.0.1
20:54:18 INFO  : disconnect
20:54:21 INFO  : connect -url tcp:127.0.0.1:3121
20:54:21 INFO  : bpremove -all
20:54:21 INFO  : Context for 'APU' is selected.
20:54:21 INFO  : System reset is completed.
20:54:24 INFO  : 'after 3000' command is executed.
20:54:24 INFO  : 'targets -set -filter {jtag_cable_name =~ "Digilent JTAG-SMT2 210251A08870" && level==0 && jtag_device_ctx=="jsn-JTAG-SMT2-210251A08870-23727093-0"}' command is executed.
20:54:26 INFO  : Device configured successfully with "C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/hello_world/_ide/bitstream/ZynqDesign_wrapper.bit"
20:54:26 INFO  : targets -set -nocase -filter {name =~"APU*"}
20:54:28 INFO  : loadhw -hw C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/platform/export/platform/hw/ZynqDesign_wrapper.xsa -mem-ranges [list {0x40000000 0xbfffffff}]
20:54:28 INFO  : configparams force-mem-access 1
20:54:28 INFO  : targets -set -nocase -filter {name =~"APU*"}
20:54:28 INFO  : source C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/hello_world/_ide/psinit/ps7_init.tcl
20:54:28 INFO  : ps7_init
20:54:28 INFO  : ps7_post_config
20:54:28 INFO  : targets -set -nocase -filter {name =~ "*A9*#0"}
20:54:28 INFO  : rst -processor
20:54:28 INFO  : dow C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/workspace/TA/arm_xc7z0x0_le_hard/make/TA.elf
20:54:28 INFO  : bpadd main
20:54:28 INFO  : con
20:54:28 INFO  : configparams force-mem-access 0
20:54:29 INFO  : Testing the connection for 127.0.0.1
21:01:58 INFO  : disconnect
21:01:59 INFO  : connect -url tcp:127.0.0.1:3121
21:01:59 INFO  : bpremove -all
21:02:02 INFO  : Context for 'APU' is selected.
21:02:02 INFO  : System reset is completed.
21:02:05 INFO  : 'after 3000' command is executed.
21:02:05 INFO  : 'targets -set -filter {jtag_cable_name =~ "Digilent JTAG-SMT2 210251A08870" && level==0 && jtag_device_ctx=="jsn-JTAG-SMT2-210251A08870-23727093-0"}' command is executed.
21:02:07 INFO  : Device configured successfully with "C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/hello_world/_ide/bitstream/ZynqDesign_wrapper.bit"
21:02:07 INFO  : targets -set -nocase -filter {name =~"APU*"}
21:02:08 INFO  : loadhw -hw C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/platform/export/platform/hw/ZynqDesign_wrapper.xsa -mem-ranges [list {0x40000000 0xbfffffff}]
21:02:08 INFO  : configparams force-mem-access 1
21:02:08 INFO  : targets -set -nocase -filter {name =~"APU*"}
21:02:08 INFO  : source C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/hello_world/_ide/psinit/ps7_init.tcl
21:02:09 INFO  : ps7_init
21:02:09 INFO  : ps7_post_config
21:02:09 INFO  : targets -set -nocase -filter {name =~ "*A9*#0"}
21:02:09 INFO  : rst -processor
21:02:09 INFO  : dow C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/workspace/TA/arm_xc7z0x0_le_hard/make/TA.elf
21:02:09 INFO  : bpadd main
21:02:09 INFO  : con
21:02:09 INFO  : configparams force-mem-access 0
21:02:10 INFO  : Testing the connection for 127.0.0.1
21:04:53 INFO  : disconnect
21:05:18 INFO  : connect -url tcp:127.0.0.1:3121
21:05:18 INFO  : bpremove -all
21:05:18 INFO  : Context for 'APU' is selected.
21:05:18 INFO  : System reset is completed.
21:05:21 INFO  : 'after 3000' command is executed.
21:05:21 INFO  : 'targets -set -filter {jtag_cable_name =~ "Digilent JTAG-SMT2 210251A08870" && level==0 && jtag_device_ctx=="jsn-JTAG-SMT2-210251A08870-23727093-0"}' command is executed.
21:05:23 INFO  : Device configured successfully with "C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/hello_world/_ide/bitstream/ZynqDesign_wrapper.bit"
21:05:23 INFO  : targets -set -nocase -filter {name =~"APU*"}
21:05:24 INFO  : loadhw -hw C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/platform/export/platform/hw/ZynqDesign_wrapper.xsa -mem-ranges [list {0x40000000 0xbfffffff}]
21:05:24 INFO  : configparams force-mem-access 1
21:05:24 INFO  : targets -set -nocase -filter {name =~"APU*"}
21:05:24 INFO  : source C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/hello_world/_ide/psinit/ps7_init.tcl
21:05:24 INFO  : ps7_init
21:05:24 INFO  : ps7_post_config
21:05:24 INFO  : targets -set -nocase -filter {name =~ "*A9*#0"}
21:05:24 INFO  : rst -processor
21:05:25 INFO  : dow C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/workspace/TA/arm_xc7z0x0_le_hard/make/TA.elf
21:05:25 INFO  : bpadd main
21:05:25 INFO  : con
21:05:25 INFO  : configparams force-mem-access 0
21:05:26 INFO  : Testing the connection for 127.0.0.1
21:16:57 INFO  : disconnect
21:16:58 INFO  : connect -url tcp:127.0.0.1:3121
21:16:58 INFO  : bpremove -all
21:16:58 INFO  : Context for 'APU' is selected.
21:16:58 INFO  : System reset is completed.
21:17:01 INFO  : 'after 3000' command is executed.
21:17:01 INFO  : 'targets -set -filter {jtag_cable_name =~ "Digilent JTAG-SMT2 210251A08870" && level==0 && jtag_device_ctx=="jsn-JTAG-SMT2-210251A08870-23727093-0"}' command is executed.
21:17:04 INFO  : Device configured successfully with "C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/hello_world/_ide/bitstream/ZynqDesign_wrapper.bit"
21:17:04 INFO  : targets -set -nocase -filter {name =~"APU*"}
21:17:05 INFO  : loadhw -hw C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/platform/export/platform/hw/ZynqDesign_wrapper.xsa -mem-ranges [list {0x40000000 0xbfffffff}]
21:17:05 INFO  : configparams force-mem-access 1
21:17:05 INFO  : targets -set -nocase -filter {name =~"APU*"}
21:17:05 INFO  : source C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/hello_world/_ide/psinit/ps7_init.tcl
21:17:05 INFO  : ps7_init
21:17:05 INFO  : ps7_post_config
21:17:05 INFO  : targets -set -nocase -filter {name =~ "*A9*#0"}
21:17:05 INFO  : rst -processor
21:17:06 INFO  : dow C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/workspace/TA/arm_xc7z0x0_le_hard/make/TA.elf
21:17:06 INFO  : bpadd main
21:17:06 INFO  : con
21:17:06 INFO  : configparams force-mem-access 0
21:17:07 INFO  : Testing the connection for 127.0.0.1
21:27:26 INFO  : connection terminated
21:27:26 INFO  : connection terminated
21:27:26 INFO  : scheduling shutdown timer for 2 seconds ...
14:28:36 INFO  : updated workspace path: C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis
14:28:36 INFO  : created .gitignore file for the project C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis
14:28:37 INFO  : Successfully created repository data at C:\Users\<USER>\AppData\Local\Temp\rigel_lopper_lymz9193736915422351685
14:28:37 INFO  : cmd.exe, /C, C:\Xilinx\VitisEmbedded\Vitis\2024.2\tps\win64\lopper-1.1.0\env\Scripts\activate.bat && python C:\Xilinx\VitisEmbedded\Vitis\2024.2\data\embeddedsw\scripts\pyesw\\get_template_data.py -d C:\Users\<USER>\AppData\Local\Temp\rigel_lopper_lymz440433357777608467 -r C:\Users\<USER>\AppData\Local\Temp\rigel_lopper_lymz9193736915422351685\.repo.yaml && C:\Xilinx\VitisEmbedded\Vitis\2024.2\tps\win64\lopper-1.1.0\env\Scripts\deactivate.bat
14:28:37 INFO  : Embedded Template List generated successfully
14:28:37 INFO  : cmd.exe, /C, C:\Xilinx\VitisEmbedded\Vitis\2024.2\tps\win64\lopper-1.1.0\env\Scripts\activate.bat && python C:\Xilinx\VitisEmbedded\Vitis\2024.2\data\embeddedsw\scripts\pyesw\\get_template_data.py -d C:\Users\<USER>\AppData\Local\Temp\rigel_lopper_lymz13331926367832419708 -r C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\_ide\.wsdata\.repo.yaml && C:\Xilinx\VitisEmbedded\Vitis\2024.2\tps\win64\lopper-1.1.0\env\Scripts\deactivate.bat
14:28:37 INFO  : Embedded Template List generated successfully
14:28:48 INFO  : XSDB server has started successfully from frontend.
14:28:48 INFO  : It has taken  1secs.
14:28:49 INFO  : Connection to XSDB Server established.
14:28:49 INFO  : It has taken  1secs.
14:28:49 INFO  : Done
14:28:52 INFO  : connect -url tcp:127.0.0.1:3121
14:28:53 INFO  : bpremove -all
14:28:53 INFO  : Context for 'APU' is selected.
14:28:53 INFO  : System reset is completed.
14:28:56 INFO  : 'after 3000' command is executed.
14:28:56 INFO  : 'targets -set -filter {jtag_cable_name =~ "Digilent JTAG-SMT2 210251A08870" && level==0 && jtag_device_ctx=="jsn-JTAG-SMT2-210251A08870-23727093-0"}' command is executed.
14:28:58 INFO  : Device configured successfully with "C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/hello_world/_ide/bitstream/ZynqDesign_wrapper.bit"
14:28:58 INFO  : targets -set -nocase -filter {name =~"APU*"}
14:28:59 INFO  : loadhw -hw C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/platform/export/platform/hw/ZynqDesign_wrapper.xsa -mem-ranges [list {0x40000000 0xbfffffff}]
14:28:59 INFO  : configparams force-mem-access 1
14:28:59 INFO  : targets -set -nocase -filter {name =~"APU*"}
14:28:59 INFO  : source C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/hello_world/_ide/psinit/ps7_init.tcl
14:28:59 INFO  : ps7_init
14:28:59 INFO  : ps7_post_config
14:28:59 INFO  : targets -set -nocase -filter {name =~ "*A9*#0"}
14:28:59 INFO  : rst -processor
14:28:59 INFO  : dow C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/workspace/TA/arm_xc7z0x0_le_hard/make/TA.elf
14:28:59 INFO  : bpadd main
14:28:59 INFO  : con
14:28:59 INFO  : configparams force-mem-access 0
14:29:01 INFO  : Testing the connection for 127.0.0.1
14:53:06 INFO  : disconnect
19:27:48 INFO  : connect -url tcp:127.0.0.1:3121
19:27:48 INFO  : bpremove -all
19:27:48 INFO  : Context for 'APU' is selected.
19:27:48 INFO  : System reset is completed.
19:27:51 INFO  : 'after 3000' command is executed.
19:27:51 INFO  : 'targets -set -filter {jtag_cable_name =~ "Digilent JTAG-SMT2 210251A08870" && level==0 && jtag_device_ctx=="jsn-JTAG-SMT2-210251A08870-23727093-0"}' command is executed.
19:27:53 INFO  : Device configured successfully with "C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/hello_world/_ide/bitstream/ZynqDesign_wrapper.bit"
19:27:53 INFO  : targets -set -nocase -filter {name =~"APU*"}
19:27:54 INFO  : loadhw -hw C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/platform/export/platform/hw/ZynqDesign_wrapper.xsa -mem-ranges [list {0x40000000 0xbfffffff}]
19:27:54 INFO  : configparams force-mem-access 1
19:27:54 INFO  : targets -set -nocase -filter {name =~"APU*"}
19:27:54 INFO  : source C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/hello_world/_ide/psinit/ps7_init.tcl
19:27:54 INFO  : ps7_init
19:27:54 INFO  : ps7_post_config
19:27:55 INFO  : targets -set -nocase -filter {name =~ "*A9*#0"}
19:27:55 INFO  : rst -processor
19:27:55 INFO  : dow C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/workspace/TA/arm_xc7z0x0_le_hard/make/TA.elf
19:27:55 INFO  : bpadd main
19:27:55 INFO  : con
19:27:55 INFO  : configparams force-mem-access 0
19:27:56 INFO  : Testing the connection for 127.0.0.1
19:28:01 INFO  : disconnect
19:33:17 INFO  : connect -url tcp:127.0.0.1:3121
19:33:17 INFO  : bpremove -all
19:33:17 INFO  : Context for 'APU' is selected.
19:33:17 INFO  : System reset is completed.
19:33:20 INFO  : 'after 3000' command is executed.
19:33:20 INFO  : 'targets -set -filter {jtag_cable_name =~ "Digilent JTAG-SMT2 210251A08870" && level==0 && jtag_device_ctx=="jsn-JTAG-SMT2-210251A08870-23727093-0"}' command is executed.
19:33:22 INFO  : Device configured successfully with "C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/hello_world/_ide/bitstream/ZynqDesign_wrapper.bit"
19:33:22 INFO  : targets -set -nocase -filter {name =~"APU*"}
19:33:24 INFO  : loadhw -hw C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/platform/export/platform/hw/ZynqDesign_wrapper.xsa -mem-ranges [list {0x40000000 0xbfffffff}]
19:33:24 INFO  : configparams force-mem-access 1
19:33:24 INFO  : targets -set -nocase -filter {name =~"APU*"}
19:33:24 INFO  : source C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/hello_world/_ide/psinit/ps7_init.tcl
19:33:24 INFO  : ps7_init
19:33:24 INFO  : ps7_post_config
19:33:24 INFO  : targets -set -nocase -filter {name =~ "*A9*#0"}
19:33:24 INFO  : rst -processor
19:33:24 INFO  : dow C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/workspace/TA/arm_xc7z0x0_le_hard/make/TA.elf
19:33:24 INFO  : bpadd main
19:33:24 INFO  : con
19:33:24 INFO  : configparams force-mem-access 0
19:33:25 INFO  : Testing the connection for 127.0.0.1
20:11:35 INFO  : connection terminated
20:11:35 INFO  : connection terminated
20:11:35 INFO  : scheduling shutdown timer for 2 seconds ...
19:45:47 INFO  : updated workspace path: C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis
19:45:47 INFO  : created .gitignore file for the project C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis
19:46:34 INFO  : connection terminated
19:46:34 INFO  : connection terminated
19:46:34 INFO  : scheduling shutdown timer for 2 seconds ...
19:46:36 INFO  : Terminating Vitis C++ Server
19:46:36 INFO  : Vitis C++ Server terminated
19:48:32 INFO  : updated workspace path: C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis
19:48:32 INFO  : created .gitignore file for the project C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis
19:48:42 INFO  : XSDB server has started successfully from frontend.
19:48:42 INFO  : It has taken  3secs.
19:48:43 INFO  : Connection to XSDB Server established.
19:48:43 INFO  : It has taken  1secs.
19:48:46 INFO  : connect -url tcp:127.0.0.1:3121
19:48:46 INFO  : Done
19:48:51 INFO  : bpremove -all
19:48:51 INFO  : Context for 'APU' is selected.
19:48:51 INFO  : System reset is completed.
19:48:54 INFO  : 'after 3000' command is executed.
19:48:54 INFO  : 'targets -set -filter {jtag_cable_name =~ "Digilent JTAG-SMT2 210251A08870" && level==0 && jtag_device_ctx=="jsn-JTAG-SMT2-210251A08870-23727093-0"}' command is executed.
19:48:57 INFO  : Device configured successfully with "C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/hello_world/_ide/bitstream/ZynqDesign_wrapper.bit"
19:48:57 INFO  : targets -set -nocase -filter {name =~"APU*"}
19:48:57 INFO  : loadhw -hw C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/platform/export/platform/hw/ZynqDesign_wrapper.xsa -mem-ranges [list {0x40000000 0xbfffffff}]
19:48:57 INFO  : configparams force-mem-access 1
19:48:57 INFO  : targets -set -nocase -filter {name =~"APU*"}
19:48:57 INFO  : source C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/hello_world/_ide/psinit/ps7_init.tcl
19:48:58 INFO  : ps7_init
19:48:58 INFO  : ps7_post_config
19:48:58 INFO  : targets -set -nocase -filter {name =~ "*A9*#0"}
19:48:58 INFO  : rst -processor
19:48:59 INFO  : dow C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/workspace/TA/arm_xc7z0x0_le_hard/make/TA.elf
19:48:59 INFO  : con
19:48:59 INFO  : configparams force-mem-access 0
19:49:00 INFO  : Testing the connection for 127.0.0.1
19:51:57 INFO  : disconnect
19:52:07 INFO  : connect -url tcp:127.0.0.1:3121
19:52:07 INFO  : bpremove -all
19:52:08 INFO  : Context for 'APU' is selected.
19:52:08 INFO  : System reset is completed.
19:52:11 INFO  : 'after 3000' command is executed.
19:52:11 INFO  : 'targets -set -filter {jtag_cable_name =~ "Digilent JTAG-SMT2 210251A08870" && level==0 && jtag_device_ctx=="jsn-JTAG-SMT2-210251A08870-23727093-0"}' command is executed.
19:52:13 INFO  : Device configured successfully with "C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/hello_world/_ide/bitstream/ZynqDesign_wrapper.bit"
19:52:13 INFO  : targets -set -nocase -filter {name =~"APU*"}
19:52:17 INFO  : loadhw -hw C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/platform/export/platform/hw/ZynqDesign_wrapper.xsa -mem-ranges [list {0x40000000 0xbfffffff}]
19:52:17 INFO  : configparams force-mem-access 1
19:52:17 INFO  : targets -set -nocase -filter {name =~"APU*"}
19:52:17 INFO  : source C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/hello_world/_ide/psinit/ps7_init.tcl
19:52:18 INFO  : ps7_init
19:52:18 INFO  : ps7_post_config
19:52:18 INFO  : targets -set -nocase -filter {name =~ "*A9*#0"}
19:52:18 INFO  : rst -processor
19:52:19 INFO  : dow C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/workspace/TA/arm_xc7z0x0_le_hard/make/TA.elf
19:52:19 INFO  : con
19:52:19 INFO  : configparams force-mem-access 0
19:52:20 INFO  : Testing the connection for 127.0.0.1
19:53:20 INFO  : disconnect
19:53:39 INFO  : connection terminated
19:53:39 INFO  : connection terminated
19:53:39 INFO  : scheduling shutdown timer for 2 seconds ...
19:53:41 INFO  : Terminating Vitis C++ Server
19:53:41 INFO  : Vitis C++ Server terminated
20:04:15 INFO  : updated workspace path: C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis
20:04:15 INFO  : created .gitignore file for the project C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis
20:04:43 INFO  : XSDB server has started successfully from frontend.
20:04:43 INFO  : It has taken  3secs.
20:04:44 INFO  : Connection to XSDB Server established.
20:04:44 INFO  : It has taken  1secs.
20:04:44 INFO  : Done
20:04:47 INFO  : connect -url tcp:127.0.0.1:3121
20:04:51 INFO  : bpremove -all
20:04:51 INFO  : Context for 'APU' is selected.
20:04:51 INFO  : System reset is completed.
20:04:54 INFO  : 'after 3000' command is executed.
20:04:54 INFO  : 'targets -set -filter {jtag_cable_name =~ "Digilent JTAG-SMT2 210251A08870" && level==0 && jtag_device_ctx=="jsn-JTAG-SMT2-210251A08870-23727093-0"}' command is executed.
20:04:56 INFO  : Device configured successfully with "C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/hello_world/_ide/bitstream/ZynqDesign_wrapper.bit"
20:04:56 INFO  : targets -set -nocase -filter {name =~"APU*"}
20:04:57 INFO  : loadhw -hw C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/platform/export/platform/hw/ZynqDesign_wrapper.xsa -mem-ranges [list {0x40000000 0xbfffffff}]
20:04:57 INFO  : configparams force-mem-access 1
20:04:57 INFO  : targets -set -nocase -filter {name =~"APU*"}
20:04:57 INFO  : source C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/hello_world/_ide/psinit/ps7_init.tcl
20:04:58 INFO  : ps7_init
20:04:58 INFO  : ps7_post_config
20:04:58 INFO  : targets -set -nocase -filter {name =~ "*A9*#0"}
20:04:58 INFO  : rst -processor
20:04:59 INFO  : dow C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/workspace/TA/arm_xc7z0x0_le_hard/make/TA.elf
20:04:59 INFO  : con
20:04:59 INFO  : configparams force-mem-access 0
20:05:00 INFO  : Testing the connection for 127.0.0.1
20:12:14 INFO  : connect -url tcp:127.0.0.1:3121
20:12:14 INFO  : bpremove -all
20:12:14 INFO  : Context for 'APU' is selected.
20:12:14 INFO  : System reset is completed.
20:12:17 INFO  : 'after 3000' command is executed.
20:12:18 INFO  : 'targets -set -filter {jtag_cable_name =~ "Digilent JTAG-SMT2 210251A08870" && level==0 && jtag_device_ctx=="jsn-JTAG-SMT2-210251A08870-23727093-0"}' command is executed.
20:12:20 INFO  : Device configured successfully with "C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/hello_world/_ide/bitstream/ZynqDesign_wrapper.bit"
20:12:20 INFO  : targets -set -nocase -filter {name =~"APU*"}
20:12:20 INFO  : loadhw -hw C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/platform/export/platform/hw/ZynqDesign_wrapper.xsa -mem-ranges [list {0x40000000 0xbfffffff}]
20:12:20 INFO  : configparams force-mem-access 1
20:12:20 INFO  : targets -set -nocase -filter {name =~"APU*"}
20:12:20 INFO  : source C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/hello_world/_ide/psinit/ps7_init.tcl
20:12:21 INFO  : ps7_init
20:12:21 INFO  : ps7_post_config
20:12:21 INFO  : targets -set -nocase -filter {name =~ "*A9*#0"}
20:12:21 INFO  : rst -processor
20:12:22 INFO  : dow C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/workspace/TA/arm_xc7z0x0_le_hard/make/TA.elf
20:12:22 INFO  : con
20:12:22 INFO  : configparams force-mem-access 0
20:12:23 INFO  : Testing the connection for 127.0.0.1
20:12:31 INFO  : disconnect
20:13:03 INFO  : connection terminated
20:13:03 INFO  : connection terminated
20:13:03 INFO  : scheduling shutdown timer for 2 seconds ...
20:13:03 INFO  : scheduling shutdown timer for 2 seconds ...
20:13:05 INFO  : Terminating Vitis C++ Server
20:13:05 INFO  : Terminating Vitis C++ Server
20:13:05 INFO  : Vitis C++ Server terminated
20:13:05 INFO  : Vitis C++ Server terminated
21:13:38 INFO  : updated workspace path: C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis
21:13:52 INFO  : XSDB server has started successfully from frontend.
21:13:52 INFO  : It has taken  7secs.
21:13:53 INFO  : Connection to XSDB Server established.
21:13:53 INFO  : It has taken  1secs.
21:13:53 INFO  : Done
21:13:58 INFO  : connect -url tcp:127.0.0.1:3121
21:14:08 INFO  : bpremove -all
21:14:09 INFO  : Context for 'APU' is selected.
21:14:09 INFO  : System reset is completed.
21:14:12 INFO  : 'after 3000' command is executed.
21:14:12 INFO  : 'targets -set -filter {jtag_cable_name =~ "Digilent JTAG-SMT2 210251A08870" && level==0 && jtag_device_ctx=="jsn-JTAG-SMT2-210251A08870-23727093-0"}' command is executed.
21:14:15 INFO  : Device configured successfully with "C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/hello_world/_ide/bitstream/ZynqDesign_wrapper.bit"
21:14:15 INFO  : targets -set -nocase -filter {name =~"APU*"}
21:14:18 INFO  : loadhw -hw C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/platform/export/platform/hw/ZynqDesign_wrapper.xsa -mem-ranges [list {0x40000000 0xbfffffff}]
21:14:18 INFO  : configparams force-mem-access 1
21:14:18 INFO  : targets -set -nocase -filter {name =~"APU*"}
21:14:18 INFO  : source C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/hello_world/_ide/psinit/ps7_init.tcl
21:14:19 INFO  : ps7_init
21:14:19 INFO  : ps7_post_config
21:14:19 INFO  : targets -set -nocase -filter {name =~ "*A9*#0"}
21:14:19 INFO  : rst -processor
21:14:20 INFO  : dow C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/workspace/TA/arm_xc7z0x0_le_hard/make/TA.elf
21:14:21 INFO  : con
21:14:21 INFO  : configparams force-mem-access 0
21:14:22 INFO  : Testing the connection for 127.0.0.1
21:15:36 INFO  : connect -url tcp:127.0.0.1:3121
21:15:36 INFO  : bpremove -all
21:15:36 INFO  : Context for 'APU' is selected.
21:15:36 INFO  : System reset is completed.
21:15:39 INFO  : 'after 3000' command is executed.
21:15:40 INFO  : disconnect
21:15:40 INFO  : 'targets -set -filter {jtag_cable_name =~ "Digilent JTAG-SMT2 210251A08870" && level==0 && jtag_device_ctx=="jsn-JTAG-SMT2-210251A08870-23727093-0"}' command is executed.
21:15:42 INFO  : Device configured successfully with "C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/hello_world/_ide/bitstream/ZynqDesign_wrapper.bit"
21:15:42 INFO  : targets -set -nocase -filter {name =~"APU*"}
21:15:53 INFO  : loadhw -hw C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/platform/export/platform/hw/ZynqDesign_wrapper.xsa -mem-ranges [list {0x40000000 0xbfffffff}]
21:15:53 INFO  : configparams force-mem-access 1
21:15:53 INFO  : targets -set -nocase -filter {name =~"APU*"}
21:15:53 INFO  : source C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/hello_world/_ide/psinit/ps7_init.tcl
21:15:55 INFO  : ps7_init
21:15:55 INFO  : ps7_post_config
21:15:55 INFO  : targets -set -nocase -filter {name =~ "*A9*#0"}
21:15:55 INFO  : rst -processor
21:15:56 INFO  : dow C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/workspace/TA/arm_xc7z0x0_le_hard/make/TA.elf
21:15:56 INFO  : con
21:15:57 INFO  : configparams force-mem-access 0
21:15:58 INFO  : Testing the connection for 127.0.0.1
21:16:06 INFO  : disconnect
21:18:40 INFO  : connect -url tcp:127.0.0.1:3121
21:18:40 INFO  : bpremove -all
21:18:41 INFO  : Context for 'APU' is selected.
21:18:41 INFO  : System reset is completed.
21:18:44 INFO  : 'after 3000' command is executed.
21:18:45 INFO  : 'targets -set -filter {jtag_cable_name =~ "Digilent JTAG-SMT2 210251A08870" && level==0 && jtag_device_ctx=="jsn-JTAG-SMT2-210251A08870-23727093-0"}' command is executed.
21:18:47 INFO  : Device configured successfully with "C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/hello_world/_ide/bitstream/ZynqDesign_wrapper.bit"
21:18:47 INFO  : targets -set -nocase -filter {name =~"APU*"}
21:19:00 INFO  : loadhw -hw C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/platform/export/platform/hw/ZynqDesign_wrapper.xsa -mem-ranges [list {0x40000000 0xbfffffff}]
21:19:00 INFO  : configparams force-mem-access 1
21:19:00 INFO  : targets -set -nocase -filter {name =~"APU*"}
21:19:01 INFO  : source C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/hello_world/_ide/psinit/ps7_init.tcl
21:19:02 INFO  : ps7_init
21:19:02 INFO  : ps7_post_config
21:19:02 INFO  : targets -set -nocase -filter {name =~ "*A9*#0"}
21:19:02 INFO  : rst -processor
21:19:04 INFO  : dow C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/workspace/TA/arm_xc7z0x0_le_hard/make/TA.elf
21:19:04 INFO  : con
21:19:04 INFO  : configparams force-mem-access 0
21:19:05 INFO  : Testing the connection for 127.0.0.1
21:21:33 INFO  : disconnect
21:22:04 INFO  : connect -url tcp:127.0.0.1:3121
21:22:04 INFO  : bpremove -all
21:22:04 INFO  : Context for 'APU' is selected.
21:22:05 INFO  : System reset is completed.
21:22:08 INFO  : 'after 3000' command is executed.
21:22:08 INFO  : 'targets -set -filter {jtag_cable_name =~ "Digilent JTAG-SMT2 210251A08870" && level==0 && jtag_device_ctx=="jsn-JTAG-SMT2-210251A08870-23727093-0"}' command is executed.
21:22:10 INFO  : Device configured successfully with "C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/hello_world/_ide/bitstream/ZynqDesign_wrapper.bit"
21:22:11 INFO  : targets -set -nocase -filter {name =~"APU*"}
21:22:24 INFO  : loadhw -hw C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/platform/export/platform/hw/ZynqDesign_wrapper.xsa -mem-ranges [list {0x40000000 0xbfffffff}]
21:22:24 INFO  : configparams force-mem-access 1
21:22:24 INFO  : targets -set -nocase -filter {name =~"APU*"}
21:22:24 INFO  : source C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/hello_world/_ide/psinit/ps7_init.tcl
21:22:25 INFO  : ps7_init
21:22:25 INFO  : ps7_post_config
21:22:25 INFO  : targets -set -nocase -filter {name =~ "*A9*#0"}
21:22:25 INFO  : rst -processor
21:22:27 INFO  : dow C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/workspace/TA/arm_xc7z0x0_le_hard/make/TA.elf
21:22:27 INFO  : con
21:22:27 INFO  : configparams force-mem-access 0
21:22:28 INFO  : Testing the connection for 127.0.0.1
21:28:43 INFO  : connection terminated
21:28:43 INFO  : connection terminated
21:28:43 INFO  : scheduling shutdown timer for 2 seconds ...
21:28:46 INFO  : Terminating Vitis C++ Server
21:28:46 INFO  : Vitis C++ Server terminated
21:57:12 INFO  : updated workspace path: C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis
21:57:14 INFO  : Successfully created repository data at C:\Users\<USER>\AppData\Local\Temp\rigel_lopper_lymz17370445202316480584
21:57:14 INFO  : cmd.exe, /C, C:\Xilinx\VitisEmbedded\Vitis\2024.2\tps\win64\lopper-1.1.0\env\Scripts\activate.bat && python C:\Xilinx\VitisEmbedded\Vitis\2024.2\data\embeddedsw\scripts\pyesw\\get_template_data.py -d C:\Users\<USER>\AppData\Local\Temp\rigel_lopper_lymz5485721602174923740 -r C:\Users\<USER>\AppData\Local\Temp\rigel_lopper_lymz17370445202316480584\.repo.yaml && C:\Xilinx\VitisEmbedded\Vitis\2024.2\tps\win64\lopper-1.1.0\env\Scripts\deactivate.bat
21:57:14 INFO  : Embedded Template List generated successfully
21:57:14 INFO  : cmd.exe, /C, C:\Xilinx\VitisEmbedded\Vitis\2024.2\tps\win64\lopper-1.1.0\env\Scripts\activate.bat && python C:\Xilinx\VitisEmbedded\Vitis\2024.2\data\embeddedsw\scripts\pyesw\\get_template_data.py -d C:\Users\<USER>\AppData\Local\Temp\rigel_lopper_lymz18310148259263747511 -r C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\_ide\.wsdata\.repo.yaml && C:\Xilinx\VitisEmbedded\Vitis\2024.2\tps\win64\lopper-1.1.0\env\Scripts\deactivate.bat
21:57:15 INFO  : Embedded Template List generated successfully
21:57:20 INFO  : BSP build will be initiated for Domain zynq_fsbl
21:57:20 INFO  : Generating BSP for the domain C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\platform\zynq_fsbl\zynq_fsbl_bsp
21:57:20 INFO  : lopper command to reconfigure BSP :python C:\Xilinx\VitisEmbedded\Vitis\2024.2\data\embeddedsw/scripts/pyesw//reconfig_bsp.py -d C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\platform\zynq_fsbl\zynq_fsbl_bsp
21:57:20 INFO  : cmd.exe, /C, C:\Xilinx\VitisEmbedded\Vitis\2024.2\tps\win64\lopper-1.1.0\env\Scripts\activate.bat && python C:\Xilinx\VitisEmbedded\Vitis\2024.2\data\embeddedsw\scripts\pyesw\\reconfig_bsp.py -d C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\platform\zynq_fsbl\zynq_fsbl_bsp && C:\Xilinx\VitisEmbedded\Vitis\2024.2\tps\win64\lopper-1.1.0\env\Scripts\deactivate.bat
21:57:20 INFO  : BSP build will be initiated for Domain standalone_ps7_cortexa9_0
21:57:20 INFO  : Generating BSP for the domain C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\platform\ps7_cortexa9_0\standalone_ps7_cortexa9_0\bsp
21:57:20 INFO  : lopper command to reconfigure BSP :python C:\Xilinx\VitisEmbedded\Vitis\2024.2\data\embeddedsw/scripts/pyesw//reconfig_bsp.py -d C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\platform\ps7_cortexa9_0\standalone_ps7_cortexa9_0\bsp
21:57:20 INFO  : cmd.exe, /C, C:\Xilinx\VitisEmbedded\Vitis\2024.2\tps\win64\lopper-1.1.0\env\Scripts\activate.bat && python C:\Xilinx\VitisEmbedded\Vitis\2024.2\data\embeddedsw\scripts\pyesw\\reconfig_bsp.py -d C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\platform\ps7_cortexa9_0\standalone_ps7_cortexa9_0\bsp && C:\Xilinx\VitisEmbedded\Vitis\2024.2\tps\win64\lopper-1.1.0\env\Scripts\deactivate.bat
21:57:20 INFO  : Domain reconfigured successfully
21:57:20 INFO  : Generating BSP for the domain C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\platform\zynq_fsbl\zynq_fsbl_bsp
21:57:20 INFO  : lopper command to build BSP :python C:\Xilinx\VitisEmbedded\Vitis\2024.2\data\embeddedsw/scripts/pyesw//build_bsp.py -d C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\platform\zynq_fsbl\zynq_fsbl_bsp
21:57:20 INFO  : cmd.exe, /C, C:\Xilinx\VitisEmbedded\Vitis\2024.2\tps\win64\lopper-1.1.0\env\Scripts\activate.bat && python C:\Xilinx\VitisEmbedded\Vitis\2024.2\data\embeddedsw\scripts\pyesw\\build_bsp.py -d C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\platform\zynq_fsbl\zynq_fsbl_bsp && C:\Xilinx\VitisEmbedded\Vitis\2024.2\tps\win64\lopper-1.1.0\env\Scripts\deactivate.bat
21:57:20 INFO  : Domain reconfigured successfully
21:57:20 INFO  : Generating BSP for the domain C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\platform\ps7_cortexa9_0\standalone_ps7_cortexa9_0\bsp
21:57:20 INFO  : lopper command to build BSP :python C:\Xilinx\VitisEmbedded\Vitis\2024.2\data\embeddedsw/scripts/pyesw//build_bsp.py -d C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\platform\ps7_cortexa9_0\standalone_ps7_cortexa9_0\bsp
21:57:20 INFO  : cmd.exe, /C, C:\Xilinx\VitisEmbedded\Vitis\2024.2\tps\win64\lopper-1.1.0\env\Scripts\activate.bat && python C:\Xilinx\VitisEmbedded\Vitis\2024.2\data\embeddedsw\scripts\pyesw\\build_bsp.py -d C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\platform\ps7_cortexa9_0\standalone_ps7_cortexa9_0\bsp && C:\Xilinx\VitisEmbedded\Vitis\2024.2\tps\win64\lopper-1.1.0\env\Scripts\deactivate.bat
21:57:22 INFO  : Domain build successfully
21:57:22 INFO  : Domain build successfully
21:57:22 INFO  : Generating Boot ELFs
21:57:22 INFO  : lopper command to retarget baremetal application :python C:\Xilinx\VitisEmbedded\Vitis\2024.2\data\embeddedsw/scripts/pyesw//retarget_app.py -s C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\platform\zynq_fsbl -d C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\platform\zynq_fsbl\zynq_fsbl_bsp -b C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\platform\zynq_fsbl\build
21:57:22 INFO  : cmd.exe, /C, C:\Xilinx\VitisEmbedded\Vitis\2024.2\tps\win64\lopper-1.1.0\env\Scripts\activate.bat && python C:\Xilinx\VitisEmbedded\Vitis\2024.2\data\embeddedsw\scripts\pyesw\\retarget_app.py -s C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\platform\zynq_fsbl -d C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\platform\zynq_fsbl\zynq_fsbl_bsp -b C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\platform\zynq_fsbl\build && C:\Xilinx\VitisEmbedded\Vitis\2024.2\tps\win64\lopper-1.1.0\env\Scripts\deactivate.bat
21:57:22 INFO  : Successfully retargeted Application C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\platform\zynq_fsbl
21:57:22 INFO  : lopper command to build baremetal application :python C:\Xilinx\VitisEmbedded\Vitis\2024.2\data\embeddedsw/scripts/pyesw//build_app.py -s C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\platform\zynq_fsbl -b C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\platform\zynq_fsbl\build
21:57:22 INFO  : cmd.exe, /C, C:\Xilinx\VitisEmbedded\Vitis\2024.2\tps\win64\lopper-1.1.0\env\Scripts\activate.bat && python C:\Xilinx\VitisEmbedded\Vitis\2024.2\data\embeddedsw\scripts\pyesw\\build_app.py -s C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\platform\zynq_fsbl -b C:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\platform\zynq_fsbl\build && C:\Xilinx\VitisEmbedded\Vitis\2024.2\tps\win64\lopper-1.1.0\env\Scripts\deactivate.bat
21:57:25 INFO  : Successfully building the baremetal applicationC:\Users\<USER>\Documents\Works\ML007\tool_ide_1\target\vitis\platform\zynq_fsbl\build
21:57:25 INFO  : Generating Export directory
21:57:25 INFO  : Platform Build Finished successfully.
21:57:31 INFO  : XSDB server has started successfully from frontend.
21:57:31 INFO  : It has taken  2secs.
21:57:32 INFO  : Connection to XSDB Server established.
21:57:32 INFO  : It has taken  1secs.
21:57:35 INFO  : Done
21:57:35 INFO  : connect -url tcp:127.0.0.1:3121
21:57:36 INFO  : bpremove -all
21:57:36 INFO  : Context for 'APU' is selected.
21:57:36 INFO  : System reset is completed.
21:57:39 INFO  : 'after 3000' command is executed.
21:57:39 INFO  : 'targets -set -filter {jtag_cable_name =~ "Digilent JTAG-SMT2 210251A08870" && level==0 && jtag_device_ctx=="jsn-JTAG-SMT2-210251A08870-23727093-0"}' command is executed.
21:57:42 INFO  : Device configured successfully with "C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/hello_world/_ide/bitstream/ZynqDesign_wrapper.bit"
21:57:42 INFO  : targets -set -nocase -filter {name =~"APU*"}
21:57:42 INFO  : loadhw -hw C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/platform/export/platform/hw/ZynqDesign_wrapper.xsa -mem-ranges [list {0x40000000 0xbfffffff}]
21:57:42 INFO  : configparams force-mem-access 1
21:57:42 INFO  : targets -set -nocase -filter {name =~"APU*"}
21:57:42 INFO  : source C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/hello_world/_ide/psinit/ps7_init.tcl
21:57:42 INFO  : ps7_init
21:57:42 INFO  : ps7_post_config
21:57:42 INFO  : targets -set -nocase -filter {name =~ "*A9*#0"}
21:57:42 INFO  : rst -processor
21:57:43 INFO  : dow C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/workspace/TA/arm_xc7z0x0_le_hard/make/TA.elf
21:57:43 INFO  : con
21:57:43 INFO  : configparams force-mem-access 0
21:57:44 INFO  : Testing the connection for 127.0.0.1
21:57:48 INFO  : disconnect
22:09:25 INFO  : connect -url tcp:127.0.0.1:3121
22:09:25 INFO  : bpremove -all
22:09:26 INFO  : Context for 'APU' is selected.
22:09:26 INFO  : System reset is completed.
22:09:29 INFO  : 'after 3000' command is executed.
22:09:29 INFO  : 'targets -set -filter {jtag_cable_name =~ "Digilent JTAG-SMT2 210251A08870" && level==0 && jtag_device_ctx=="jsn-JTAG-SMT2-210251A08870-23727093-0"}' command is executed.
22:09:32 INFO  : Device configured successfully with "C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/hello_world/_ide/bitstream/ZynqDesign_wrapper.bit"
22:09:32 INFO  : targets -set -nocase -filter {name =~"APU*"}
22:09:33 INFO  : loadhw -hw C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/platform/export/platform/hw/ZynqDesign_wrapper.xsa -mem-ranges [list {0x40000000 0xbfffffff}]
22:09:33 INFO  : configparams force-mem-access 1
22:09:33 INFO  : targets -set -nocase -filter {name =~"APU*"}
22:09:33 INFO  : source C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/hello_world/_ide/psinit/ps7_init.tcl
22:09:33 INFO  : ps7_init
22:09:33 INFO  : ps7_post_config
22:09:33 INFO  : targets -set -nocase -filter {name =~ "*A9*#0"}
22:09:33 INFO  : rst -processor
22:09:34 INFO  : dow C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/workspace/TA/arm_xc7z0x0_le_hard/make/TA.elf
22:09:34 INFO  : con
22:09:34 INFO  : configparams force-mem-access 0
22:09:35 INFO  : Testing the connection for 127.0.0.1
22:10:23 INFO  : connect -url tcp:127.0.0.1:3121
22:10:23 INFO  : bpremove -all
22:10:23 INFO  : Context for 'APU' is selected.
22:10:23 INFO  : System reset is completed.
22:10:26 INFO  : 'after 3000' command is executed.
22:10:26 INFO  : 'targets -set -filter {jtag_cable_name =~ "Digilent JTAG-SMT2 210251A08870" && level==0 && jtag_device_ctx=="jsn-JTAG-SMT2-210251A08870-23727093-0"}' command is executed.
22:10:28 INFO  : Device configured successfully with "C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/hello_world/_ide/bitstream/ZynqDesign_wrapper.bit"
22:10:28 INFO  : targets -set -nocase -filter {name =~"APU*"}
22:10:28 INFO  : loadhw -hw C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/platform/export/platform/hw/ZynqDesign_wrapper.xsa -mem-ranges [list {0x40000000 0xbfffffff}]
22:10:28 INFO  : configparams force-mem-access 1
22:10:28 INFO  : targets -set -nocase -filter {name =~"APU*"}
22:10:28 INFO  : source C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/hello_world/_ide/psinit/ps7_init.tcl
22:10:29 INFO  : ps7_init
22:10:29 INFO  : ps7_post_config
22:10:29 INFO  : targets -set -nocase -filter {name =~ "*A9*#0"}
22:10:29 INFO  : rst -processor
22:10:29 INFO  : dow C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/workspace/TA/arm_xc7z0x0_le_hard/make/TA.elf
22:10:29 INFO  : con
22:10:29 INFO  : configparams force-mem-access 0
22:10:30 INFO  : Testing the connection for 127.0.0.1
22:12:23 INFO  : disconnect
22:12:26 INFO  : connect -url tcp:127.0.0.1:3121
22:12:26 INFO  : bpremove -all
22:12:27 INFO  : Context for 'APU' is selected.
22:12:27 INFO  : System reset is completed.
22:12:30 INFO  : 'after 3000' command is executed.
22:12:30 INFO  : 'targets -set -filter {jtag_cable_name =~ "Digilent JTAG-SMT2 210251A08870" && level==0 && jtag_device_ctx=="jsn-JTAG-SMT2-210251A08870-23727093-0"}' command is executed.
22:12:33 INFO  : Device configured successfully with "C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/hello_world/_ide/bitstream/ZynqDesign_wrapper.bit"
22:12:33 INFO  : targets -set -nocase -filter {name =~"APU*"}
22:12:34 INFO  : loadhw -hw C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/platform/export/platform/hw/ZynqDesign_wrapper.xsa -mem-ranges [list {0x40000000 0xbfffffff}]
22:12:34 INFO  : configparams force-mem-access 1
22:12:34 INFO  : targets -set -nocase -filter {name =~"APU*"}
22:12:34 INFO  : source C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/hello_world/_ide/psinit/ps7_init.tcl
22:12:34 INFO  : ps7_init
22:12:34 INFO  : ps7_post_config
22:12:34 INFO  : targets -set -nocase -filter {name =~ "*A9*#0"}
22:12:34 INFO  : rst -processor
22:12:35 INFO  : dow C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/workspace/TA/arm_xc7z0x0_le_hard/make/TA.elf
22:12:35 INFO  : bpadd main
22:12:35 INFO  : con
22:12:35 INFO  : configparams force-mem-access 0
22:12:36 INFO  : Testing the connection for 127.0.0.1
22:13:57 INFO  : disconnect
22:13:58 INFO  : connect -url tcp:127.0.0.1:3121
22:13:58 INFO  : bpremove -all
22:13:58 INFO  : Context for 'APU' is selected.
22:13:58 INFO  : System reset is completed.
22:14:01 INFO  : 'after 3000' command is executed.
22:14:01 INFO  : 'targets -set -filter {jtag_cable_name =~ "Digilent JTAG-SMT2 210251A08870" && level==0 && jtag_device_ctx=="jsn-JTAG-SMT2-210251A08870-23727093-0"}' command is executed.
22:14:03 INFO  : Device configured successfully with "C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/hello_world/_ide/bitstream/ZynqDesign_wrapper.bit"
22:14:03 INFO  : targets -set -nocase -filter {name =~"APU*"}
22:14:04 INFO  : loadhw -hw C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/platform/export/platform/hw/ZynqDesign_wrapper.xsa -mem-ranges [list {0x40000000 0xbfffffff}]
22:14:04 INFO  : configparams force-mem-access 1
22:14:04 INFO  : targets -set -nocase -filter {name =~"APU*"}
22:14:04 INFO  : source C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/hello_world/_ide/psinit/ps7_init.tcl
22:14:04 INFO  : ps7_init
22:14:05 INFO  : ps7_post_config
22:14:05 INFO  : targets -set -nocase -filter {name =~ "*A9*#0"}
22:14:05 INFO  : rst -processor
22:14:05 INFO  : dow C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/workspace/TA/arm_xc7z0x0_le_hard/make/TA.elf
22:14:05 INFO  : bpadd main
22:14:05 INFO  : con
22:14:05 INFO  : configparams force-mem-access 0
22:14:06 INFO  : Testing the connection for 127.0.0.1
22:16:55 INFO  : disconnect
22:17:06 INFO  : connect -url tcp:127.0.0.1:3121
22:17:06 INFO  : bpremove -all
22:17:06 INFO  : Context for 'APU' is selected.
22:17:06 INFO  : System reset is completed.
22:17:09 INFO  : 'after 3000' command is executed.
22:17:09 INFO  : 'targets -set -filter {jtag_cable_name =~ "Digilent JTAG-SMT2 210251A08870" && level==0 && jtag_device_ctx=="jsn-JTAG-SMT2-210251A08870-23727093-0"}' command is executed.
22:17:11 INFO  : Device configured successfully with "C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/hello_world/_ide/bitstream/ZynqDesign_wrapper.bit"
22:17:11 INFO  : targets -set -nocase -filter {name =~"APU*"}
22:17:12 INFO  : loadhw -hw C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/platform/export/platform/hw/ZynqDesign_wrapper.xsa -mem-ranges [list {0x40000000 0xbfffffff}]
22:17:12 INFO  : configparams force-mem-access 1
22:17:12 INFO  : targets -set -nocase -filter {name =~"APU*"}
22:17:12 INFO  : source C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/hello_world/_ide/psinit/ps7_init.tcl
22:17:12 INFO  : ps7_init
22:17:12 INFO  : ps7_post_config
22:17:12 INFO  : targets -set -nocase -filter {name =~ "*A9*#0"}
22:17:12 INFO  : rst -processor
22:17:13 INFO  : dow C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/workspace/TA/arm_xc7z0x0_le_hard/make/TA.elf
22:17:13 INFO  : bpadd main
22:17:13 INFO  : con
22:17:13 INFO  : configparams force-mem-access 0
22:17:14 INFO  : Testing the connection for 127.0.0.1
22:18:15 INFO  : disconnect
22:19:08 INFO  : connect -url tcp:127.0.0.1:3121
22:19:08 INFO  : bpremove -all
22:19:08 INFO  : Context for 'APU' is selected.
22:19:08 INFO  : System reset is completed.
22:19:11 INFO  : 'after 3000' command is executed.
22:19:11 INFO  : 'targets -set -filter {jtag_cable_name =~ "Digilent JTAG-SMT2 210251A08870" && level==0 && jtag_device_ctx=="jsn-JTAG-SMT2-210251A08870-23727093-0"}' command is executed.
22:19:13 INFO  : Device configured successfully with "C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/hello_world/_ide/bitstream/ZynqDesign_wrapper.bit"
22:19:13 INFO  : targets -set -nocase -filter {name =~"APU*"}
22:19:14 INFO  : loadhw -hw C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/platform/export/platform/hw/ZynqDesign_wrapper.xsa -mem-ranges [list {0x40000000 0xbfffffff}]
22:19:14 INFO  : configparams force-mem-access 1
22:19:14 INFO  : targets -set -nocase -filter {name =~"APU*"}
22:19:14 INFO  : source C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/hello_world/_ide/psinit/ps7_init.tcl
22:19:15 INFO  : ps7_init
22:19:15 INFO  : ps7_post_config
22:19:15 INFO  : targets -set -nocase -filter {name =~ "*A9*#0"}
22:19:15 INFO  : rst -processor
22:19:15 INFO  : dow C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/workspace/TA/arm_xc7z0x0_le_hard/make/TA.elf
22:19:15 INFO  : bpadd main
22:19:15 INFO  : con
22:19:15 INFO  : configparams force-mem-access 0
22:19:16 INFO  : Testing the connection for 127.0.0.1
22:44:55 INFO  : disconnect
22:44:55 INFO  : connect -url tcp:127.0.0.1:3121
22:44:55 INFO  : bpremove -all
22:45:04 ERROR : Failed to initialize the hardware Could not find ARM device on the board for connection 'Local'.
Check if the target is in:
1. Split JTAG - No operations are possible with ARM DAP.
2. Non JTAG bootmode - Bootrom may need time to enable DAP.
Please try again.


Troubleshooting hints:
1. Check whether board is connected to system properly.
2. In case of zynq board, check whether Digilent/Xilinx cable switch settings are correct.
3. If you are using Xilinx Platform Cable USB, ensure that status LED is green.
22:45:17 INFO  : connect -url tcp:127.0.0.1:3121
22:45:17 INFO  : bpremove -all
22:45:17 INFO  : Context for 'APU' is selected.
22:45:17 INFO  : System reset is completed.
22:45:20 INFO  : 'after 3000' command is executed.
22:45:20 INFO  : 'targets -set -filter {jtag_cable_name =~ "Digilent JTAG-SMT2 210251A08870" && level==0 && jtag_device_ctx=="jsn-JTAG-SMT2-210251A08870-23727093-0"}' command is executed.
22:45:22 INFO  : Device configured successfully with "C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/hello_world/_ide/bitstream/ZynqDesign_wrapper.bit"
22:45:22 INFO  : targets -set -nocase -filter {name =~"APU*"}
22:45:23 INFO  : loadhw -hw C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/platform/export/platform/hw/ZynqDesign_wrapper.xsa -mem-ranges [list {0x40000000 0xbfffffff}]
22:45:23 INFO  : configparams force-mem-access 1
22:45:23 INFO  : targets -set -nocase -filter {name =~"APU*"}
22:45:23 INFO  : source C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/hello_world/_ide/psinit/ps7_init.tcl
22:45:23 INFO  : ps7_init
22:45:23 INFO  : ps7_post_config
22:45:23 INFO  : targets -set -nocase -filter {name =~ "*A9*#0"}
22:45:23 INFO  : rst -processor
22:45:24 INFO  : dow C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/workspace/TA/arm_xc7z0x0_le_hard/make/TA.elf
22:45:24 INFO  : bpadd main
22:45:24 INFO  : con
22:45:24 INFO  : configparams force-mem-access 0
22:45:25 INFO  : Testing the connection for 127.0.0.1
22:45:38 INFO  : disconnect
22:52:29 INFO  : connect -url tcp:127.0.0.1:3121
22:52:29 INFO  : bpremove -all
22:52:29 INFO  : Context for 'APU' is selected.
22:52:29 INFO  : System reset is completed.
22:52:32 INFO  : 'after 3000' command is executed.
22:52:32 INFO  : 'targets -set -filter {jtag_cable_name =~ "Digilent JTAG-SMT2 210251A08870" && level==0 && jtag_device_ctx=="jsn-JTAG-SMT2-210251A08870-23727093-0"}' command is executed.
22:52:34 INFO  : Device configured successfully with "C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/hello_world/_ide/bitstream/ZynqDesign_wrapper.bit"
22:52:34 INFO  : targets -set -nocase -filter {name =~"APU*"}
22:52:35 INFO  : loadhw -hw C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/platform/export/platform/hw/ZynqDesign_wrapper.xsa -mem-ranges [list {0x40000000 0xbfffffff}]
22:52:35 INFO  : configparams force-mem-access 1
22:52:35 INFO  : targets -set -nocase -filter {name =~"APU*"}
22:52:35 INFO  : source C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/hello_world/_ide/psinit/ps7_init.tcl
22:52:35 INFO  : ps7_init
22:52:35 INFO  : ps7_post_config
22:52:35 INFO  : targets -set -nocase -filter {name =~ "*A9*#0"}
22:52:35 INFO  : rst -processor
22:52:36 INFO  : dow C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/workspace/TA/arm_xc7z0x0_le_hard/make/TA.elf
22:52:36 INFO  : bpadd main
22:52:36 INFO  : con
22:52:36 INFO  : configparams force-mem-access 0
22:52:37 INFO  : Testing the connection for 127.0.0.1
22:54:43 INFO  : disconnect
22:54:44 INFO  : connect -url tcp:127.0.0.1:3121
22:54:44 INFO  : bpremove -all
22:54:44 INFO  : Context for 'APU' is selected.
22:54:44 INFO  : System reset is completed.
22:54:47 INFO  : 'after 3000' command is executed.
22:54:47 INFO  : 'targets -set -filter {jtag_cable_name =~ "Digilent JTAG-SMT2 210251A08870" && level==0 && jtag_device_ctx=="jsn-JTAG-SMT2-210251A08870-23727093-0"}' command is executed.
22:54:49 INFO  : Device configured successfully with "C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/hello_world/_ide/bitstream/ZynqDesign_wrapper.bit"
22:54:49 INFO  : targets -set -nocase -filter {name =~"APU*"}
22:54:50 INFO  : loadhw -hw C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/platform/export/platform/hw/ZynqDesign_wrapper.xsa -mem-ranges [list {0x40000000 0xbfffffff}]
22:54:50 INFO  : configparams force-mem-access 1
22:54:50 INFO  : targets -set -nocase -filter {name =~"APU*"}
22:54:50 INFO  : source C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/hello_world/_ide/psinit/ps7_init.tcl
22:54:51 INFO  : ps7_init
22:54:51 INFO  : ps7_post_config
22:54:51 INFO  : targets -set -nocase -filter {name =~ "*A9*#0"}
22:54:51 INFO  : rst -processor
22:54:51 INFO  : dow C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/workspace/TA/arm_xc7z0x0_le_hard/make/TA.elf
22:54:51 INFO  : bpadd main
22:54:51 INFO  : con
22:54:51 INFO  : configparams force-mem-access 0
22:54:52 INFO  : Testing the connection for 127.0.0.1
22:55:07 INFO  : disconnect
22:55:45 INFO  : connect -url tcp:127.0.0.1:3121
22:55:45 INFO  : bpremove -all
22:55:45 INFO  : Context for 'APU' is selected.
22:55:45 INFO  : System reset is completed.
22:55:48 INFO  : 'after 3000' command is executed.
22:55:48 INFO  : 'targets -set -filter {jtag_cable_name =~ "Digilent JTAG-SMT2 210251A08870" && level==0 && jtag_device_ctx=="jsn-JTAG-SMT2-210251A08870-23727093-0"}' command is executed.
22:55:51 INFO  : Device configured successfully with "C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/hello_world/_ide/bitstream/ZynqDesign_wrapper.bit"
22:55:51 INFO  : targets -set -nocase -filter {name =~"APU*"}
22:55:52 INFO  : loadhw -hw C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/platform/export/platform/hw/ZynqDesign_wrapper.xsa -mem-ranges [list {0x40000000 0xbfffffff}]
22:55:52 INFO  : configparams force-mem-access 1
22:55:52 INFO  : targets -set -nocase -filter {name =~"APU*"}
22:55:52 INFO  : source C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/hello_world/_ide/psinit/ps7_init.tcl
22:55:52 INFO  : ps7_init
22:55:52 INFO  : ps7_post_config
22:55:52 INFO  : targets -set -nocase -filter {name =~ "*A9*#0"}
22:55:52 INFO  : rst -processor
22:55:53 INFO  : dow C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/workspace/TA/arm_xc7z0x0_le_hard/make/TA.elf
22:55:53 INFO  : bpadd main
22:55:53 INFO  : con
22:55:53 INFO  : configparams force-mem-access 0
22:55:54 INFO  : Testing the connection for 127.0.0.1
23:03:11 INFO  : disconnect
23:03:12 INFO  : connect -url tcp:127.0.0.1:3121
23:03:12 INFO  : bpremove -all
23:03:12 INFO  : Context for 'APU' is selected.
23:03:12 INFO  : System reset is completed.
23:03:15 INFO  : 'after 3000' command is executed.
23:03:15 INFO  : 'targets -set -filter {jtag_cable_name =~ "Digilent JTAG-SMT2 210251A08870" && level==0 && jtag_device_ctx=="jsn-JTAG-SMT2-210251A08870-23727093-0"}' command is executed.
23:03:17 INFO  : Device configured successfully with "C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/hello_world/_ide/bitstream/ZynqDesign_wrapper.bit"
23:03:17 INFO  : targets -set -nocase -filter {name =~"APU*"}
23:03:19 INFO  : loadhw -hw C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/platform/export/platform/hw/ZynqDesign_wrapper.xsa -mem-ranges [list {0x40000000 0xbfffffff}]
23:03:19 INFO  : configparams force-mem-access 1
23:03:19 INFO  : targets -set -nocase -filter {name =~"APU*"}
23:03:19 INFO  : source C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/hello_world/_ide/psinit/ps7_init.tcl
23:03:19 INFO  : ps7_init
23:03:19 INFO  : ps7_post_config
23:03:19 INFO  : targets -set -nocase -filter {name =~ "*A9*#0"}
23:03:19 INFO  : rst -processor
23:03:19 INFO  : dow C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/workspace/TA/arm_xc7z0x0_le_hard/make/TA.elf
23:03:19 INFO  : bpadd main
23:03:19 INFO  : con
23:03:19 INFO  : configparams force-mem-access 0
23:03:20 INFO  : Testing the connection for 127.0.0.1
23:14:05 INFO  : disconnect
23:14:08 INFO  : connect -url tcp:127.0.0.1:3121
23:14:08 INFO  : bpremove -all
23:14:08 INFO  : Context for 'APU' is selected.
23:14:08 INFO  : System reset is completed.
23:14:11 INFO  : 'after 3000' command is executed.
23:14:11 INFO  : 'targets -set -filter {jtag_cable_name =~ "Digilent JTAG-SMT2 210251A08870" && level==0 && jtag_device_ctx=="jsn-JTAG-SMT2-210251A08870-23727093-0"}' command is executed.
23:14:13 INFO  : Device configured successfully with "C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/hello_world/_ide/bitstream/ZynqDesign_wrapper.bit"
23:14:13 INFO  : targets -set -nocase -filter {name =~"APU*"}
23:14:15 INFO  : loadhw -hw C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/platform/export/platform/hw/ZynqDesign_wrapper.xsa -mem-ranges [list {0x40000000 0xbfffffff}]
23:14:15 INFO  : configparams force-mem-access 1
23:14:15 INFO  : targets -set -nocase -filter {name =~"APU*"}
23:14:15 INFO  : source C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/hello_world/_ide/psinit/ps7_init.tcl
23:14:15 INFO  : ps7_init
23:14:15 INFO  : ps7_post_config
23:14:15 INFO  : targets -set -nocase -filter {name =~ "*A9*#0"}
23:14:15 INFO  : rst -processor
23:14:15 INFO  : dow C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/workspace/TA/arm_xc7z0x0_le_hard/make/TA.elf
23:14:15 INFO  : bpadd main
23:14:15 INFO  : con
23:14:15 INFO  : configparams force-mem-access 0
23:14:16 INFO  : Testing the connection for 127.0.0.1
23:15:44 INFO  : disconnect
23:15:45 INFO  : connect -url tcp:127.0.0.1:3121
23:15:45 INFO  : bpremove -all
23:15:45 INFO  : Context for 'APU' is selected.
23:15:45 INFO  : System reset is completed.
23:15:48 INFO  : 'after 3000' command is executed.
23:15:48 INFO  : 'targets -set -filter {jtag_cable_name =~ "Digilent JTAG-SMT2 210251A08870" && level==0 && jtag_device_ctx=="jsn-JTAG-SMT2-210251A08870-23727093-0"}' command is executed.
23:15:51 INFO  : Device configured successfully with "C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/hello_world/_ide/bitstream/ZynqDesign_wrapper.bit"
23:15:51 INFO  : targets -set -nocase -filter {name =~"APU*"}
23:15:52 INFO  : loadhw -hw C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/platform/export/platform/hw/ZynqDesign_wrapper.xsa -mem-ranges [list {0x40000000 0xbfffffff}]
23:15:52 INFO  : configparams force-mem-access 1
23:15:52 INFO  : targets -set -nocase -filter {name =~"APU*"}
23:15:52 INFO  : source C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/target/vitis/hello_world/_ide/psinit/ps7_init.tcl
23:15:52 INFO  : ps7_init
23:15:52 INFO  : ps7_post_config
23:15:52 INFO  : targets -set -nocase -filter {name =~ "*A9*#0"}
23:15:52 INFO  : rst -processor
23:15:53 INFO  : dow C:/Users/<USER>/Documents/Works/ML007/tool_ide_1/workspace/TA/arm_xc7z0x0_le_hard/make/TA.elf
23:15:53 INFO  : bpadd main
23:15:53 INFO  : con
23:15:53 INFO  : configparams force-mem-access 0
23:15:54 INFO  : Testing the connection for 127.0.0.1
