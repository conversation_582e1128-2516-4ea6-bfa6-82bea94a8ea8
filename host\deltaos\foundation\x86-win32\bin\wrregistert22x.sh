#!/bin/bash
################################################################
# Name:     wrregistert22x.sh                                  #
# Purpose:  Register an existing Tornado 2.2.x installation to # 
#           the Wind River Workbench                           #
# OS:       Windows and Solaris 9                              #
################################################################
echo "Register a Tornado 2.2.x Installation to Wind River Workbench"
SHELLHOME=`dirname $0`
SHELLHOMEFULL=`(cd $SHELLHOME && pwd)`
PLATFORM=`dirname $SHELLHOMEFULL | xargs basename`
OS=`uname`
if [ ! "$OS" = "windows32" -a ! "$OS" = "SunOS" ]; then
	echo "Tornado 2.2.x registration not possible for this OS, exiting!"
	exit 1
fi
OSVERSION=`uname -r`
if [ "$OS" = "SunOS" -a ! "$OSVERSION" = "5.9" ]; then
	echo "Tornado 2.2.x registration not possible for this OS, exiting!"
	exit 1
fi
if [ "$WIND_HOME" != "" ]; then
	echo "Run this script outside of a Wind River Workbench environment!"
	exit 1
fi
VXWORKS55_SUPPORT_DIR=$SHELLHOME/../../../vxworks-5.5
VXWORKS55_PACKAGE_PROPERTIES=$VXWORKS55_SUPPORT_DIR/package.properties
if [ ! -d "$VXWORKS55_SUPPORT_DIR" ]; then
	echo "Could not find this Workbench's VxWorks 5.5 support!"
	echo "Workbench does not support VxWorks 5.5 in this installation, exiting!"
	exit 1
fi
SUCCESS=false
ROOTDIRS="docs host SETUP share target"
FIRSTARGUMENT=$1
CHOICE=n
if [ "$FIRSTARGUMENT" = "" -a "$PLATFORM" = "x86-win32" ]; then
	REGISTRYVALUE=`wrreadregistryvalue "HKEY_LOCAL_MACHINE" "SOFTWARE\\Wind River Systems" "WIND_BASE" "REG_DWORD" 2>&1`
else
	REGISTRYVALUE=""
fi
if [ ! "$REGISTRYVALUE=" = "" -a ! "$REGISTRYVALUE=" = "Error: The system cannot find the file specified." ]; then
	if [ -d "$REGISTRYVALUE" ]; then
		for rootdir in $ROOTDIRS; do	
			if [ ! -d "$REGISTRYVALUE/$rootdir" ]; then
				SUCCESS=false
				break
			fi
			SUCCESS=true
		done
	fi
	if [ "$SUCCESS" = "true" ]; then
		echo "Found a Tornado 2.2.x installation in your registry: $REGISTRYVALUE"
		echo -n "Do you want to use this installation(y/n)?"
		if [ "$PLATFORM" = "x86-win32" ]; then
			read CHOICE
		else
			read -n 1 CHOICE
		fi
	fi
fi
if [ "$CHOICE" = "n" ]; then
	SUCCESS=false
	echo -n "Please specify the Tornado dir: " 
	while [ "$SUCCESS" = "false" ]; do
		if [ "$FIRSTARGUMENT" = "" ]; then 
			read -r TORNADO_INSTALL_DIR_BACKSLASH
		else
			TORNADO_INSTALL_DIR_BACKSLASH=$FIRSTARGUMENT
			FIRSTARGUMENT=""
		fi
		TORNADO_INSTALL_DIR=`echo -E $TORNADO_INSTALL_DIR_BACKSLASH | tr '\\\' /`
		SUCCESS=false
		if [ -d "$TORNADO_INSTALL_DIR" ]; then
			for rootdir in $ROOTDIRS; do	
				if [ ! -d "$TORNADO_INSTALL_DIR/$rootdir" ]; then
					SUCCESS=false
					break
				fi
				SUCCESS=true
			done
		fi
		if [ "$SUCCESS" = "false" ]; then
			echo -n "Incorrect directory $TORNADO_INSTALL_DIR_BACKSLASH, specify Tornado dir: "
		fi
	done
else
	TORNADO_INSTALL_DIR=$REGISTRYVALUE
fi
echo "Dumping package.properties!"
echo "vxworks55.name=vxworks-5.5" > $VXWORKS55_PACKAGE_PROPERTIES
echo "vxworks55.version=5.5" >> $VXWORKS55_PACKAGE_PROPERTIES
echo "vxworks55.type=platform" >> $VXWORKS55_PACKAGE_PROPERTIES
echo "vxworks55.subtype=vxworks55" >> $VXWORKS55_PACKAGE_PROPERTIES
echo "vxworks55.label=Wind River VxWorks 5.5" >> $VXWORKS55_PACKAGE_PROPERTIES
echo "vxworks55.valid_if=\$(builtin:HostType)==\"x86-win32\" || \$(builtin:HostType)==\"sun4-solaris2\"" >> $VXWORKS55_PACKAGE_PROPERTIES
echo "vxworks55.eval.01=export WIND_HOME=\$(builtin:InstallHome)" >> $VXWORKS55_PACKAGE_PROPERTIES
echo "vxworks55.eval.02=export WIND_BASE=$TORNADO_INSTALL_DIR" | sed -e 's/\//\$\//g' >> $VXWORKS55_PACKAGE_PROPERTIES
echo "vxworks55.eval.03=export WIND_DIAB_PATH=\$(WIND_BASE)\$/host\$/diab" >> $VXWORKS55_PACKAGE_PROPERTIES
echo "vxworks55.eval.04=export DIAB_HOST_TYPE=\$(builtin:DiabHostType)" >> $VXWORKS55_PACKAGE_PROPERTIES
echo "vxworks55.eval.05=export WIND_HOST_TYPE=\$(builtin:HostType)" >> $VXWORKS55_PACKAGE_PROPERTIES
echo "vxworks55.eval.06=addpath PATH \$(WIND_BASE)$/host$/\$(WIND_HOST_TYPE)$/bin" >> $VXWORKS55_PACKAGE_PROPERTIES
echo "vxworks55.eval.07=addpath PATH \$(WIND_DIAB_PATH)$/\$(builtin:DiabHostType)$/bin" >> $VXWORKS55_PACKAGE_PROPERTIES
echo "vxworks55.eval.08=addpath PATH \$(WIND_HOME)$/vxworks-5.5$/host$/\$(WIND_HOST_TYPE)$/bin" >> $VXWORKS55_PACKAGE_PROPERTIES
echo "vxworks55.eval.09=addpath WIND_SAMPLES \$(WIND_HOME)$/vxworks-5.5$/samples" >> $VXWORKS55_PACKAGE_PROPERTIES
echo "vxworks55.eval.10=export DIABLIB=\$(WIND_DIAB_PATH)" >> $VXWORKS55_PACKAGE_PROPERTIES
echo "vxworks55.eval.11=export WIND_PLATFORM=vxworks-5.5" >> $VXWORKS55_PACKAGE_PROPERTIES
echo "vxworks55.eval.12=require [workbench,,2.5,4.0]" >> $VXWORKS55_PACKAGE_PROPERTIES
echo "vxworks55.eval.13=optional components" >> $VXWORKS55_PACKAGE_PROPERTIES
echo "vxworks55.eval.14=optional wrmscomponents" >> $VXWORKS55_PACKAGE_PROPERTIES
if [ "$PLATFORM" = "sun4-solaris2" ]; then
	if [ "$WIND_REGISTRY" = "" ]; then
		WIND_REGISTRY="localhost"
	fi
	echo "vxworks55.eval.15=export WIND_REGISTRY=$WIND_REGISTRY" >> $VXWORKS55_PACKAGE_PROPERTIES
fi
echo "vxworks55.eval.16=addpath LD_LIBRARY_PATH \$(WIND_BASE)$/host$/\$(WIND_HOST_TYPE)$/lib" >> $VXWORKS55_PACKAGE_PROPERTIES
echo "vxworks55.bdp_type=preference" >> $VXWORKS55_PACKAGE_PROPERTIES
echo "vxworks55.bdp_creationcmd=\$(WIND_HOME)\$/vxworks-5.5\$/host\$/\$(WIND_HOST_TYPE)\$/bin\$/bdgen \$(WIND_HOME)\$/vxworks-5.5\$/host\$/resource\$/bdgen %projecttype%" >> $VXWORKS55_PACKAGE_PROPERTIES
echo "Calling postinstaller to pickup the changes"
if [ "$PLATFORM" = "x86-win32" ]; then
	cmd.exe /c `dirname $VXWORKS55_SUPPORT_DIR`/setup/postinstall.bat
else
	sh `dirname $VXWORKS55_SUPPORT_DIR`/setup/postinstall.sh	
fi
sleep 3
