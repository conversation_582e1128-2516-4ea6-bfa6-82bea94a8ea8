
#parameters: file_path , module_name file_name, file_contents
function create_call_lib_file () {

   echo "#include <sysClb.h>" > $1/$2_$3_clb.S
   echo ".text" >> $1/$2_$3_clb.S
   echo "$4">> $1/$2_$3_clb.S
   echo ".end">> $1/$2_$3_clb.S
   ${CC} ${CFLAGS} $1/$2_$3_clb.S -o $1/$2_$3_clb.o
}

echo "file=${1}/${2}"
funcs=`cat ${1}/${2}`

##echo "$funcs"




for fun in $funcs ; do
   if expr "$fun" : 'LLDR_CALLLIB_MODDEF.*' > /tmp/null.txt 2>&1; then
     module_contents=$fun
     echo "module_contents=$module_contents"
     module_name=`echo $module_contents|sed -e 's:.*([^,]*,::'|sed -e 's:"::g'|sed -e 's:)::g'`
     echo "module_name=$module_name"
     create_call_lib_file ${1} $module_name $module_name $module_contents
     break
   fi
   
done

for fun in $funcs ; do
   if expr "$fun" : 'LLDR_CALLLIB_FUNDEF.*' > /tmp/null.txt 2>&1; then
     fun_contents=$fun
     echo "fun_contents=$fun_contents"
     fun_name=`echo $fun_contents|sed -e 's:.*([^,]*,::'|sed -e 's:,.*)::'`
     echo "fun_name=$fun_name"
     create_call_lib_file ${1} $module_name $fun_name $fun_contents
   fi
   
done

##for fun in $funcs ; do
##echo "fun=$fun"
##fun_name=`echo $fun|sed -e 's:.*([^,]*,::'|sed -e 's:,.*)::'`
##echo "fun_name=$fun_name"

##if [ "$fun" != "$fun_name" ] ; then
##  echo "$fun_name is a function"
##fi


##done

##while :; do
##  read ans < ${1}
##  echo $ans
##done
