/* memory-config in eclipse by lwj*/
/*9 Jul 2025 15:13:57 GMT*/

ENTRY(_start)

SECTIONS	{

.text	BLOCK(0x00001000)	:
{

KEEP (*(.vectors))    *(.boot) *(.image_info)   *(.text)    *(.text.*)    *(.gnu.linkonce.t.*)    *(.plt)    *(.gnu_warning)    *(.gcc_execpt_table)    *(.glue_7)    *(.glue_7t)    *(.vfp11_veneer)    *(.ARM.extab)    *(.gnu.linkonce.armextab.*)    *(.note.gnu.build-id) ;
}

_text_start = . ;
_text_end = . ;
_text_size = . ;

.init	BLOCK(0x00000002)	:
{

KEEP (*(.init));
}

_init_start = . ;
_init_end = . ;
_init_size = . ;

.fini	BLOCK(0x00000002)	:
{

KEEP (*(.fini));
}

.fini.start = . ;
.fini.end = . ;
.fini.size = . ;

.rodata	BLOCK(0x00000002)	:
{

*(.rodata) *(.rodata.*) *(.gnu.linkonce.r.*);
}

__rodata_start = . ;
__rodata_end = . ;
__rodata_size = . ;

.rodata1	BLOCK(0x00000002)	:
{

*(.rodata1) *(.rodata1.*);
}

__rodata1_start = . ;
__rodata1_end = . ;
__rodata1_size = . ;

.sdata2	BLOCK(0x00000002)	:
{

*(.sdata2) *(.sdata2.*) *(.gnu.linkonce.s2.*);
}

__sdata2_start = . ;
__sdata2_end = . ;
__sdata2_size = . ;

.sbss2	BLOCK(0x00000002)	:
{

*(.sbss2) *(.sbss2.*) *(.gnu.linkonce.sb2.*);
}

__sbss2_start = . ;
__sbss2_end = . ;
__sbss2_size = . ;

.data	BLOCK(0x00001000)	:
{

*(.data) *(.data.*) *(.gnu.linkonce.d.*) *(.jcr) *(.got) *(.got.plt) *(.DeltaSVMConfig);
}

__data_start = . ;
__data_end = . ;
__data_size = . ;

.data1	BLOCK(0x00000002)	:
{

*(.data1) *(.data1.*);
}

__data1_start = . ;
__data1_end = . ;
__data1_size = . ;

.got	BLOCK(0x00000002)	:
{

*(.got);
}

.got.start = . ;
.got.end = . ;
.got.size = . ;

.ctors	BLOCK(0x00000002)	:
{

___CTORS_LIST___ = .;
 KEEP (*crtbegin.o(.ctors)) KEEP (*(EXCLUDE_FILE(*crtend.o) .ctors)) KEEP (*(SORT(.ctors.*))) KEEP (*(.ctors)) ___CTORS_END___ = .;
}

__CTOR_LIST__ = . ;
__CTOR_END__ = . ;
__CTOR_SIZE__ = . ;

.dtors	BLOCK(0x00000002)	:
{

___DTORS_LIST___ = .;
 KEEP (*crtbegin.o(.dtors)) KEEP (*(EXCLUDE_FILE(*crtend.o) .dtors)) KEEP (*(SORT(.dtors.*))) KEEP (*(.dtors)) ___DTORS_END___ = .;
}

__DTOR_LIST__ = . ;
__DTOR_END__ = . ;
__DTOR_SIZE__ = . ;

.fixup	BLOCK(0x00000002)	:
{

*(.fixup);
}

__fixup_start = . ;
__fixup_end = . ;
__fixup_size = . ;

.eh_frame	BLOCK(0x00000002)	:
{

*(.eh_frame);
}

.eh_frame.start = . ;
.eh_frame.end = . ;
.eh_frame.size = . ;

.eh_framehdr	BLOCK(0x00000002)	:
{

*(.eh_framehdr);
}

__eh_framehdr_start = . ;
__eh_framehdr_end = . ;
__eh_framehdr_size = . ;

.gcc_except_table	BLOCK(0x00000002)	:
{

*(.gcc_except_table);
}

.gcc_except_table.start = . ;
.gcc_except_table.end = . ;
.gcc_except_table.size = . ;

.mmu_tbl	BLOCK(0x00004000)	:
{

*(.mmu_tbl);
}

__mmu_tbl_start = . ;
__mmu_tbl_end = . ;
__mmu_tbl_size = . ;

.ARM_exidx	BLOCK(0x00000004)	:
{

*(.ARM.exidx*) *(.gnu.linkonce.armexidix.*.*);
}

__exidx_start = . ;
__exidx_end = . ;
__exidx_size = . ;

.preinit_array	BLOCK(0x00000002)	:
{

KEEP (*(SORT(.preinit_array.*))) KEEP (*(.preinit_array));
}

__preinit_array_start = . ;
__preinit_array_end = . ;
__preinit_array_size = . ;

.init_array	BLOCK(0x00000002)	:
{

KEEP (*(SORT(.init_array.*))) KEEP (*(.init_array));
}

__init_array_start = . ;
__init_array_end = . ;
__init_array_size = . ;

.fini_array	BLOCK(0x00000002)	:
{

KEEP (*(SORT(.fini_array.*))) KEEP (*(.fini_array));
}

__fini_array_start = . ;
__fini_array_end = . ;
__fini_array_size = . ;

.drvcfg_sec	BLOCK(0x00000008)	:
{

KEEP (*(.drvcfg_sec));
}

__drvcfgsecdata_start = . ;
__drvcfgsecdata_end = . ;
__drvcfgsecdata_size = . ;

.ARM.attributes	BLOCK(0x00000002)	:
{

*(.ARM.attributes);
}

__ARM.attributes_start = . ;
__ARM.attributes_end = . ;
__ARM.attributes_size = . ;

.sdata	BLOCK(0x00000002)	:
{

*(.sdata) *(.sdata.*) *(.gnu.linkonce.s.*);
}

__sdata_start = . ;
__sdata_end = . ;
__sdata_size = . ;

.sbss	BLOCK(0x00000010)	:
{

*(.sbss) *(.sbss.*) *(.gnu.linkonce.sb.*);
}

__sbss_start = . ;
__sbss_end = . ;
__sbss_size = . ;

.tdata	BLOCK(0x00000002)	:
{

*(.tdata) *(.tdata.*) *(.gnu.linkonce.td.*);
}

__tdata_start = . ;
__tdata_end = . ;
__tdata_size = . ;

.tbss	BLOCK(0x00000002)	:
{

*(.tbss) *(.tbss.*) *(.gnu.linkonce.tb.*);
}

__tbss_start = . ;
__tbss_end = . ;
__tbss_size = . ;

.bss	BLOCK(0x00001000)	:
{

*(.bss) *(.bss.*) *(.gnu.linkonce.b.*) *(COMMON)      ;
}

__bss_start = . ;
__bss_end = . ;
__bss_size = . ;

.heap	BLOCK(0x00000010)	:
{

_heap = .;
 HeapBase = .;
 . += 0x400000;
 HeapLimit = .;
}

_heap_start = . ;
_heap_end = . ;
_heap_size = . ;

.stack	BLOCK(0x00000010)	:
{

. += 0x4000;
  . = ALIGN(16);
  _stack = .;
  __stack = _stack;
  . = ALIGN(16);
  _irq_stack_end = .;
  . += 0x4000;
  . = ALIGN(16);
  __irq_stack = .;
  _supervisor_stack_end = .;
  . += 0x4000;
  . = ALIGN(16);
  __supervisor_stack = .;
  _abort_stack_end = .;
  . += 0x4000;
  . = ALIGN(16);
  __abort_stack = .;
  _fiq_stack_end = .;
  . += 0x4000;
  . = ALIGN(16);
  __fiq_stack = .;
  _undef_stack_end = .;
  . += 0x4000;
  . = ALIGN(16);
  __undef_stack = .;
 _SDA_BASE_ = __sdata_start + ((__sbss_end - __sdata_start) / 2 );
  _SDA2_BASE_ = __sdata2_start + ((__sbss2_end - __sdata2_start) / 2 );
}

_stack_end = . ;
_stack_start = . ;
_stack_size = . ;

}
load__text_start =( 0x00001000-1 )&~(0x00001000 -1 );
load___rodata_start =( load__text_start + _text_size + ( 0x00000002 -1 ))&~(0x00000002 -1 );
load___data_start =( load___rodata_start + __rodata_size + ( 0x00001000 -1 ))&~(0x00001000 -1 );
load___bss_start =( load___data_start + __data_size + ( 0x00001000 -1 ))&~(0x00001000 -1 );
