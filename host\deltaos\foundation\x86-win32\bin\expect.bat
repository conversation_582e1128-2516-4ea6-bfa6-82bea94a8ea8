@echo off

IF NOT DEFINED WIND_FOUNDATION_PATH echo WIND_FOUNDATION_PATH is not defined.
IF NOT DEFINED WIND_FOUNDATION_PATH goto end

IF NOT DEFINED OS echo OS is not defined.
IF NOT DEFINED OS goto end

IF "%OS%" == "Windows_NT" pushd .

tclsh85.exe %WIND_FOUNDATION_PATH%\resource\tcl\wrexpect.tcl %*
IF errorlevel 1 echo Warning : Command "tclsh85.exe %WIND_FOUNDATION_PATH%\resource\tcl\wrexpect.tcl %*" has failed.

IF "%OS%" == "Windows_NT"  popd

:end
exit %ERRORLEVEL%
