

/*
 * @file： kbspRawFlash.c
 * @brief：
 *	    <li>实现目标板FLASH驱动。该FLASH由32bits S29GL512组成。</li>
 * @implements：DB.2.2 
 */

/* @<MODULE     */
/************************头 文 件******************************/

/* @<MOD_HEAD   */
#include <vmkTypes.h>
#include <kbspRawFlash.h>
#include <string.h>

#ifndef SDT
#include <xparameters.h>	/* SDK generated parameters */
#endif
#include <xqspips.h>		/* QSPI device driver */

/* @MOD_HEAD>   */

/************************宏 定 义******************************/

#ifndef SDT
#define QSPI_DEVICE_ID		XPAR_XQSPIPS_0_DEVICE_ID
#else
#define QSPI_DEVICE_ID		XPAR_XQSPIPS_0_BASEADDR
#endif

#define DATA_OFFSET		4 /* Start of Data for Read/Write */

#define WRITE_CMD			0x02
#define QUAD_READ_CMD		0x6B

/************************类型定义******************************/
struct QspiFlash
{
	XQspiPs QspiInstance;
	uint32_t FlashSize;
	u8 ReadBuffer[8192];
	u8 WriteBuffer[8192];
};
/************************外部声明******************************/

#ifndef SDT
int QspiG128FlashInit(XQspiPs *QspiInstancePtr, u16 QspiDeviceId);
#else
int QspiG128FlashInit(XQspiPs *QspiInstancePtr, UINTPTR BaseAddress);
#endif

void FlashErase(XQspiPs *QspiPtr, u32 Address, u32 ByteCount, u8 *WriteBfrPtr);

void FlashWrite(XQspiPs *QspiPtr, u32 Address, u32 ByteCount, u8 Command,
				u8 *WriteBfrPtr);

void FlashRead(XQspiPs *QspiPtr, u32 Address, u32 ByteCount, u8 Command,
				u8 *WriteBfrPtr, u8 *ReadBfrPtr);
u32 FlashGetPageSize(void);
u32 FlashGetSize(void);

/************************前向声明******************************/

struct QspiFlash g_Flash;

/**
 * @brief:
 *    flash初始化操作。
 * @return:
 *    无
 * @tracedREQ: RB.2.2.1
 * @implements: DB.2.2.1
 */
T_VOID kbspRawFlashInit(T_VOID)
{
	int status;
	status = QspiG128FlashInit(&g_Flash.QspiInstance, QSPI_DEVICE_ID);
	if(status != XST_SUCCESS)
	{
		printk("Flash Init Error\n");
		return;
	}
	printk("Flash Size %d MBytes\n", FlashGetSize() / 0x100000);
}

/*
 * @brief:
 *    flash扇区擦除操作。
 * @param[in]: uwAddr: 起始地址
 * @param[in]: size: 擦除大小
 * @return:
 *    TRUE: 擦除成功。
 *    FALSE: 擦除失败。
 * @tracedREQ: RB.2.2.2
 * @implements: DB.2.2.2
 */
T_BOOL kbspRawFlashErase(T_UWORD addr,T_UWORD size)
{
	printk("earse addr 0x%x size 0x%x\n", addr, size);
	FlashErase(&g_Flash.QspiInstance, addr, size, g_Flash.WriteBuffer);
	printk("earse done\n");
	return TRUE;
}

static T_WORD kbspRawFlashBlockWrite(T_UWORD addr,T_CONST T_UBYTE *bpBuf,T_UWORD size)
{
	uint32_t xfer_size;
	uint32_t remian_size = size;
	uint32_t page_size = FlashGetPageSize();
	printk("write addr 0x%x size 0x%x\n", addr, size);
	while(remian_size)
	{
		xfer_size = remian_size > page_size? page_size : remian_size;
		memcpy(&g_Flash.WriteBuffer[DATA_OFFSET], &bpBuf[size - remian_size], xfer_size);
		FlashWrite(&g_Flash.QspiInstance, addr + (size - remian_size), xfer_size, WRITE_CMD, g_Flash.WriteBuffer);
		remian_size -= xfer_size;
	}
	printk("write done\n");
	return size;
}

/**
 * @brief:
 *    将指定大小的数据写入flash的指定位置。
 * @param[in]: bpBuf: 指针，指向写入数据
 * @param[in]: size: 数据长度
 * @param[out]: addr: 起始地址
 * @return:
 *    -FLASH_INVALID_SPACE: 地址空间无效。
 *    -FLASH_OPERATE_BUSY：设备忙。
 *    -FLASH_OPERATE_FAIL: 写失败。
 *    size：数据全部写入
 * @tracedREQ: RB.2.2.3
 * @implements: DB.2.2.3
 */
T_WORD kbspRawFlashWrite(T_UWORD addr,T_CONST T_UBYTE *bpBuf,T_UWORD size)
{


}


/**
 * @brief
 *	功能:
 *		从flash中读取数据。
 *
 *  实现内容：
 *		根据指定的长度和位置,从指定的flash位置读出数据。
 *
 * @param[in] uwAddr:起始地址
 * @param[in] bpBuf:指针，读取的数据放入到该地址的缓冲
 * @param[in] uwSize:读取数据长度
 *
 * @return 成功返回1；否则，返回0。
 */
T_WORD kbspRawFlashRead(T_UWORD uwAddr,T_CHAR *bpBuf,T_WORD uwSize)
{
	uint32_t xfer_size;
	uint32_t remian_size = uwSize;
	while(remian_size)
	{
		xfer_size = remian_size > 4096? 4096 : remian_size;
		FlashRead(&g_Flash.QspiInstance, uwAddr + (uwSize - remian_size), xfer_size, QUAD_READ_CMD,
						g_Flash.WriteBuffer, g_Flash.ReadBuffer);
		memcpy(&bpBuf[uwSize - remian_size], &g_Flash.ReadBuffer[0], xfer_size);
		remian_size -= xfer_size;
	}
	return uwSize;
}


/**
 * @brief:
 *    返回flash的有效起始地址。
 * @return:
 *    flash的有效地址。
 * @tracedREQ: RB.2.2.4
 * @implements: DB.2.2.4
 */ 
T_UWORD kbspRawFlashQureyStartAddr(T_VOID)
{
	return(0);
}

/**
 * @brief:
 *    返回flash的有效大小。
 * @return:
 *    flash的有效大小。
 * @tracedREQ: RB.2.2.5
 * @implements: DB.2.2.5
 */
T_UWORD kbspRawFlashQureySize(T_VOID)
{
	return FlashGetSize();
}

/**
 * @brief:
 *    返回flash的每个扇区的大小。
 * @return:
 *    flash的每个扇区的大小。
 * @tracedREQ: RB.2.2.6
 * @implements: DB.2.2.6
 */
T_UWORD kbspRawFlashQureySectorSize(T_VOID)
{
	return 4096;
}	

/**
 * @brief:
 *    返回校验flash的有效起始地址。
 * @return:
 *    flash的有效地址。
 * @tracedREQ: RB.2.2
 * @implements: DB.2.2.7
 */
T_UWORD kbspRawVerifyFlashQureyStartAddr(T_VOID)
{
	return(0);
}

/**
 * @brief:
 *    返回校验flash的有效大小。
 * @return:
 *    flash的有效大小。
 * @tracedREQ: RB.2.2
 * @implements: DB.2.2.8
 */
T_UWORD kbspRawVerifyFlashQureySize(T_VOID)
{
	return FlashGetSize();
}

/**
 * @brief:
 *    返回校验flash的每个扇区的大小。
 * @return:
 *    flash的每个扇区的大小。
 * @tracedREQ: RB.2.2
 * @implements: DB.2.2.9
 */
T_UWORD kbspRawVerifyFlashQureySectorSize(T_VOID)
{
	return 4096;
}


