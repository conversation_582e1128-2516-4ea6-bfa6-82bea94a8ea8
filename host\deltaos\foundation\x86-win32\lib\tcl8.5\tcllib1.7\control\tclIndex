# Tcl autoload index file, version 2.0
# This file is generated by the "auto_mkindex" command
# and sourced to set up indexing information for one or
# more commands.  Typically each line is a command that
# sets an element in the auto_index array, where the
# element name is the name of a command and the value is
# a script that loads the command.

set auto_index(::control::CommandAsCaller) [list source [file join $dir ascaller.tcl]]
set auto_index(::control::BodyAsCaller) [list source [file join $dir ascaller.tcl]]
set auto_index(::control::ErrorInfoAsCaller) [list source [file join $dir ascaller.tcl]]
set auto_index(::control::assert::EnabledAssert) [list source [file join $dir assert.tcl]]
set auto_index(::control::assert::enabled) [list source [file join $dir assert.tcl]]
set auto_index(::control::assert::callback) [list source [file join $dir assert.tcl]]
set auto_index(::control::AssertSwitch) [list source [file join $dir assert.tcl]]
set auto_index(::control::assert) [list source [file join $dir assert.tcl]]
set auto_index(::control::do) [list source [file join $dir do.tcl]]
set auto_index(::control::no-op) [list source [file join $dir no-op.tcl]]
