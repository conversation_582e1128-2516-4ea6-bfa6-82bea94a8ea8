#!/bin/sh
#$1: Specify the project configuration path contains file linkcmds & _linkcmds
#$2: Specify the bin path in host where tools stored in
# this script is to catenate linkcmds and _linkcmds files into catlinkcmds
# linkcmds is stored in $1/make path, and _linkcmds is stored in $1 path.

#make sure the linkcmds file exists.
if [ ! -e ${1}/make/linkcmds ]; then
	${2}/echo "error:${1}/make/linkcmds does not exist!"
	exit 1
fi

#make sure the _linkcmds file exists.
if [ ! -e ${1}/_linkcmds ]; then
	${2}/echo "${1}/_linkcmds does not exist!"
	exit 1
fi

#delete the old catlinkcmds file if it exists. 
if [ -e ${1}/catlinkcmds ]; then
	${2}/rm -f ${1}/catlinkcmds
fi

#cat linkcmds and _linkcmds into catlinkcmds
${2}/cat ${1}/make/linkcmds ${1}/_linkcmds >${1}/catlinkcmds
