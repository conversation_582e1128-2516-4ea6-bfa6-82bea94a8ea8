@ECHO OFF
rem Copyright (C) 2004-2005, 2010 Wind River Systems, Inc.
rem
rem Usage: windsh <args>
rem
rem
rem modification history
rem --------------------
rem 01c,18jan10,sdt  Rework script in order to fix CQ:WIND00016069
rem 01b,04jan05,jeg  upated echo management
rem 01a,22dec04,jeg  written

rem Check environement variables

if "%WIND_FOUNDATION_PATH%" == "" goto nofound

rem Run winsh TCL wrapper

set WTXTCL= %WIND_FOUNDATION_PATH%/x86-win32/bin/wtxtcl

rem Define a temporary filename

set TMP_FILE=%TMP%\windsh_tmp.bat

rem Execute TCL script to determine the full command line

%WTXTCL% %WIND_FOUNDATION_PATH%/resource/windsh/windsh.tcl %* > %TMP_FILE%

rem Check if specfic keywords "ERROR" or "usage" are present in temp. file

findstr "ERROR usage"  %TMP_FILE% > NUL
IF %ERRORLEVEL% EQU 0 (

    rem Report error or usage message
    TYPE %TMP_FILE%
) ELSE (

    rem Start hostShell using command line stored in temp. file
    CALL %TMP_FILE% 
)

rem Delete temporary file

DEL %TMP_FILE%
goto end

:nofound
	echo WIND_FOUNDATION_PATH must be set to start the Host Shell
	echo exit.
goto end

:end
