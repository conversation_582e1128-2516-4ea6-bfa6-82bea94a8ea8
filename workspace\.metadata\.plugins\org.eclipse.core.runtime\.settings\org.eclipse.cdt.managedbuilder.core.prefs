#Wed Jul 09 23:19:32 CST 2025
properties/MlsDemo.com.coretek.tools.ide.project.deltaos.projectType.ta.707524892/com.coretek.tools.ide.project.deltaos.burn.ta.1846533180=\#\r\n\#Wed Jul 09 23\:19\:32 CST 2025\r\ncom.coretek.tools.ide.project.deltaos.cpp.compiler.gnu344.ta.1096000639\=\\\#\\r\\n\\\#Wed Jul 09 23\\\:19\\\:32 CST 2025\\r\\nrebuildState\\\=false\\r\\n\r\ncom.coretek.tools.ide.project.deltaos.assembler.gnu344.ta.1111138139\=\\\#\\r\\n\\\#Wed Jul 09 23\\\:19\\\:32 CST 2025\\r\\nrebuildState\\\=false\\r\\n\r\ncom.coretek.tools.ide.project.deltaos.c.linker.gnu344.ta.711050029\=\\\#\\r\\n\\\#Wed Jul 09 23\\\:19\\\:32 CST 2025\\r\\nrebuildState\\\=false\\r\\n\r\ncom.coretek.tools.ide.project.deltaos.burn.ta.1846533180\=\\\#\\r\\n\\\#Wed Jul 09 22\\\:54\\\:26 CST 2025\\r\\nrcState\\\=0\\r\\nrebuildState\\\=false\\r\\n\r\ncom.coretek.tools.ide.project.deltaos.c.compiler.gnu344.ta.1338120349\=\\\#\\r\\n\\\#Wed Jul 09 23\\\:19\\\:32 CST 2025\\r\\nrebuildState\\\=false\\r\\n\r\ncom.coretek.tools.ide.project.deltaos.burn.gnu344.ta.2018730834\=\\\#\\r\\n\\\#Wed Jul 09 23\\\:19\\\:32 CST 2025\\r\\nrebuildState\\\=false\\r\\n\r\ncom.coretek.tools.ide.project.deltaos.cpp.linker.gnu344.ta.1244055983\=\\\#\\r\\n\\\#Wed Jul 09 23\\\:19\\\:32 CST 2025\\r\\nrebuildState\\\=false\\r\\n\r\ncom.coretek.tools.ide.project.deltaos.archiver.gnu344.ta.2071317175\=\\\#\\r\\n\\\#Wed Jul 09 23\\\:19\\\:32 CST 2025\\r\\nrebuildState\\\=false\\r\\n\r\n
properties/MlsDemo.com.coretek.tools.ide.project.deltaos.projectType.app.707524892/com.coretek.tools.ide.project.deltaos.burn.app.1846533179.488282521.2074075900=\#\r\n\#Wed Jul 09 21\:46\:22 CST 2025\r\ncom.coretek.tools.ide.project.deltaos.burn.gnu344.app.1875650298\=\\\#\\r\\n\\\#Wed Jul 09 21\\\:46\\\:22 CST 2025\\r\\nrebuildState\\\=false\\r\\n\r\ncom.coretek.tools.ide.project.deltaos.cpp.linker.gnu344.app.2054675762\=\\\#\\r\\n\\\#Wed Jul 09 21\\\:46\\\:22 CST 2025\\r\\nrebuildState\\\=false\\r\\n\r\ncom.coretek.tools.ide.project.deltaos.c.compiler.gnu344.app.2121619684\=\\\#\\r\\n\\\#Wed Jul 09 21\\\:46\\\:22 CST 2025\\r\\nrebuildState\\\=false\\r\\n\r\ncom.coretek.tools.ide.project.deltaos.cpp.compiler.gnu344.app.966071514\=\\\#\\r\\n\\\#Wed Jul 09 21\\\:46\\\:22 CST 2025\\r\\nrebuildState\\\=false\\r\\n\r\ncom.coretek.tools.ide.project.deltaos.burn.gnu344.app.1826221799\=\\\#\\r\\n\\\#Wed Jul 09 21\\\:46\\\:22 CST 2025\\r\\nrebuildState\\\=false\\r\\n\r\ncom.coretek.tools.ide.project.deltaos.archiver.gnu344.app.744353234\=\\\#\\r\\n\\\#Wed Jul 09 21\\\:46\\\:22 CST 2025\\r\\nrebuildState\\\=false\\r\\n\r\ncom.coretek.tools.ide.project.deltaos.assembler.gnu344.app.1435654568\=\\\#\\r\\n\\\#Wed Jul 09 21\\\:46\\\:22 CST 2025\\r\\nrebuildState\\\=false\\r\\n\r\ncom.coretek.tools.ide.project.deltaos.cpp.linker.gnu344.app.2081130932\=\\\#\\r\\n\\\#Wed Jul 09 21\\\:46\\\:22 CST 2025\\r\\nrebuildState\\\=false\\r\\n\r\ncom.coretek.tools.ide.project.deltaos.assembler.gnu344.app.571427873\=\\\#\\r\\n\\\#Wed Jul 09 21\\\:46\\\:22 CST 2025\\r\\nrebuildState\\\=false\\r\\n\r\ncom.coretek.tools.ide.project.deltaos.c.linker.gnu344.app.1921179035\=\\\#\\r\\n\\\#Wed Jul 09 21\\\:46\\\:22 CST 2025\\r\\nrebuildState\\\=false\\r\\n\r\ncom.coretek.tools.ide.project.deltaos.c.compiler.gnu344.app.1475982278\=\\\#\\r\\n\\\#Wed Jul 09 21\\\:46\\\:22 CST 2025\\r\\nrebuildState\\\=false\\r\\n\r\ncom.coretek.tools.ide.project.deltaos.c.linker.gnu344.app.385266747\=\\\#\\r\\n\\\#Wed Jul 09 21\\\:46\\\:22 CST 2025\\r\\nrebuildState\\\=false\\r\\n\r\ncom.coretek.tools.ide.project.deltaos.cpp.compiler.gnu344.app.494189212\=\\\#\\r\\n\\\#Wed Jul 09 21\\\:46\\\:22 CST 2025\\r\\nrebuildState\\\=false\\r\\n\r\ncom.coretek.tools.ide.project.deltaos.burn.app.1846533179.488282521.2074075900\=\\\#\\r\\n\\\#Wed Jul 09 21\\\:46\\\:22 CST 2025\\r\\nrcState\\\=0\\r\\nrebuildState\\\=false\\r\\n\r\ncom.coretek.tools.ide.project.deltaos.archiver.gnu344.app.1012517929\=\\\#\\r\\n\\\#Wed Jul 09 21\\\:46\\\:22 CST 2025\\r\\nrebuildState\\\=false\\r\\n\r\n
properties/MlsDemo.com.coretek.tools.ide.project.deltaos.projectType.kbsp.707524892/com.coretek.tools.ide.project.deltaos.burn.kbsp.1846533181=\#\r\n\#Sat Jun 14 06\:27\:17 CST 2025\r\ncom.coretek.tools.ide.project.deltaos.burn.gnu344.kbsp.2018730834\=\\\#\\r\\n\\\#Sat Jun 14 06\\\:27\\\:17 CST 2025\\r\\nrebuildState\\\=true\\r\\n\r\ncom.coretek.tools.ide.project.deltaos.cpp.compiler.gnu344.kbsp.1096000639\=\\\#\\r\\n\\\#Sat Jun 14 06\\\:27\\\:17 CST 2025\\r\\nrebuildState\\\=true\\r\\n\r\ncom.coretek.tools.ide.project.deltaos.archiver.gnu344.kbsp.2071317175\=\\\#\\r\\n\\\#Sat Jun 14 06\\\:27\\\:17 CST 2025\\r\\nrebuildState\\\=true\\r\\n\r\ncom.coretek.tools.ide.project.deltaos.cpp.linker.gnu344.kbsp.1244055983\=\\\#\\r\\n\\\#Sat Jun 14 06\\\:27\\\:17 CST 2025\\r\\nrebuildState\\\=true\\r\\n\r\ncom.coretek.tools.ide.project.deltaos.assembler.gnu344.kbsp.1111138139\=\\\#\\r\\n\\\#Sat Jun 14 06\\\:27\\\:17 CST 2025\\r\\nrebuildState\\\=true\\r\\n\r\ncom.coretek.tools.ide.project.deltaos.c.linker.gnu344.kbsp.711050029\=\\\#\\r\\n\\\#Sat Jun 14 06\\\:27\\\:17 CST 2025\\r\\nrebuildState\\\=true\\r\\n\r\ncom.coretek.tools.ide.project.deltaos.c.compiler.gnu344.kbsp.1338120349\=\\\#\\r\\n\\\#Sat Jun 14 06\\\:27\\\:17 CST 2025\\r\\nrebuildState\\\=true\\r\\n\r\n
properties/MlsDemo.com.coretek.tools.ide.project.deltaos.projectType.kbsp.707524892/com.coretek.tools.ide.project.deltaos.burn.kbsp.1846533180=\#\r\n\#Wed Jul 09 23\:19\:32 CST 2025\r\ncom.coretek.tools.ide.project.deltaos.burn.gnu344.kbsp.2018730834\=\\\#\\r\\n\\\#Wed Jul 09 23\\\:19\\\:32 CST 2025\\r\\nrebuildState\\\=false\\r\\n\r\ncom.coretek.tools.ide.project.deltaos.cpp.compiler.gnu344.kbsp.1096000639\=\\\#\\r\\n\\\#Wed Jul 09 23\\\:19\\\:32 CST 2025\\r\\nrebuildState\\\=false\\r\\n\r\ncom.coretek.tools.ide.project.deltaos.archiver.gnu344.kbsp.2071317175\=\\\#\\r\\n\\\#Wed Jul 09 23\\\:19\\\:32 CST 2025\\r\\nrebuildState\\\=false\\r\\n\r\ncom.coretek.tools.ide.project.deltaos.cpp.linker.gnu344.kbsp.1244055983\=\\\#\\r\\n\\\#Wed Jul 09 23\\\:19\\\:32 CST 2025\\r\\nrebuildState\\\=false\\r\\n\r\ncom.coretek.tools.ide.project.deltaos.assembler.gnu344.kbsp.1111138139\=\\\#\\r\\n\\\#Wed Jul 09 23\\\:19\\\:32 CST 2025\\r\\nrebuildState\\\=false\\r\\n\r\ncom.coretek.tools.ide.project.deltaos.c.linker.gnu344.kbsp.711050029\=\\\#\\r\\n\\\#Wed Jul 09 23\\\:19\\\:32 CST 2025\\r\\nrebuildState\\\=false\\r\\n\r\ncom.coretek.tools.ide.project.deltaos.burn.kbsp.1846533180\=\\\#\\r\\n\\\#Wed Jul 09 23\\\:14\\\:00 CST 2025\\r\\nrcState\\\=0\\r\\nrebuildState\\\=false\\r\\n\r\ncom.coretek.tools.ide.project.deltaos.c.compiler.gnu344.kbsp.1338120349\=\\\#\\r\\n\\\#Wed Jul 09 23\\\:19\\\:32 CST 2025\\r\\nrebuildState\\\=false\\r\\n\r\n
properties/MlsDemo.com.coretek.tools.ide.project.deltaos.projectType.staticlib.707524892/com.coretek.tools.ide.project.deltaos.debug.staticlib.1691529082=\#\r\n\#Wed Jul 09 21\:46\:22 CST 2025\r\ncom.coretek.tools.ide.project.deltaos.c.linker.gnu344.staticlib.1572067664\=\\\#\\r\\n\\\#Wed Jul 09 21\\\:46\\\:22 CST 2025\\r\\nrebuildState\\\=false\\r\\n\r\ncom.coretek.tools.ide.project.deltaos.cpp.compiler.gnu344.staticlib.1758740547\=\\\#\\r\\n\\\#Wed Jul 09 21\\\:46\\\:22 CST 2025\\r\\nrebuildState\\\=false\\r\\n\r\ncom.coretek.tools.ide.project.deltaos.debug.toolChain.staticlib.425749994\=\\\#\\r\\n\\\#Wed Jul 09 21\\\:46\\\:22 CST 2025\\r\\nrebuildState\\\=false\\r\\n\r\ncom.coretek.tools.ide.project.deltaos.cpp.compiler.gnu344.staticlib.408312969\=\\\#\\r\\n\\\#Wed Jul 09 21\\\:46\\\:22 CST 2025\\r\\nrebuildState\\\=false\\r\\n\r\ncom.coretek.tools.ide.project.deltaos.archiver.gnu344.staticlib.331450132\=\\\#\\r\\n\\\#Wed Jul 09 21\\\:46\\\:22 CST 2025\\r\\nrebuildState\\\=false\\r\\n\r\ncom.coretek.tools.ide.project.deltaos.c.compiler.gnu344.staticlib.1636671451\=\\\#\\r\\n\\\#Wed Jul 09 21\\\:46\\\:22 CST 2025\\r\\nrebuildState\\\=false\\r\\n\r\ncom.coretek.tools.ide.project.deltaos.assembler.gnu344.staticlib.1552863337\=\\\#\\r\\n\\\#Wed Jul 09 21\\\:46\\\:22 CST 2025\\r\\nrebuildState\\\=false\\r\\n\r\ncom.coretek.tools.ide.project.deltaos.assembler.gnu344.staticlib.1314985245\=\\\#\\r\\n\\\#Wed Jul 09 21\\\:46\\\:22 CST 2025\\r\\nrebuildState\\\=false\\r\\n\r\ncom.coretek.tools.ide.project.deltaos.cpp.linker.gnu344.staticlib.1786357094\=\\\#\\r\\n\\\#Wed Jul 09 21\\\:46\\\:22 CST 2025\\r\\nrebuildState\\\=false\\r\\n\r\ncom.coretek.tools.ide.project.deltaos.c.compiler.gnu344.staticlib.12911390\=\\\#\\r\\n\\\#Wed Jul 09 21\\\:46\\\:22 CST 2025\\r\\nrebuildState\\\=false\\r\\n\r\ncom.coretek.tools.ide.project.deltaos.debug.gnu344.staticlib.1457515091\=\\\#\\r\\n\\\#Wed Jul 09 21\\\:46\\\:22 CST 2025\\r\\nrebuildState\\\=false\\r\\n\r\ncom.coretek.tools.ide.project.deltaos.c.linker.gnu344.staticlib.1128784652\=\\\#\\r\\n\\\#Wed Jul 09 21\\\:46\\\:22 CST 2025\\r\\nrebuildState\\\=false\\r\\n\r\ncom.coretek.tools.ide.project.deltaos.cpp.linker.gnu344.staticlib.650354222\=\\\#\\r\\n\\\#Wed Jul 09 21\\\:46\\\:22 CST 2025\\r\\nrebuildState\\\=false\\r\\n\r\ncom.coretek.tools.ide.project.deltaos.archiver.gnu344.staticlib.893342773\=\\\#\\r\\n\\\#Wed Jul 09 21\\\:46\\\:22 CST 2025\\r\\nrebuildState\\\=false\\r\\n\r\ncom.coretek.tools.ide.project.deltaos.debug.staticlib.1691529082\=\\\#\\r\\n\\\#Wed Jul 09 21\\\:46\\\:22 CST 2025\\r\\nrcState\\\=0\\r\\nrebuildState\\\=false\\r\\n\r\n
properties/MlsDemo.com.coretek.tools.ide.project.deltaos.projectType.app.707524892/com.coretek.tools.ide.project.deltaos.burn.app.1846533179.488282521=\#\r\n\#Wed Jul 02 22\:40\:53 CST 2025\r\ncom.coretek.tools.ide.project.deltaos.burn.app.1846533179.488282521\=\\\#\\r\\n\\\#Wed Jul 02 22\\\:40\\\:53 CST 2025\\r\\nrcState\\\=0\\r\\nrebuildState\\\=false\\r\\n\r\ncom.coretek.tools.ide.project.deltaos.burn.gnu344.app.1948451340\=\\\#\\r\\n\\\#Wed Jul 02 22\\\:40\\\:53 CST 2025\\r\\nrebuildState\\\=false\\r\\n\r\ncom.coretek.tools.ide.project.deltaos.assembler.gnu344.app.694666973\=\\\#\\r\\n\\\#Wed Jul 02 22\\\:40\\\:53 CST 2025\\r\\nrebuildState\\\=false\\r\\n\r\ncom.coretek.tools.ide.project.deltaos.cpp.linker.gnu344.app.215213301\=\\\#\\r\\n\\\#Wed Jul 02 22\\\:40\\\:53 CST 2025\\r\\nrebuildState\\\=false\\r\\n\r\ncom.coretek.tools.ide.project.deltaos.c.compiler.gnu344.app.1513787354\=\\\#\\r\\n\\\#Wed Jul 02 22\\\:40\\\:53 CST 2025\\r\\nrebuildState\\\=false\\r\\n\r\ncom.coretek.tools.ide.project.deltaos.cpp.linker.gnu344.app.2123918827\=\\\#\\r\\n\\\#Wed Jul 02 22\\\:40\\\:53 CST 2025\\r\\nrebuildState\\\=false\\r\\n\r\ncom.coretek.tools.ide.project.deltaos.burn.gnu344.app.1586360581\=\\\#\\r\\n\\\#Wed Jul 02 22\\\:40\\\:53 CST 2025\\r\\nrebuildState\\\=false\\r\\n\r\ncom.coretek.tools.ide.project.deltaos.archiver.gnu344.app.1352883779\=\\\#\\r\\n\\\#Wed Jul 02 22\\\:40\\\:53 CST 2025\\r\\nrebuildState\\\=false\\r\\n\r\ncom.coretek.tools.ide.project.deltaos.c.compiler.gnu344.app.1068618867\=\\\#\\r\\n\\\#Wed Jul 02 22\\\:40\\\:53 CST 2025\\r\\nrebuildState\\\=false\\r\\n\r\ncom.coretek.tools.ide.project.deltaos.c.linker.gnu344.app.1198621730\=\\\#\\r\\n\\\#Wed Jul 02 22\\\:40\\\:53 CST 2025\\r\\nrebuildState\\\=false\\r\\n\r\ncom.coretek.tools.ide.project.deltaos.assembler.gnu344.app.1736852134\=\\\#\\r\\n\\\#Wed Jul 02 22\\\:40\\\:53 CST 2025\\r\\nrebuildState\\\=false\\r\\n\r\ncom.coretek.tools.ide.project.deltaos.cpp.compiler.gnu344.app.1065140870\=\\\#\\r\\n\\\#Wed Jul 02 22\\\:40\\\:53 CST 2025\\r\\nrebuildState\\\=false\\r\\n\r\ncom.coretek.tools.ide.project.deltaos.c.linker.gnu344.app.445843962\=\\\#\\r\\n\\\#Wed Jul 02 22\\\:40\\\:53 CST 2025\\r\\nrebuildState\\\=false\\r\\n\r\ncom.coretek.tools.ide.project.deltaos.archiver.gnu344.app.1259795146\=\\\#\\r\\n\\\#Wed Jul 02 22\\\:40\\\:53 CST 2025\\r\\nrebuildState\\\=false\\r\\n\r\ncom.coretek.tools.ide.project.deltaos.cpp.compiler.gnu344.app.273366230\=\\\#\\r\\n\\\#Wed Jul 02 22\\\:40\\\:53 CST 2025\\r\\nrebuildState\\\=false\\r\\n\r\n
properties/MlsDemo.com.coretek.tools.ide.project.deltaos.projectType.staticlib.707524892/com.coretek.tools.ide.project.deltaos.debug.staticlib.506810886=\#\r\n\#Wed Jul 09 21\:46\:22 CST 2025\r\ncom.coretek.tools.ide.project.deltaos.debug.gnu344.staticlib.1823734068\=\\\#\\r\\n\\\#Wed Jul 09 21\\\:46\\\:22 CST 2025\\r\\nrebuildState\\\=true\\r\\n\r\ncom.coretek.tools.ide.project.deltaos.archiver.gnu344.staticlib.955000423\=\\\#\\r\\n\\\#Wed Jul 09 21\\\:46\\\:22 CST 2025\\r\\nrebuildState\\\=true\\r\\n\r\ncom.coretek.tools.ide.project.deltaos.cpp.compiler.gnu344.staticlib.1679255070\=\\\#\\r\\n\\\#Wed Jul 09 21\\\:46\\\:22 CST 2025\\r\\nrebuildState\\\=true\\r\\n\r\ncom.coretek.tools.ide.project.deltaos.c.linker.gnu344.staticlib.353431063\=\\\#\\r\\n\\\#Wed Jul 09 21\\\:46\\\:22 CST 2025\\r\\nrebuildState\\\=true\\r\\n\r\ncom.coretek.tools.ide.project.deltaos.assembler.gnu344.staticlib.685606305\=\\\#\\r\\n\\\#Wed Jul 09 21\\\:46\\\:22 CST 2025\\r\\nrebuildState\\\=true\\r\\n\r\ncom.coretek.tools.ide.project.deltaos.cpp.linker.gnu344.staticlib.891502582\=\\\#\\r\\n\\\#Wed Jul 09 21\\\:46\\\:22 CST 2025\\r\\nrebuildState\\\=true\\r\\n\r\ncom.coretek.tools.ide.project.deltaos.archiver.gnu344.staticlib.1907180183\=\\\#\\r\\n\\\#Wed Jul 09 21\\\:46\\\:22 CST 2025\\r\\nrebuildState\\\=true\\r\\n\r\ncom.coretek.tools.ide.project.deltaos.debug.toolChain.staticlib.1367219934\=\\\#\\r\\n\\\#Wed Jul 09 21\\\:46\\\:22 CST 2025\\r\\nrebuildState\\\=true\\r\\n\r\ncom.coretek.tools.ide.project.deltaos.cpp.linker.gnu344.staticlib.588057323\=\\\#\\r\\n\\\#Wed Jul 09 21\\\:46\\\:22 CST 2025\\r\\nrebuildState\\\=true\\r\\n\r\ncom.coretek.tools.ide.project.deltaos.assembler.gnu344.staticlib.1015694037\=\\\#\\r\\n\\\#Wed Jul 09 21\\\:46\\\:22 CST 2025\\r\\nrebuildState\\\=true\\r\\n\r\ncom.coretek.tools.ide.project.deltaos.cpp.compiler.gnu344.staticlib.1063396208\=\\\#\\r\\n\\\#Wed Jul 09 21\\\:46\\\:22 CST 2025\\r\\nrebuildState\\\=true\\r\\n\r\ncom.coretek.tools.ide.project.deltaos.c.linker.gnu344.staticlib.169513482\=\\\#\\r\\n\\\#Wed Jul 09 21\\\:46\\\:22 CST 2025\\r\\nrebuildState\\\=true\\r\\n\r\ncom.coretek.tools.ide.project.deltaos.c.compiler.gnu344.staticlib.1684760242\=\\\#\\r\\n\\\#Wed Jul 09 21\\\:46\\\:22 CST 2025\\r\\nrebuildState\\\=true\\r\\n\r\ncom.coretek.tools.ide.project.deltaos.c.compiler.gnu344.staticlib.1799544937\=\\\#\\r\\n\\\#Wed Jul 09 21\\\:46\\\:22 CST 2025\\r\\nrebuildState\\\=true\\r\\n\r\n
properties/MlsDemo.com.coretek.tools.ide.project.deltaos.projectType.app.707524892/com.coretek.tools.ide.project.deltaos.burn.app.1846533179.488282521.1398561960=\#\r\n\#Wed Jul 02 21\:07\:21 CST 2025\r\ncom.coretek.tools.ide.project.deltaos.cpp.compiler.gnu344.app.570205515\=\\\#\\r\\n\\\#Wed Jul 02 21\\\:07\\\:21 CST 2025\\r\\nrebuildState\\\=false\\r\\n\r\ncom.coretek.tools.ide.project.deltaos.burn.app.1846533179.488282521.1398561960\=\\\#\\r\\n\\\#Wed Jul 02 21\\\:07\\\:21 CST 2025\\r\\nrcState\\\=0\\r\\nrebuildState\\\=false\\r\\n\r\ncom.coretek.tools.ide.project.deltaos.burn.gnu344.app.1547135574\=\\\#\\r\\n\\\#Wed Jul 02 21\\\:07\\\:21 CST 2025\\r\\nrebuildState\\\=false\\r\\n\r\ncom.coretek.tools.ide.project.deltaos.c.compiler.gnu344.app.893779148\=\\\#\\r\\n\\\#Wed Jul 02 21\\\:07\\\:21 CST 2025\\r\\nrebuildState\\\=false\\r\\n\r\ncom.coretek.tools.ide.project.deltaos.c.linker.gnu344.app.1839319779\=\\\#\\r\\n\\\#Wed Jul 02 21\\\:07\\\:21 CST 2025\\r\\nrebuildState\\\=false\\r\\n\r\ncom.coretek.tools.ide.project.deltaos.archiver.gnu344.app.1908082032\=\\\#\\r\\n\\\#Wed Jul 02 21\\\:07\\\:21 CST 2025\\r\\nrebuildState\\\=false\\r\\n\r\ncom.coretek.tools.ide.project.deltaos.c.linker.gnu344.app.1019704294\=\\\#\\r\\n\\\#Wed Jul 02 21\\\:07\\\:21 CST 2025\\r\\nrebuildState\\\=false\\r\\n\r\ncom.coretek.tools.ide.project.deltaos.burn.gnu344.app.946768731\=\\\#\\r\\n\\\#Wed Jul 02 21\\\:07\\\:21 CST 2025\\r\\nrebuildState\\\=false\\r\\n\r\ncom.coretek.tools.ide.project.deltaos.archiver.gnu344.app.208690260\=\\\#\\r\\n\\\#Wed Jul 02 21\\\:07\\\:21 CST 2025\\r\\nrebuildState\\\=false\\r\\n\r\ncom.coretek.tools.ide.project.deltaos.assembler.gnu344.app.315417594\=\\\#\\r\\n\\\#Wed Jul 02 21\\\:07\\\:21 CST 2025\\r\\nrebuildState\\\=false\\r\\n\r\ncom.coretek.tools.ide.project.deltaos.cpp.compiler.gnu344.app.394204057\=\\\#\\r\\n\\\#Wed Jul 02 21\\\:07\\\:21 CST 2025\\r\\nrebuildState\\\=false\\r\\n\r\ncom.coretek.tools.ide.project.deltaos.cpp.linker.gnu344.app.657692794\=\\\#\\r\\n\\\#Wed Jul 02 21\\\:07\\\:21 CST 2025\\r\\nrebuildState\\\=false\\r\\n\r\ncom.coretek.tools.ide.project.deltaos.assembler.gnu344.app.386721732\=\\\#\\r\\n\\\#Wed Jul 02 21\\\:07\\\:21 CST 2025\\r\\nrebuildState\\\=false\\r\\n\r\ncom.coretek.tools.ide.project.deltaos.c.compiler.gnu344.app.1754672463\=\\\#\\r\\n\\\#Wed Jul 02 21\\\:07\\\:21 CST 2025\\r\\nrebuildState\\\=false\\r\\n\r\ncom.coretek.tools.ide.project.deltaos.cpp.linker.gnu344.app.1396741116\=\\\#\\r\\n\\\#Wed Jul 02 21\\\:07\\\:21 CST 2025\\r\\nrebuildState\\\=false\\r\\n\r\n
eclipse.preferences.version=1
properties/MlsDemo.com.coretek.tools.ide.project.deltaos.projectType.app.707524892/com.coretek.tools.ide.project.deltaos.burn.app.1846533179.488282521.1426055571=\#\r\n\#Wed Jul 02 20\:53\:19 CST 2025\r\ncom.coretek.tools.ide.project.deltaos.cpp.compiler.gnu344.app.1601259635\=\\\#\\r\\n\\\#Wed Jul 02 20\\\:53\\\:19 CST 2025\\r\\nrebuildState\\\=false\\r\\n\r\ncom.coretek.tools.ide.project.deltaos.archiver.gnu344.app.34367147\=\\\#\\r\\n\\\#Wed Jul 02 20\\\:53\\\:19 CST 2025\\r\\nrebuildState\\\=false\\r\\n\r\ncom.coretek.tools.ide.project.deltaos.burn.app.1846533179.488282521.1426055571\=\\\#\\r\\n\\\#Wed Jul 02 20\\\:53\\\:19 CST 2025\\r\\nrcState\\\=0\\r\\nrebuildState\\\=false\\r\\n\r\ncom.coretek.tools.ide.project.deltaos.cpp.linker.gnu344.app.1527959774\=\\\#\\r\\n\\\#Wed Jul 02 20\\\:53\\\:19 CST 2025\\r\\nrebuildState\\\=false\\r\\n\r\ncom.coretek.tools.ide.project.deltaos.burn.gnu344.app.1582268717\=\\\#\\r\\n\\\#Wed Jul 02 20\\\:53\\\:19 CST 2025\\r\\nrebuildState\\\=false\\r\\n\r\ncom.coretek.tools.ide.project.deltaos.burn.gnu344.app.1406727252\=\\\#\\r\\n\\\#Wed Jul 02 20\\\:53\\\:19 CST 2025\\r\\nrebuildState\\\=false\\r\\n\r\ncom.coretek.tools.ide.project.deltaos.c.compiler.gnu344.app.1023738949\=\\\#\\r\\n\\\#Wed Jul 02 20\\\:53\\\:19 CST 2025\\r\\nrebuildState\\\=false\\r\\n\r\ncom.coretek.tools.ide.project.deltaos.c.compiler.gnu344.app.1184987778\=\\\#\\r\\n\\\#Wed Jul 02 20\\\:53\\\:19 CST 2025\\r\\nrebuildState\\\=false\\r\\n\r\ncom.coretek.tools.ide.project.deltaos.c.linker.gnu344.app.1799771009\=\\\#\\r\\n\\\#Wed Jul 02 20\\\:53\\\:19 CST 2025\\r\\nrebuildState\\\=false\\r\\n\r\ncom.coretek.tools.ide.project.deltaos.archiver.gnu344.app.1057373169\=\\\#\\r\\n\\\#Wed Jul 02 20\\\:53\\\:19 CST 2025\\r\\nrebuildState\\\=false\\r\\n\r\ncom.coretek.tools.ide.project.deltaos.cpp.linker.gnu344.app.1931712944\=\\\#\\r\\n\\\#Wed Jul 02 20\\\:53\\\:19 CST 2025\\r\\nrebuildState\\\=false\\r\\n\r\ncom.coretek.tools.ide.project.deltaos.cpp.compiler.gnu344.app.1958537742\=\\\#\\r\\n\\\#Wed Jul 02 20\\\:53\\\:19 CST 2025\\r\\nrebuildState\\\=false\\r\\n\r\ncom.coretek.tools.ide.project.deltaos.assembler.gnu344.app.434330082\=\\\#\\r\\n\\\#Wed Jul 02 20\\\:53\\\:19 CST 2025\\r\\nrebuildState\\\=false\\r\\n\r\ncom.coretek.tools.ide.project.deltaos.assembler.gnu344.app.579144414\=\\\#\\r\\n\\\#Wed Jul 02 20\\\:53\\\:19 CST 2025\\r\\nrebuildState\\\=false\\r\\n\r\ncom.coretek.tools.ide.project.deltaos.c.linker.gnu344.app.676593015\=\\\#\\r\\n\\\#Wed Jul 02 20\\\:53\\\:19 CST 2025\\r\\nrebuildState\\\=false\\r\\n\r\n
properties/MlsDemo.com.coretek.tools.ide.project.deltaos.projectType.app.707524892/com.coretek.tools.ide.project.deltaos.burn.app.1846533179.488282521.384558753=\#\r\n\#Wed Jul 02 20\:42\:57 CST 2025\r\ncom.coretek.tools.ide.project.deltaos.burn.gnu344.app.843766616\=\\\#\\r\\n\\\#Wed Jul 02 20\\\:42\\\:57 CST 2025\\r\\nrebuildState\\\=false\\r\\n\r\ncom.coretek.tools.ide.project.deltaos.cpp.linker.gnu344.app.62473128\=\\\#\\r\\n\\\#Wed Jul 02 20\\\:42\\\:57 CST 2025\\r\\nrebuildState\\\=false\\r\\n\r\ncom.coretek.tools.ide.project.deltaos.c.compiler.gnu344.app.936530254\=\\\#\\r\\n\\\#Wed Jul 02 20\\\:42\\\:57 CST 2025\\r\\nrebuildState\\\=false\\r\\n\r\ncom.coretek.tools.ide.project.deltaos.cpp.compiler.gnu344.app.1081163227\=\\\#\\r\\n\\\#Wed Jul 02 20\\\:42\\\:57 CST 2025\\r\\nrebuildState\\\=false\\r\\n\r\ncom.coretek.tools.ide.project.deltaos.c.linker.gnu344.app.215591218\=\\\#\\r\\n\\\#Wed Jul 02 20\\\:42\\\:57 CST 2025\\r\\nrebuildState\\\=false\\r\\n\r\ncom.coretek.tools.ide.project.deltaos.archiver.gnu344.app.369716132\=\\\#\\r\\n\\\#Wed Jul 02 20\\\:42\\\:57 CST 2025\\r\\nrebuildState\\\=false\\r\\n\r\ncom.coretek.tools.ide.project.deltaos.assembler.gnu344.app.1327243986\=\\\#\\r\\n\\\#Wed Jul 02 20\\\:42\\\:57 CST 2025\\r\\nrebuildState\\\=false\\r\\n\r\ncom.coretek.tools.ide.project.deltaos.assembler.gnu344.app.1923713720\=\\\#\\r\\n\\\#Wed Jul 02 20\\\:42\\\:57 CST 2025\\r\\nrebuildState\\\=false\\r\\n\r\ncom.coretek.tools.ide.project.deltaos.cpp.compiler.gnu344.app.890244918\=\\\#\\r\\n\\\#Wed Jul 02 20\\\:42\\\:57 CST 2025\\r\\nrebuildState\\\=false\\r\\n\r\ncom.coretek.tools.ide.project.deltaos.archiver.gnu344.app.1677671228\=\\\#\\r\\n\\\#Wed Jul 02 20\\\:42\\\:57 CST 2025\\r\\nrebuildState\\\=false\\r\\n\r\ncom.coretek.tools.ide.project.deltaos.burn.gnu344.app.704730063\=\\\#\\r\\n\\\#Wed Jul 02 20\\\:42\\\:57 CST 2025\\r\\nrebuildState\\\=false\\r\\n\r\ncom.coretek.tools.ide.project.deltaos.burn.app.1846533179.488282521.384558753\=\\\#\\r\\n\\\#Wed Jul 02 20\\\:39\\\:04 CST 2025\\r\\nrcState\\\=0\\r\\nrebuildState\\\=false\\r\\n\r\ncom.coretek.tools.ide.project.deltaos.c.linker.gnu344.app.940542472\=\\\#\\r\\n\\\#Wed Jul 02 20\\\:42\\\:57 CST 2025\\r\\nrebuildState\\\=false\\r\\n\r\ncom.coretek.tools.ide.project.deltaos.c.compiler.gnu344.app.1299813323\=\\\#\\r\\n\\\#Wed Jul 02 20\\\:42\\\:57 CST 2025\\r\\nrebuildState\\\=false\\r\\n\r\ncom.coretek.tools.ide.project.deltaos.cpp.linker.gnu344.app.56730285\=\\\#\\r\\n\\\#Wed Jul 02 20\\\:42\\\:57 CST 2025\\r\\nrebuildState\\\=false\\r\\n\r\n
properties/MlsDemo.com.coretek.tools.ide.project.deltaos.projectType.staticlib.707524892/com.coretek.tools.ide.project.deltaos.burn.staticlib.1846533280=\#\r\n\#Wed Jul 09 21\:46\:22 CST 2025\r\ncom.coretek.tools.ide.project.deltaos.cpp.linker.gnu344.staticlib.1244055983\=\\\#\\r\\n\\\#Wed Jul 09 21\\\:46\\\:22 CST 2025\\r\\nrebuildState\\\=true\\r\\n\r\ncom.coretek.tools.ide.project.deltaos.burn.gnu344.staticlib.2018730834\=\\\#\\r\\n\\\#Wed Jul 09 21\\\:46\\\:22 CST 2025\\r\\nrebuildState\\\=true\\r\\n\r\ncom.coretek.tools.ide.project.deltaos.assembler.gnu344.staticlib.1111138139\=\\\#\\r\\n\\\#Wed Jul 09 21\\\:46\\\:22 CST 2025\\r\\nrebuildState\\\=true\\r\\n\r\ncom.coretek.tools.ide.project.deltaos.archiver.gnu344.staticlib.2071317175\=\\\#\\r\\n\\\#Wed Jul 09 21\\\:46\\\:22 CST 2025\\r\\nrebuildState\\\=true\\r\\n\r\ncom.coretek.tools.ide.project.deltaos.c.linker.gnu344.staticlib.711050029\=\\\#\\r\\n\\\#Wed Jul 09 21\\\:46\\\:22 CST 2025\\r\\nrebuildState\\\=true\\r\\n\r\ncom.coretek.tools.ide.project.deltaos.c.compiler.gnu344.staticlib.1338120349\=\\\#\\r\\n\\\#Wed Jul 09 21\\\:46\\\:22 CST 2025\\r\\nrebuildState\\\=true\\r\\n\r\ncom.coretek.tools.ide.project.deltaos.cpp.compiler.gnu344.staticlib.1096000639\=\\\#\\r\\n\\\#Wed Jul 09 21\\\:46\\\:22 CST 2025\\r\\nrebuildState\\\=true\\r\\n\r\n
properties/MlsDemo.com.coretek.tools.ide.project.deltaos.projectType.staticlib.707524892/com.coretek.tools.ide.project.deltaos.debug.staticlib.2067246375=\#\r\n\#Wed Jul 09 21\:54\:39 CST 2025\r\ncom.coretek.tools.ide.project.deltaos.cpp.compiler.gnu344.staticlib.545246776\=\\\#\\r\\n\\\#Wed Jul 09 21\\\:46\\\:22 CST 2025\\r\\nrebuildState\\\=false\\r\\n\r\ncom.coretek.tools.ide.project.deltaos.c.linker.gnu344.staticlib.1209521561\=\\\#\\r\\n\\\#Wed Jul 09 21\\\:46\\\:22 CST 2025\\r\\nrebuildState\\\=false\\r\\n\r\ncom.coretek.tools.ide.project.deltaos.archiver.gnu344.staticlib.1429386156\=\\\#\\r\\n\\\#Wed Jul 09 21\\\:46\\\:22 CST 2025\\r\\nrebuildState\\\=false\\r\\n\r\ncom.coretek.tools.ide.project.deltaos.debug.toolChain.staticlib.789188076\=\\\#\\r\\n\\\#Wed Jul 09 21\\\:46\\\:22 CST 2025\\r\\nrebuildState\\\=false\\r\\n\r\ncom.coretek.tools.ide.project.deltaos.c.compiler.gnu344.staticlib.1614769572\=\\\#\\r\\n\\\#Wed Jul 09 21\\\:46\\\:22 CST 2025\\r\\nrebuildState\\\=false\\r\\n\r\ncom.coretek.tools.ide.project.deltaos.assembler.gnu344.staticlib.1224061486\=\\\#\\r\\n\\\#Wed Jul 09 21\\\:46\\\:22 CST 2025\\r\\nrebuildState\\\=false\\r\\n\r\ncom.coretek.tools.ide.project.deltaos.cpp.linker.gnu344.staticlib.823306434\=\\\#\\r\\n\\\#Wed Jul 09 21\\\:46\\\:22 CST 2025\\r\\nrebuildState\\\=false\\r\\n\r\ncom.coretek.tools.ide.project.deltaos.c.compiler.gnu344.staticlib.1570280055\=\\\#\\r\\n\\\#Wed Jul 09 21\\\:46\\\:22 CST 2025\\r\\nrebuildState\\\=false\\r\\n\r\ncom.coretek.tools.ide.project.deltaos.cpp.compiler.gnu344.staticlib.810224540\=\\\#\\r\\n\\\#Wed Jul 09 21\\\:46\\\:22 CST 2025\\r\\nrebuildState\\\=false\\r\\n\r\ncom.coretek.tools.ide.project.deltaos.c.linker.gnu344.staticlib.982476630\=\\\#\\r\\n\\\#Wed Jul 09 21\\\:46\\\:22 CST 2025\\r\\nrebuildState\\\=false\\r\\n\r\ncom.coretek.tools.ide.project.deltaos.debug.gnu344.staticlib.1509568035\=\\\#\\r\\n\\\#Wed Jul 09 21\\\:46\\\:22 CST 2025\\r\\nrebuildState\\\=false\\r\\n\r\ncom.coretek.tools.ide.project.deltaos.assembler.gnu344.staticlib.899084987\=\\\#\\r\\n\\\#Wed Jul 09 21\\\:46\\\:22 CST 2025\\r\\nrebuildState\\\=false\\r\\n\r\ncom.coretek.tools.ide.project.deltaos.cpp.linker.gnu344.staticlib.1935657634\=\\\#\\r\\n\\\#Wed Jul 09 21\\\:46\\\:22 CST 2025\\r\\nrebuildState\\\=false\\r\\n\r\ncom.coretek.tools.ide.project.deltaos.debug.staticlib.2067246375\=\\\#\\r\\n\\\#Wed Jul 09 21\\\:54\\\:39 CST 2025\\r\\nrcState\\\=0\\r\\nrebuildState\\\=false\\r\\n\r\ncom.coretek.tools.ide.project.deltaos.archiver.gnu344.staticlib.90706849\=\\\#\\r\\n\\\#Wed Jul 09 21\\\:46\\\:22 CST 2025\\r\\nrebuildState\\\=false\\r\\n\r\n
properties/MlsDemo.com.coretek.tools.ide.project.deltaos.projectType.staticlib.707524892/com.coretek.tools.ide.project.deltaos.burn.staticlib.1846534280=\#\r\n\#Wed Jul 09 21\:46\:22 CST 2025\r\ncom.coretek.tools.ide.project.deltaos.cpp.linker.gnu344.staticlib.1244055983\=\\\#\\r\\n\\\#Wed Jul 09 21\\\:46\\\:22 CST 2025\\r\\nrebuildState\\\=true\\r\\n\r\ncom.coretek.tools.ide.project.deltaos.burn.gnu344.staticlib.2018730834\=\\\#\\r\\n\\\#Wed Jul 09 21\\\:46\\\:22 CST 2025\\r\\nrebuildState\\\=true\\r\\n\r\ncom.coretek.tools.ide.project.deltaos.assembler.gnu344.staticlib.1111138139\=\\\#\\r\\n\\\#Wed Jul 09 21\\\:46\\\:22 CST 2025\\r\\nrebuildState\\\=true\\r\\n\r\ncom.coretek.tools.ide.project.deltaos.archiver.gnu344.staticlib.2071317175\=\\\#\\r\\n\\\#Wed Jul 09 21\\\:46\\\:22 CST 2025\\r\\nrebuildState\\\=true\\r\\n\r\ncom.coretek.tools.ide.project.deltaos.c.linker.gnu344.staticlib.711050029\=\\\#\\r\\n\\\#Wed Jul 09 21\\\:46\\\:22 CST 2025\\r\\nrebuildState\\\=true\\r\\n\r\ncom.coretek.tools.ide.project.deltaos.c.compiler.gnu344.staticlib.1338120349\=\\\#\\r\\n\\\#Wed Jul 09 21\\\:46\\\:22 CST 2025\\r\\nrebuildState\\\=true\\r\\n\r\ncom.coretek.tools.ide.project.deltaos.cpp.compiler.gnu344.staticlib.1096000639\=\\\#\\r\\n\\\#Wed Jul 09 21\\\:46\\\:22 CST 2025\\r\\nrebuildState\\\=true\\r\\n\r\n
properties/MlsDemo.com.coretek.tools.ide.project.deltaos.projectType.staticlib.707524892/com.coretek.tools.ide.project.deltaos.debug.staticlib.1565814588=\#\r\n\#Wed Jul 09 21\:46\:22 CST 2025\r\ncom.coretek.tools.ide.project.deltaos.c.linker.gnu344.staticlib.1057695822\=\\\#\\r\\n\\\#Wed Jul 09 21\\\:46\\\:22 CST 2025\\r\\nrebuildState\\\=false\\r\\n\r\ncom.coretek.tools.ide.project.deltaos.cpp.linker.gnu344.staticlib.2046605704\=\\\#\\r\\n\\\#Wed Jul 09 21\\\:46\\\:22 CST 2025\\r\\nrebuildState\\\=false\\r\\n\r\ncom.coretek.tools.ide.project.deltaos.cpp.compiler.gnu344.staticlib.1602403300\=\\\#\\r\\n\\\#Wed Jul 09 21\\\:46\\\:22 CST 2025\\r\\nrebuildState\\\=false\\r\\n\r\ncom.coretek.tools.ide.project.deltaos.archiver.gnu344.staticlib.1647270441\=\\\#\\r\\n\\\#Wed Jul 09 21\\\:46\\\:22 CST 2025\\r\\nrebuildState\\\=false\\r\\n\r\ncom.coretek.tools.ide.project.deltaos.assembler.gnu344.staticlib.581784300\=\\\#\\r\\n\\\#Wed Jul 09 21\\\:46\\\:22 CST 2025\\r\\nrebuildState\\\=false\\r\\n\r\ncom.coretek.tools.ide.project.deltaos.cpp.linker.gnu344.staticlib.765005849\=\\\#\\r\\n\\\#Wed Jul 09 21\\\:46\\\:22 CST 2025\\r\\nrebuildState\\\=false\\r\\n\r\ncom.coretek.tools.ide.project.deltaos.cpp.compiler.gnu344.staticlib.1310791782\=\\\#\\r\\n\\\#Wed Jul 09 21\\\:46\\\:22 CST 2025\\r\\nrebuildState\\\=false\\r\\n\r\ncom.coretek.tools.ide.project.deltaos.debug.staticlib.1565814588\=\\\#\\r\\n\\\#Wed Jul 09 21\\\:46\\\:22 CST 2025\\r\\nrcState\\\=0\\r\\nrebuildState\\\=false\\r\\n\r\ncom.coretek.tools.ide.project.deltaos.archiver.gnu344.staticlib.2016616064\=\\\#\\r\\n\\\#Wed Jul 09 21\\\:46\\\:22 CST 2025\\r\\nrebuildState\\\=false\\r\\n\r\ncom.coretek.tools.ide.project.deltaos.debug.toolChain.staticlib.1130646085\=\\\#\\r\\n\\\#Wed Jul 09 21\\\:46\\\:22 CST 2025\\r\\nrebuildState\\\=true\\r\\n\r\ncom.coretek.tools.ide.project.deltaos.c.compiler.gnu344.staticlib.961641775\=\\\#\\r\\n\\\#Wed Jul 09 21\\\:46\\\:22 CST 2025\\r\\nrebuildState\\\=false\\r\\n\r\ncom.coretek.tools.ide.project.deltaos.c.compiler.gnu344.staticlib.1256422953\=\\\#\\r\\n\\\#Wed Jul 09 21\\\:46\\\:22 CST 2025\\r\\nrebuildState\\\=false\\r\\n\r\ncom.coretek.tools.ide.project.deltaos.debug.gnu344.staticlib.1243296429\=\\\#\\r\\n\\\#Wed Jul 09 21\\\:46\\\:22 CST 2025\\r\\nrebuildState\\\=true\\r\\n\r\ncom.coretek.tools.ide.project.deltaos.assembler.gnu344.staticlib.1047540958\=\\\#\\r\\n\\\#Wed Jul 09 21\\\:46\\\:22 CST 2025\\r\\nrebuildState\\\=false\\r\\n\r\ncom.coretek.tools.ide.project.deltaos.c.linker.gnu344.staticlib.93105986\=\\\#\\r\\n\\\#Wed Jul 09 21\\\:46\\\:22 CST 2025\\r\\nrebuildState\\\=false\\r\\n\r\n
properties/MlsDemo.com.coretek.tools.ide.project.deltaos.projectType.app.707524892/com.coretek.tools.ide.project.deltaos.burn.app.1846533179.488282521.858358096=\#\r\n\#Wed Jul 02 20\:31\:18 CST 2025\r\ncom.coretek.tools.ide.project.deltaos.archiver.gnu344.app.105473398\=\\\#\\r\\n\\\#Wed Jul 02 20\\\:31\\\:18 CST 2025\\r\\nrebuildState\\\=false\\r\\n\r\ncom.coretek.tools.ide.project.deltaos.burn.app.1846533179.488282521.858358096\=\\\#\\r\\n\\\#Wed Jul 02 20\\\:31\\\:18 CST 2025\\r\\nrcState\\\=0\\r\\nrebuildState\\\=false\\r\\n\r\ncom.coretek.tools.ide.project.deltaos.archiver.gnu344.app.147467146\=\\\#\\r\\n\\\#Wed Jul 02 20\\\:31\\\:18 CST 2025\\r\\nrebuildState\\\=false\\r\\n\r\ncom.coretek.tools.ide.project.deltaos.c.linker.gnu344.app.1579578924\=\\\#\\r\\n\\\#Wed Jul 02 20\\\:31\\\:18 CST 2025\\r\\nrebuildState\\\=false\\r\\n\r\ncom.coretek.tools.ide.project.deltaos.burn.gnu344.app.458432384\=\\\#\\r\\n\\\#Wed Jul 02 20\\\:31\\\:18 CST 2025\\r\\nrebuildState\\\=false\\r\\n\r\ncom.coretek.tools.ide.project.deltaos.burn.gnu344.app.1393593072\=\\\#\\r\\n\\\#Wed Jul 02 20\\\:31\\\:18 CST 2025\\r\\nrebuildState\\\=false\\r\\n\r\ncom.coretek.tools.ide.project.deltaos.c.compiler.gnu344.app.968475641\=\\\#\\r\\n\\\#Wed Jul 02 20\\\:31\\\:18 CST 2025\\r\\nrebuildState\\\=false\\r\\n\r\ncom.coretek.tools.ide.project.deltaos.c.linker.gnu344.app.1358700844\=\\\#\\r\\n\\\#Wed Jul 02 20\\\:31\\\:18 CST 2025\\r\\nrebuildState\\\=false\\r\\n\r\ncom.coretek.tools.ide.project.deltaos.c.compiler.gnu344.app.166105170\=\\\#\\r\\n\\\#Wed Jul 02 20\\\:31\\\:18 CST 2025\\r\\nrebuildState\\\=false\\r\\n\r\ncom.coretek.tools.ide.project.deltaos.assembler.gnu344.app.1981082909\=\\\#\\r\\n\\\#Wed Jul 02 20\\\:31\\\:18 CST 2025\\r\\nrebuildState\\\=false\\r\\n\r\ncom.coretek.tools.ide.project.deltaos.cpp.compiler.gnu344.app.1638789868\=\\\#\\r\\n\\\#Wed Jul 02 20\\\:31\\\:18 CST 2025\\r\\nrebuildState\\\=false\\r\\n\r\ncom.coretek.tools.ide.project.deltaos.cpp.compiler.gnu344.app.473635344\=\\\#\\r\\n\\\#Wed Jul 02 20\\\:31\\\:18 CST 2025\\r\\nrebuildState\\\=false\\r\\n\r\ncom.coretek.tools.ide.project.deltaos.cpp.linker.gnu344.app.1229545678\=\\\#\\r\\n\\\#Wed Jul 02 20\\\:31\\\:18 CST 2025\\r\\nrebuildState\\\=false\\r\\n\r\ncom.coretek.tools.ide.project.deltaos.cpp.linker.gnu344.app.1501011598\=\\\#\\r\\n\\\#Wed Jul 02 20\\\:31\\\:18 CST 2025\\r\\nrebuildState\\\=false\\r\\n\r\ncom.coretek.tools.ide.project.deltaos.assembler.gnu344.app.419928395\=\\\#\\r\\n\\\#Wed Jul 02 20\\\:31\\\:18 CST 2025\\r\\nrebuildState\\\=false\\r\\n\r\n
properties/MlsDemo.com.coretek.tools.ide.project.deltaos.projectType.staticlib.707524892/com.coretek.tools.ide.project.deltaos.burn.staticlib.1846563180=\#\r\n\#Wed Jul 09 21\:46\:22 CST 2025\r\ncom.coretek.tools.ide.project.deltaos.cpp.linker.gnu344.staticlib.1244055983\=\\\#\\r\\n\\\#Wed Jul 09 21\\\:46\\\:22 CST 2025\\r\\nrebuildState\\\=true\\r\\n\r\ncom.coretek.tools.ide.project.deltaos.burn.gnu344.staticlib.2018730834\=\\\#\\r\\n\\\#Wed Jul 09 21\\\:46\\\:22 CST 2025\\r\\nrebuildState\\\=true\\r\\n\r\ncom.coretek.tools.ide.project.deltaos.assembler.gnu344.staticlib.1111138139\=\\\#\\r\\n\\\#Wed Jul 09 21\\\:46\\\:22 CST 2025\\r\\nrebuildState\\\=true\\r\\n\r\ncom.coretek.tools.ide.project.deltaos.archiver.gnu344.staticlib.2071317175\=\\\#\\r\\n\\\#Wed Jul 09 21\\\:46\\\:22 CST 2025\\r\\nrebuildState\\\=true\\r\\n\r\ncom.coretek.tools.ide.project.deltaos.c.linker.gnu344.staticlib.711050029\=\\\#\\r\\n\\\#Wed Jul 09 21\\\:46\\\:22 CST 2025\\r\\nrebuildState\\\=true\\r\\n\r\ncom.coretek.tools.ide.project.deltaos.c.compiler.gnu344.staticlib.1338120349\=\\\#\\r\\n\\\#Wed Jul 09 21\\\:46\\\:22 CST 2025\\r\\nrebuildState\\\=true\\r\\n\r\ncom.coretek.tools.ide.project.deltaos.cpp.compiler.gnu344.staticlib.1096000639\=\\\#\\r\\n\\\#Wed Jul 09 21\\\:46\\\:22 CST 2025\\r\\nrebuildState\\\=true\\r\\n\r\n
properties/MlsDemo.com.coretek.tools.ide.project.deltaos.projectType.staticlib.707524892/com.coretek.tools.ide.project.deltaos.debug.staticlib.953153658=\#\r\n\#Wed Jul 09 21\:46\:22 CST 2025\r\ncom.coretek.tools.ide.project.deltaos.debug.gnu344.staticlib.144094013\=\\\#\\r\\n\\\#Wed Jul 09 21\\\:46\\\:22 CST 2025\\r\\nrebuildState\\\=false\\r\\n\r\ncom.coretek.tools.ide.project.deltaos.c.linker.gnu344.staticlib.1297209067\=\\\#\\r\\n\\\#Wed Jul 09 21\\\:46\\\:22 CST 2025\\r\\nrebuildState\\\=false\\r\\n\r\ncom.coretek.tools.ide.project.deltaos.c.compiler.gnu344.staticlib.197617787\=\\\#\\r\\n\\\#Wed Jul 09 21\\\:46\\\:22 CST 2025\\r\\nrebuildState\\\=false\\r\\n\r\ncom.coretek.tools.ide.project.deltaos.cpp.compiler.gnu344.staticlib.526220227\=\\\#\\r\\n\\\#Wed Jul 09 21\\\:46\\\:22 CST 2025\\r\\nrebuildState\\\=false\\r\\n\r\ncom.coretek.tools.ide.project.deltaos.archiver.gnu344.staticlib.1671994806\=\\\#\\r\\n\\\#Wed Jul 09 21\\\:46\\\:22 CST 2025\\r\\nrebuildState\\\=false\\r\\n\r\ncom.coretek.tools.ide.project.deltaos.c.linker.gnu344.staticlib.1922312082\=\\\#\\r\\n\\\#Wed Jul 09 21\\\:46\\\:22 CST 2025\\r\\nrebuildState\\\=false\\r\\n\r\ncom.coretek.tools.ide.project.deltaos.cpp.linker.gnu344.staticlib.1074193679\=\\\#\\r\\n\\\#Wed Jul 09 21\\\:46\\\:22 CST 2025\\r\\nrebuildState\\\=false\\r\\n\r\ncom.coretek.tools.ide.project.deltaos.c.compiler.gnu344.staticlib.763082674\=\\\#\\r\\n\\\#Wed Jul 09 21\\\:46\\\:22 CST 2025\\r\\nrebuildState\\\=false\\r\\n\r\ncom.coretek.tools.ide.project.deltaos.archiver.gnu344.staticlib.737366817\=\\\#\\r\\n\\\#Wed Jul 09 21\\\:46\\\:22 CST 2025\\r\\nrebuildState\\\=false\\r\\n\r\ncom.coretek.tools.ide.project.deltaos.assembler.gnu344.staticlib.1747029917\=\\\#\\r\\n\\\#Wed Jul 09 21\\\:46\\\:22 CST 2025\\r\\nrebuildState\\\=false\\r\\n\r\ncom.coretek.tools.ide.project.deltaos.cpp.linker.gnu344.staticlib.1789762462\=\\\#\\r\\n\\\#Wed Jul 09 21\\\:46\\\:22 CST 2025\\r\\nrebuildState\\\=false\\r\\n\r\ncom.coretek.tools.ide.project.deltaos.assembler.gnu344.staticlib.1335460847\=\\\#\\r\\n\\\#Wed Jul 09 21\\\:46\\\:22 CST 2025\\r\\nrebuildState\\\=false\\r\\n\r\ncom.coretek.tools.ide.project.deltaos.debug.toolChain.staticlib.497647691\=\\\#\\r\\n\\\#Wed Jul 09 21\\\:46\\\:22 CST 2025\\r\\nrebuildState\\\=true\\r\\n\r\ncom.coretek.tools.ide.project.deltaos.debug.staticlib.953153658\=\\\#\\r\\n\\\#Wed Jul 09 21\\\:46\\\:22 CST 2025\\r\\nrcState\\\=-1\\r\\nrebuildState\\\=false\\r\\n\r\ncom.coretek.tools.ide.project.deltaos.cpp.compiler.gnu344.staticlib.973831972\=\\\#\\r\\n\\\#Wed Jul 09 21\\\:46\\\:22 CST 2025\\r\\nrebuildState\\\=false\\r\\n\r\n
properties/MlsDemo.com.coretek.tools.ide.project.deltaos.projectType.app.707524892/com.coretek.tools.ide.project.deltaos.burn.app.1846533179.488282521.1825317274=\#\r\n\#Wed Jul 02 20\:33\:50 CST 2025\r\ncom.coretek.tools.ide.project.deltaos.archiver.gnu344.app.1872521131\=\\\#\\r\\n\\\#Wed Jul 02 20\\\:33\\\:50 CST 2025\\r\\nrebuildState\\\=false\\r\\n\r\ncom.coretek.tools.ide.project.deltaos.c.compiler.gnu344.app.44504132\=\\\#\\r\\n\\\#Wed Jul 02 20\\\:33\\\:50 CST 2025\\r\\nrebuildState\\\=false\\r\\n\r\ncom.coretek.tools.ide.project.deltaos.burn.app.1846533179.488282521.1825317274\=\\\#\\r\\n\\\#Wed Jul 02 20\\\:33\\\:50 CST 2025\\r\\nrcState\\\=0\\r\\nrebuildState\\\=false\\r\\n\r\ncom.coretek.tools.ide.project.deltaos.c.linker.gnu344.app.1217522301\=\\\#\\r\\n\\\#Wed Jul 02 20\\\:33\\\:50 CST 2025\\r\\nrebuildState\\\=false\\r\\n\r\ncom.coretek.tools.ide.project.deltaos.archiver.gnu344.app.1817536370\=\\\#\\r\\n\\\#Wed Jul 02 20\\\:33\\\:50 CST 2025\\r\\nrebuildState\\\=false\\r\\n\r\ncom.coretek.tools.ide.project.deltaos.cpp.linker.gnu344.app.1945360476\=\\\#\\r\\n\\\#Wed Jul 02 20\\\:33\\\:50 CST 2025\\r\\nrebuildState\\\=false\\r\\n\r\ncom.coretek.tools.ide.project.deltaos.cpp.compiler.gnu344.app.1688891830\=\\\#\\r\\n\\\#Wed Jul 02 20\\\:33\\\:50 CST 2025\\r\\nrebuildState\\\=false\\r\\n\r\ncom.coretek.tools.ide.project.deltaos.c.compiler.gnu344.app.1094451130\=\\\#\\r\\n\\\#Wed Jul 02 20\\\:33\\\:50 CST 2025\\r\\nrebuildState\\\=false\\r\\n\r\ncom.coretek.tools.ide.project.deltaos.c.linker.gnu344.app.1160814807\=\\\#\\r\\n\\\#Wed Jul 02 20\\\:33\\\:50 CST 2025\\r\\nrebuildState\\\=false\\r\\n\r\ncom.coretek.tools.ide.project.deltaos.burn.gnu344.app.174208778\=\\\#\\r\\n\\\#Wed Jul 02 20\\\:33\\\:50 CST 2025\\r\\nrebuildState\\\=false\\r\\n\r\ncom.coretek.tools.ide.project.deltaos.assembler.gnu344.app.701621527\=\\\#\\r\\n\\\#Wed Jul 02 20\\\:33\\\:50 CST 2025\\r\\nrebuildState\\\=false\\r\\n\r\ncom.coretek.tools.ide.project.deltaos.assembler.gnu344.app.2073278598\=\\\#\\r\\n\\\#Wed Jul 02 20\\\:33\\\:50 CST 2025\\r\\nrebuildState\\\=false\\r\\n\r\ncom.coretek.tools.ide.project.deltaos.cpp.linker.gnu344.app.1757342059\=\\\#\\r\\n\\\#Wed Jul 02 20\\\:33\\\:50 CST 2025\\r\\nrebuildState\\\=false\\r\\n\r\ncom.coretek.tools.ide.project.deltaos.cpp.compiler.gnu344.app.1562360045\=\\\#\\r\\n\\\#Wed Jul 02 20\\\:33\\\:50 CST 2025\\r\\nrebuildState\\\=false\\r\\n\r\ncom.coretek.tools.ide.project.deltaos.burn.gnu344.app.17217644\=\\\#\\r\\n\\\#Wed Jul 02 20\\\:33\\\:50 CST 2025\\r\\nrebuildState\\\=false\\r\\n\r\n
properties/MlsDemo.com.coretek.tools.ide.project.deltaos.projectType.staticlib.707524892/com.coretek.tools.ide.project.deltaos.burn.staticlib.1846533180=\#\r\n\#Wed Jul 09 21\:55\:42 CST 2025\r\ncom.coretek.tools.ide.project.deltaos.cpp.linker.gnu344.staticlib.1244055983\=\\\#\\r\\n\\\#Wed Jul 09 21\\\:55\\\:42 CST 2025\\r\\nrebuildState\\\=true\\r\\n\r\ncom.coretek.tools.ide.project.deltaos.burn.gnu344.staticlib.2018730834\=\\\#\\r\\n\\\#Wed Jul 09 21\\\:55\\\:42 CST 2025\\r\\nrebuildState\\\=true\\r\\n\r\ncom.coretek.tools.ide.project.deltaos.assembler.gnu344.staticlib.1111138139\=\\\#\\r\\n\\\#Wed Jul 09 21\\\:55\\\:42 CST 2025\\r\\nrebuildState\\\=true\\r\\n\r\ncom.coretek.tools.ide.project.deltaos.archiver.gnu344.staticlib.2071317175\=\\\#\\r\\n\\\#Wed Jul 09 21\\\:55\\\:42 CST 2025\\r\\nrebuildState\\\=true\\r\\n\r\ncom.coretek.tools.ide.project.deltaos.c.linker.gnu344.staticlib.711050029\=\\\#\\r\\n\\\#Wed Jul 09 21\\\:55\\\:42 CST 2025\\r\\nrebuildState\\\=true\\r\\n\r\ncom.coretek.tools.ide.project.deltaos.cpp.compiler.gnu344.staticlib.1096000639\=\\\#\\r\\n\\\#Wed Jul 09 21\\\:55\\\:42 CST 2025\\r\\nrebuildState\\\=true\\r\\n\r\ncom.coretek.tools.ide.project.deltaos.c.compiler.gnu344.staticlib.1338120349\=\\\#\\r\\n\\\#Wed Jul 09 21\\\:55\\\:42 CST 2025\\r\\nrebuildState\\\=true\\r\\n\r\n
properties/MlsDemo.com.coretek.tools.ide.project.deltaos.projectType.app.707524892/com.coretek.tools.ide.project.deltaos.burn.app.1846533179.488282521.176447576=\#\r\n\#Wed Jul 02 20\:58\:58 CST 2025\r\ncom.coretek.tools.ide.project.deltaos.burn.gnu344.app.1260548256\=\\\#\\r\\n\\\#Wed Jul 02 20\\\:58\\\:58 CST 2025\\r\\nrebuildState\\\=false\\r\\n\r\ncom.coretek.tools.ide.project.deltaos.c.linker.gnu344.app.2125914489\=\\\#\\r\\n\\\#Wed Jul 02 20\\\:58\\\:58 CST 2025\\r\\nrebuildState\\\=false\\r\\n\r\ncom.coretek.tools.ide.project.deltaos.c.compiler.gnu344.app.497125709\=\\\#\\r\\n\\\#Wed Jul 02 20\\\:58\\\:58 CST 2025\\r\\nrebuildState\\\=false\\r\\n\r\ncom.coretek.tools.ide.project.deltaos.burn.app.1846533179.488282521.176447576\=\\\#\\r\\n\\\#Wed Jul 02 20\\\:58\\\:58 CST 2025\\r\\nrcState\\\=0\\r\\nrebuildState\\\=false\\r\\n\r\ncom.coretek.tools.ide.project.deltaos.archiver.gnu344.app.689739221\=\\\#\\r\\n\\\#Wed Jul 02 20\\\:58\\\:58 CST 2025\\r\\nrebuildState\\\=false\\r\\n\r\ncom.coretek.tools.ide.project.deltaos.c.compiler.gnu344.app.720337095\=\\\#\\r\\n\\\#Wed Jul 02 20\\\:58\\\:58 CST 2025\\r\\nrebuildState\\\=false\\r\\n\r\ncom.coretek.tools.ide.project.deltaos.archiver.gnu344.app.1454765905\=\\\#\\r\\n\\\#Wed Jul 02 20\\\:58\\\:58 CST 2025\\r\\nrebuildState\\\=false\\r\\n\r\ncom.coretek.tools.ide.project.deltaos.cpp.linker.gnu344.app.166868229\=\\\#\\r\\n\\\#Wed Jul 02 20\\\:58\\\:58 CST 2025\\r\\nrebuildState\\\=false\\r\\n\r\ncom.coretek.tools.ide.project.deltaos.assembler.gnu344.app.413190602\=\\\#\\r\\n\\\#Wed Jul 02 20\\\:58\\\:58 CST 2025\\r\\nrebuildState\\\=false\\r\\n\r\ncom.coretek.tools.ide.project.deltaos.cpp.compiler.gnu344.app.1644415379\=\\\#\\r\\n\\\#Wed Jul 02 20\\\:58\\\:58 CST 2025\\r\\nrebuildState\\\=false\\r\\n\r\ncom.coretek.tools.ide.project.deltaos.c.linker.gnu344.app.1655290163\=\\\#\\r\\n\\\#Wed Jul 02 20\\\:58\\\:58 CST 2025\\r\\nrebuildState\\\=false\\r\\n\r\ncom.coretek.tools.ide.project.deltaos.cpp.linker.gnu344.app.2003306335\=\\\#\\r\\n\\\#Wed Jul 02 20\\\:58\\\:58 CST 2025\\r\\nrebuildState\\\=false\\r\\n\r\ncom.coretek.tools.ide.project.deltaos.burn.gnu344.app.1329402119\=\\\#\\r\\n\\\#Wed Jul 02 20\\\:58\\\:58 CST 2025\\r\\nrebuildState\\\=false\\r\\n\r\ncom.coretek.tools.ide.project.deltaos.assembler.gnu344.app.1872006252\=\\\#\\r\\n\\\#Wed Jul 02 20\\\:58\\\:58 CST 2025\\r\\nrebuildState\\\=false\\r\\n\r\ncom.coretek.tools.ide.project.deltaos.cpp.compiler.gnu344.app.70645521\=\\\#\\r\\n\\\#Wed Jul 02 20\\\:58\\\:58 CST 2025\\r\\nrebuildState\\\=false\\r\\n\r\n
properties/MlsDemo.com.coretek.tools.ide.project.deltaos.projectType.app.707524892/com.coretek.tools.ide.project.deltaos.burn.app.1846533179.488282521.319793206=\#\r\n\#Wed Jul 09 21\:46\:22 CST 2025\r\ncom.coretek.tools.ide.project.deltaos.c.compiler.gnu344.app.689248780\=\\\#\\r\\n\\\#Wed Jul 09 21\\\:46\\\:22 CST 2025\\r\\nrebuildState\\\=false\\r\\n\r\ncom.coretek.tools.ide.project.deltaos.burn.gnu344.app.1234533284\=\\\#\\r\\n\\\#Wed Jul 09 21\\\:46\\\:22 CST 2025\\r\\nrebuildState\\\=false\\r\\n\r\ncom.coretek.tools.ide.project.deltaos.assembler.gnu344.app.145851835\=\\\#\\r\\n\\\#Wed Jul 09 21\\\:46\\\:22 CST 2025\\r\\nrebuildState\\\=false\\r\\n\r\ncom.coretek.tools.ide.project.deltaos.archiver.gnu344.app.2014196910\=\\\#\\r\\n\\\#Wed Jul 09 21\\\:46\\\:22 CST 2025\\r\\nrebuildState\\\=false\\r\\n\r\ncom.coretek.tools.ide.project.deltaos.cpp.compiler.gnu344.app.1526319375\=\\\#\\r\\n\\\#Wed Jul 09 21\\\:46\\\:22 CST 2025\\r\\nrebuildState\\\=false\\r\\n\r\ncom.coretek.tools.ide.project.deltaos.burn.gnu344.app.1980061768\=\\\#\\r\\n\\\#Wed Jul 09 21\\\:46\\\:22 CST 2025\\r\\nrebuildState\\\=false\\r\\n\r\ncom.coretek.tools.ide.project.deltaos.archiver.gnu344.app.667334173\=\\\#\\r\\n\\\#Wed Jul 09 21\\\:46\\\:22 CST 2025\\r\\nrebuildState\\\=false\\r\\n\r\ncom.coretek.tools.ide.project.deltaos.c.linker.gnu344.app.1703000041\=\\\#\\r\\n\\\#Wed Jul 09 21\\\:46\\\:22 CST 2025\\r\\nrebuildState\\\=false\\r\\n\r\ncom.coretek.tools.ide.project.deltaos.cpp.linker.gnu344.app.2040545103\=\\\#\\r\\n\\\#Wed Jul 09 21\\\:46\\\:22 CST 2025\\r\\nrebuildState\\\=false\\r\\n\r\ncom.coretek.tools.ide.project.deltaos.c.linker.gnu344.app.630562204\=\\\#\\r\\n\\\#Wed Jul 09 21\\\:46\\\:22 CST 2025\\r\\nrebuildState\\\=false\\r\\n\r\ncom.coretek.tools.ide.project.deltaos.c.compiler.gnu344.app.560531520\=\\\#\\r\\n\\\#Wed Jul 09 21\\\:46\\\:22 CST 2025\\r\\nrebuildState\\\=false\\r\\n\r\ncom.coretek.tools.ide.project.deltaos.cpp.compiler.gnu344.app.252578017\=\\\#\\r\\n\\\#Wed Jul 09 21\\\:46\\\:22 CST 2025\\r\\nrebuildState\\\=false\\r\\n\r\ncom.coretek.tools.ide.project.deltaos.burn.app.1846533179.488282521.319793206\=\\\#\\r\\n\\\#Wed Jul 09 21\\\:46\\\:22 CST 2025\\r\\nrcState\\\=0\\r\\nrebuildState\\\=false\\r\\n\r\ncom.coretek.tools.ide.project.deltaos.cpp.linker.gnu344.app.1257820532\=\\\#\\r\\n\\\#Wed Jul 09 21\\\:46\\\:22 CST 2025\\r\\nrebuildState\\\=false\\r\\n\r\ncom.coretek.tools.ide.project.deltaos.assembler.gnu344.app.457377417\=\\\#\\r\\n\\\#Wed Jul 09 21\\\:46\\\:22 CST 2025\\r\\nrebuildState\\\=false\\r\\n\r\n
