BEGIN	{
	Count = 0;
	Flag = 0;
	ModuleName = ""
	TheModuleVersion = ""
	TheModuleType = ""
	TheModuleUnload = ""
	FS="[[:space:]+]"
}

/LIBRARY/{ 
	if( Flag == 0 )
	{
		i = NF
		while( i > 1 )
		{
			if( $i != "" )
			{
				ModuleName = $i
				break
			}
			i--
		}
		
		print "unsigned int " ModuleName "_symbol_table[]= {"
	}
}

/VERSION/{ 
	if( Flag == 0 )
	{
		i = NF
		while( i > 1 )
		{
			if( $i != "" )
			{
				TheModuleVersion = $i
				break
			}
			i--
		}
		 
	}
}

/TYPE/{ 
	if( Flag == 0 )
	{
		i = NF
		while( i > 1 )
		{
			if( $i != "" )
			{
				TheModuleType = $i
				break
			}
			i--
		}
		 
	}
}

{
	if(Flag == 1)
	{
		i = 1
		while( i <= NF )
		{
			if( $i != "" )
			{
			   
				if( Count != 0 )
				{
					printf ",\n"
				}
				printf "	(unsigned int )%s", $i
				
				Count++
				
				break
			}
			i++
		}
		
		
	}
}

/EXPORTS/	{ Flag = 1 }

END	{
	print "\n};\n\n"
	
}
