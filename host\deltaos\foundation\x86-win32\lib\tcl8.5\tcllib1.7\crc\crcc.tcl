# crcc.tcl - Copyright (C) 2002 <PERSON> <<EMAIL>>
#
# Place holder for building a critcl C module for this tcllib module.
#
# -------------------------------------------------------------------------
# See the file "license.terms" for information on usage and redistribution
# of this file, and for a DISCLAIMER OF ALL WARRANTIES.
# -------------------------------------------------------------------------
# $Id$

package require critcl 

namespace eval ::crc {
    variable rcsid {$Id$}
}

package provide crcc 1.0.0