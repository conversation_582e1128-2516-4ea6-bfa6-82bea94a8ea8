#!/bin/sh
## Usage: wdm [-h] [options] [commands]
## Launch Workbench in Debug Mode, optionally launching/attaching a debuggee.
##
## Options:
##   -data workspacepath             Choose a non-default workspace
##
## Commands:
##   -attach                         Attach to running debuggee:
##      -t targetname                Name of target in the WR Registry
##      [-s systemname]              Optional System name on the target
##      [-m Task|System]             Optional Target Mode to switch
##      [-ot Process|KernelTask|RTP] Type of OS Object to attach to
##      [-o id]                      Name of OS Object to attach to
##      [-sh true]                   Optional: Attach a Debug Command Shell
##
##  -launchConfig / -lc              Launch an existing, named config:
##      -n configname                Name of the launch configuration
##
##  -launch                          Launch a program (on Linux or VxWorks):
##      -t targetname                Name of target in the WR Registry
##      -ot Process|KernelTask|RTP   Kind of Launch (type of object to create).
##      [-f host-exepath]            Path to ELF file on host
##      [-tp target-exepath]         Path to ELF file on target
##      [-e entrypoint]              Entrypoint in the image to break on
##      [-sh true]                   Optional: Attach a Debug Command Shell
##
## Examples:
## wdm
## wdm -attach -t WRISS_MPC8260 -sh true
## wdm -lc -n "WRISS_MPC8260 - MPC8260"
## wdm -launch -t vxsim0 -ot KernelTask -f myDKM.out -e progStart -sh true
## wdm -launch -t mylinux -ot Process -tp /bin/myapp -sh true
##
case x$1 in
  x-h*) grep '^##' $0 | cut -c4-
        exit 0
        ;;
esac

# Determine host type
if [ "$WIND_HOST_TYPE" = "" ]; then
  uname=`uname`
  case $uname in
    Linux) WIND_HOST_TYPE=x86-linux2 ;;
    SunOS | Solaris) WIND_HOST_TYPE=sun4-solaris2 ;;
    win*) WIND_HOST_TYPE=x86-win32 ;;
    *) echo "Unsupported system type: $uname"
       exit 1
       ;;
  esac
fi

# Determine EXE name
case $WIND_HOST_TYPE in
  x86-linux2)   
                WRENV="wrenv.linux"
                WRWB="/wrwb-x86-linux2.gtk"
                WSLOC="$HOME/WindRiver"
                ;;
  sun4-solaris2)
                WRENV="wrenv.solaris"
                WRWB="/wrwb-sun4-solaris2.gtk"
                WSLOC="$HOME/WindRiver"
                ;;
  x86-win32)
                WRENV="wrenv.exe"
                WRWB="\\wrwb-x86-win32.exe"
                WSLOC="C:/WindRiver"
                ;;
  *)            echo "Unsupported WIND_HOST_TYPE: $WIND_HOST_TYPE"
                exit 1
                ;;
esac

# Determine EXE location
if [ "$WIND_TOOLS" = "" ]; then
  curdir=`pwd`
  cd `dirname $0`
  cd `/bin/pwd`/../..
  WIND_TOOLS=`pwd`
  cd "$curdir"
fi
if [ "$WIND_WRWB_PATH" = "" ]; then
  WIND_WRWB_PATH="$WIND_TOOLS/wrwb/platform/$WIND_HOST_TYPE/eclipse"
fi
WDM_EXE="${WIND_WRWB_PATH}${WRWB}"
if [ ! -x "${WDM_EXE}" ]; then
  echo "Error: Cannot execute $WDM_EXE"
  exit 1
fi

# Determine default settings and launch
if [ "$WIND_WDM_DEFAULT_WORKSPACE" = "" ]; then
  WIND_WDM_DEFAULT_WORKSPACE="$WSLOC/wrwb-debugmode-workspace"
fi
USER_VMARGS=`echo $* | grep -- -vmargs`
if [ "$USER_VMARGS" != "" ]; then
  VMARGS_ARG=""
else
  VMARGS_ARG="-vmargs"
fi
echo "Launching Wind River Workbench Debug Mode..."
${WDM_EXE} \
  -mode debug \
  "$@" \
  $VMARGS_ARG '-DWIND_INTRO=$(WIND_TOOLS)/gettingStarted/debugmode/debugmode.properties' \
  &

